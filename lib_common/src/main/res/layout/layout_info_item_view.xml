<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:paddingStart="20dp"
        android:paddingEnd="20dp">
    <TextView
            android:id="@+id/item_name"
            tools:text="哈哈哈哈哈哈哈哈"
            android:singleLine="true"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/hint_status"
            app:layout_constraintHorizontal_weight="1"
            android:layout_width="0dp"
            android:textSize="15sp"
            android:textColor="@color/color_0D0D1A"
            android:layout_height="wrap_content"/>

    <TextView
            android:id="@+id/hint_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:textColor="@color/color_9999A3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/item_name"
            app:layout_constraintEnd_toStartOf="@id/next_icon"
            tools:text="已启用"

            />

    <ImageView
            android:id="@+id/next_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/hint_status"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/ic_icon_gray_arrow_right"
            tools:src="@drawable/ic_icon_gray_arrow_right"/>
</androidx.constraintlayout.widget.ConstraintLayout>