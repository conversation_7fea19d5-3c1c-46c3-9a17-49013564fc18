<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp">

    <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/color_0D0D1A"
            android:textSize="15sp"
            android:singleLine="true"
            android:ellipsize="end"
            app:layout_constraintBottom_toTopOf="@id/description"
            app:layout_constraintEnd_toStartOf="@id/shift"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题" />

    <TextView
            android:id="@+id/description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="#B1B1B8"
            android:singleLine="true"
            android:ellipsize="end"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/title"
            app:layout_constraintStart_toStartOf="@id/title"
            app:layout_constraintTop_toBottomOf="@id/title"
            tools:text="描述描述描述描述描述描述描述描述描述" />

    <Switch
            android:id="@+id/shift"
            android:layout_width="48dp"
            android:layout_height="24dp"
            android:checked="true"
            android:thumb="@drawable/switch_thumb"
            android:track="@drawable/switch_track"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="10dp"
            app:layout_constraintStart_toEndOf="@id/title"/>
</androidx.constraintlayout.widget.ConstraintLayout>