<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="CommonAppTheme" parent="Theme.AppCompat.Light.NoActionBar">        <!-- Customize your theme here. -->
        <item name="android:background">@color/color_transparent</item>
    </style>    <!--全屏加透明-->

    <style name="CommonFullScreenTheme" parent="CommonAppTheme">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>    <!--透明，有任务栏电量时间等-->

    <!--默认样式 dialog-->
    <style name="DialogNormal" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled" tools:ignore="NewApi">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
    </style>

    <style name="CommonNoTitleTranslucentTheme" parent="CommonAppTheme">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/color_transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="MyProgressProgressBar" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">false</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:backgroundDimAmount">0</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="SpinKitView">
        <item name="android:indeterminateOnly">true</item>
    </style>

    <style name="BossGallery" parent="Matisse.Hi">
        <!--<item name="colorPrimary">#000</item>-->
        <!--<item name="colorPrimaryDark">#000</item>-->
        <!--<item name="album.element.color">@android:color/black</item>-->

        <!--<item name="item.checkCircle.backgroundColor">-->
        <!--@color/app_blue-->
        <!--</item>-->
        <!--<item name="page.bg">@android:color/black</item>-->
        <!--<item name="bottomToolbar.bg">@android:color/white</item>-->
        <!--<item name="bottomToolbar.apply.textColor">@color/app_white</item>-->
        <!--<item name="preview.bottomToolbar.apply.textColor">@color/app_white</item>-->
        <!--<item name="android:windowAnimationStyle">@style/bottom_to_top_anim</item>-->
        <item name="bottomToolbar_apply_textColor">@color/hi_bottom_toolbar_apply</item>
    </style>

    <style name="BossGalleryNew" parent="Matisse.Hi">
        <item name="colorPrimary">@color/color_primary</item>
        <item name="item_checkCircle_backgroundColor">@color/color_primary</item>
        <item name="matisse_preview_bottomToolbar_check_selected_color">@color/color_primary</item>
        <item name="bottomToolbar_apply_textColor">@color/common_text_color_primary_disabled</item>
        <item name="preview_bottomToolbar_hover">@drawable/bg_matisse_preview_selected_item</item>
        <item name="bottomToolbar_apply_background">@drawable/bg_corner_4_color_primary</item>
    </style>

    <style name="BossHi.UCrop" parent="Matisse.UCrop">
        <item name="ucrop_DimmedLayerColor">@color/ucrop_color_default_dimmed</item>
        <item name="ucrop_CropFrameColor">@color/ucrop_color_default_crop_frame</item>
        <item name="ucrop_CropGridColor">@color/ucrop_color_default_crop_grid</item>
        <item name="ucrop_ToolbarColor">@color/color_3F51B5</item>
        <item name="ucrop_StatusBarColor">@color/color_3F51B5</item>
        <item name="ucrop_ActiveWidgetColor">@color/color_3F51B5</item>
        <item name="ucrop_ToolbarWidgetColor">@color/ucrop_color_toolbar_widget</item>
        <item name="ucrop_LogoColor">@color/ucrop_color_default_logo</item>
        <item name="ucrop_RootViewBackgroundColor">@color/ucrop_color_crop_background</item>
    </style>

    <style name="BossHi.UCropNew" parent="Matisse.UCrop">
        <item name="ucrop_DimmedLayerColor">@color/ucrop_color_default_dimmed</item>
        <item name="ucrop_CropFrameColor">@color/ucrop_color_default_crop_frame</item>
        <item name="ucrop_CropGridColor">@color/ucrop_color_default_crop_grid</item>
        <item name="ucrop_ToolbarColor">@color/color_primary</item>
        <item name="ucrop_StatusBarColor">@color/color_primary</item>
        <item name="ucrop_ActiveWidgetColor">@color/color_primary</item>
        <item name="ucrop_ToolbarWidgetColor">@color/ucrop_color_toolbar_widget</item>
        <item name="ucrop_LogoColor">@color/ucrop_color_default_logo</item>
        <item name="ucrop_RootViewBackgroundColor">@color/ucrop_color_crop_background</item>
    </style>

    <style name="custom_dialog2" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!-- Dialog的windowFrame框为无 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否漂现在activity上 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 是否半透明 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <!-- 去除黑色边框的关键设置项 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 屏幕背景是否变暗 -->
        <item name="android:backgroundDimAmount">0.3</item>
    </style>

    <style name="common_dialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <!-- 背景颜色默认半透明-->
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <declare-styleable name="RectangleButton">
        <attr name="btnRadius" format="dimension"></attr>
        <attr name="btnText" format="string"></attr>
        <attr name="btnTextSize" format="dimension"></attr>
        <attr name="gradientBtnColorStart" format="color"></attr>
        <attr name="gradientBtnColorEnd" format="color"></attr>
        <attr name="gradientTextColorStart" format="color"></attr>
        <attr name="gradientTextColorEnd" format="color"></attr>
    </declare-styleable>

    <declare-styleable name="ExpandSiblingIcon">
        <attr name="iconExpandedDefault" format="boolean"/>
        <attr name="iconExpandedSrc" format="reference"/>
        <attr name="iconClosedSrc" format="reference"/>
        <attr name="siblingAnimationDuration" format="integer"/>
        <attr name="siblingId" format="reference"/>
    </declare-styleable>

    <declare-styleable name="WarningInputEdit">
        <attr name="hintText" format="string"/>
        <attr name="title" format="string"/>
        <attr name="warningText" format="string"/>
    </declare-styleable>

    <declare-styleable name="InputSelectorView">
        <attr name="titleSelect" format="string"/>
        <attr name="hintTextSelect" format="string"/>
        <attr name="warningSelectText" format="string"/>
    </declare-styleable>

    <declare-styleable name="MaxHeightScrollView">
        <attr name="viewMaxHeight" format="dimension"/>
    </declare-styleable>
</resources>