package lib.twl.common.photoselect;

import android.Manifest;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationManager;
import android.media.ExifInterface;
import android.net.Uri;
import android.os.Build;
import android.os.Looper;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.util.Pair;
import android.webkit.MimeTypeMap;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.FragmentActivity;

import com.common.AvoidOnResult;
import com.sankuai.waimai.router.Router;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.callback.MainThread;
import com.twl.utils.GsonUtils;
import com.twl.utils.SettingBuilder;
import com.twl.utils.file.FileUtils;
import com.twl.utils.kv.KV;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.SelectionCreator;
import com.zhihu.matisse.internal.entity.CaptureStrategy;
import com.zhihu.matisse.internal.utils.PathUtils;
import com.zhihu.matisse.internal.utils.PhotoMetadataUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import lib.twl.common.R;
import lib.twl.common.app.AppThreadFactory;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.model.CommonConstants;
import lib.twl.common.permission.IPermissionDialogService;
import lib.twl.common.permission.PermissionAvoidManager;
import lib.twl.common.photoselect.bean.TempFileBean;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.HiPermissionUtil;
import lib.twl.common.util.LList;
import lib.twl.common.util.ToastUtils;
import lib.twl.common.util.image.ImageUtils;
import top.zibin.luban.Luban;

/**
 * 图片选择器。
 */
public final class PhotoSelectManager {
    
    private static final String TAG = "PhotoSelectManager";

    private static final int REQ_GALLERY = 100;

    public static final String IMAGE = "image"; //与MimeType中的name前缀保持一致

    public static final String VIDEO = "video";

    private static final String MIME_TYPE_GIF = "image/gif";

    private static final CharSequence TIME_FORMAT = "yyyy:MM:dd HH:mm:ss";

    private static boolean useNewTheme = false;

    public static void setUseNewTheme(boolean newTheme) {
        useNewTheme = newTheme;
    }

    /**
     * 跳转到拍照 app统一调用 包含聊天会话
     *
     * @param context
     * @param onCameraCallBack
     */
    public static void jumpForCameraResultWithRotate(FragmentActivity context, @NonNull OnCameraCallBack onCameraCallBack) {
        jumpForCameraResultInternal(context, onCameraCallBack, true, false);
    }

    /**
     * 跳转到拍照 app统一调用 包含聊天会话
     *
     * @param context
     * @param onCameraCallBack
     */
    public static void jumpForCameraResult(FragmentActivity context, @NonNull OnCameraCallBack onCameraCallBack) {
        jumpForCameraResultInternal(context, onCameraCallBack, false, false);
    }

    public static void jumpForCameraResult(FragmentActivity context, boolean useFrontCamera, @NonNull OnCameraCallBack onCameraCallBack) {
        jumpForCameraResultInternal(context, onCameraCallBack, false, useFrontCamera);
    }

    /**
     * 跳转到拍照 app统一调用 包含聊天会话
     *
     * @param context          上下文
     * @param onCameraCallBack 拍摄回调
     * @param autoRotate       旋转手机后拍照，图片是否支持自动旋转
     * @param userFrontCamera  是否使用前置摄像头
     */
    private static void jumpForCameraResultInternal(
            FragmentActivity context,
            @NonNull OnCameraCallBack onCameraCallBack,
            boolean autoRotate,
            boolean userFrontCamera) {
        String[] permissions = {Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO};
        //请求权限
        PermissionAvoidManager manager = new PermissionAvoidManager(context);
        manager.requestPermission(permissions, (hasPermission, shouldShowAllRequestPermissionRationale) -> {

            if (hasPermission) {
                Matisse.from(context)
                        .choose(MimeType.ofImage(), true)
                        .withCustomAnimations(R.anim.activity_camera_enter_up_glide, R.anim.activity_camera_exit_up_glide)
                        .captureStrategy(new CaptureStrategy(true, context.getPackageName() + ".fileprovider", userFrontCamera))
                        .autoRotatePic(autoRotate)
                        .captureThenEdit(new AvoidOnResult.Callback() {
                            @Override
                            public void onActivityResult(int requestCode, int resultCode, Intent data) {
                                onCameraCallBack.onActivityResult(requestCode, resultCode, data);

                                if (resultCode != FragmentActivity.RESULT_OK || data == null) { //适配，h5调用拍照，没有数据返回需要重新设置回调状态
                                    onCameraCallBack.onCameraCallback(null);
                                    return;
                                }
                                if (resultCode == FragmentActivity.RESULT_OK) {
                                    String path = Matisse.obtainSavePathResult(data);
                                    if (TextUtils.isEmpty(path)) {
                                        onCameraCallBack.onCameraCallback(null);
                                        return;
                                    }

                                    try {
                                        //写入exif信息到图片中
                                        ExifInterface exifInterface = new ExifInterface(path);
                                        //型号信息
                                        exifInterface.setAttribute(ExifInterface.TAG_MODEL, Build.MODEL);
                                        //产商信息
                                        exifInterface.setAttribute(ExifInterface.TAG_MAKE, Build.MANUFACTURER);
                                        //拍摄时间
                                        exifInterface.setAttribute(ExifInterface.TAG_DATETIME, String.valueOf(DateFormat.format(TIME_FORMAT, System.currentTimeMillis())));

                                        //获取地理位置并保存
                                        Location location = getLocation(context);
                                        if (location != null) {
                                            exifInterface.setAttribute(ExifInterface.TAG_GPS_LATITUDE_REF, location.getLatitude() + "");
                                            exifInterface.setAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF, location.getLongitude() + "");
                                        }

                                        exifInterface.saveAttributes();
                                    } catch (IOException e) {
                                        TLog.error(TAG, e.getMessage());
                                    }

                                    onCameraCallBack.onCameraCallback(new File(path));
                                }
                            }
                        });
            } else {
                showLackedPermissionTips(context, permissions);
            }
        });
    }

    /**
     * 获取地理位置.
     */
    private static Location getLocation(Context context) {
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
            LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
            List<String> providers = locationManager.getProviders(true);
            Location location = null;
            if (providers.contains(LocationManager.GPS_PROVIDER)) {
                //如果是GPS
                location = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
            }
            if (providers.contains(LocationManager.NETWORK_PROVIDER)) {
                //如果是Network
                if (location == null) {
                    location = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
                }
            }
            return location;
        }
        return null;
    }

    /**
     * 跳转到图库,裁切图片(扫一扫使用)
     */
    public static void jumpForGalleryWithCropResult(FragmentActivity context, @NonNull OnGalleryCallBack callback) {
        //请求权限
        PermissionAvoidManager manager = new PermissionAvoidManager(context);
        manager.requestPermission(HiPermissionUtil.getStoragePermissions(), (hasPermission, shouldShowAllRequestPermissionRationale) -> {
            if (hasPermission) {
                getDefaultImageConfig(context)
                        .enableCrop()
                        // .ucropThemeId(R.style.Matisse_UCrop)
                        .withAspectRatio(1, 1)
                        .withMaxResultSize(1024, 1024)
                        .forResult(REQ_GALLERY, (requestCode, resultCode, data) -> {
                            callback.onActivityResult(requestCode, resultCode, data);
                            if (resultCode != FragmentActivity.RESULT_OK || data == null) {
                                return;
                            }
                            List<String> files = Matisse.obtainPathResult(data);
                            String galleryPath = LList.getElement(files, 0);
                            callback.onGalleryListener(galleryPath == null ? null : new File(galleryPath));
                        });
            } else {
                Router.getService(IPermissionDialogService.class, CommonConstants.PERMISSION_DIALOG_SERVICE_KEY).showStorageFailedDialog(context,null, null);
            }
        });
    }


    /**
     * 聊天选择图片包含视频 gif
     *
     * @param context
     * @param callBack
     */
    public static void jumpForGalleryFromChatResult(FragmentActivity context, @NonNull OnGalleryCountCallBack callBack) {
        jumpForGalleryFromResult(context, 9, true, true, callBack);
    }

    /**
     * app普通选择图片 不包含视频 gif
     *
     * @param context
     * @param count
     * @param callBack
     */
    public static void jumpForGalleryFromResult(FragmentActivity context, int count, @NonNull OnGalleryCountCallBack callBack) {
        jumpForGalleryFromResult(context, count, false, true, callBack);
    }

    public static void jumpGalleryMoreThanPhotoForResult(FragmentActivity context, int count, boolean originalDefaultCheck, @NonNull OnGalleryCountCallBack callBack) {
        jumpForGalleryFromResult(context, count, true, originalDefaultCheck, callBack);
    }

    public static void jumpForGalleryFromResult(FragmentActivity context, int count, boolean originalDefaultCheck, @NonNull OnGalleryCountCallBack callBack) {
        jumpForGalleryFromResult(context, count, false, originalDefaultCheck, callBack);
    }


    /**
     * 聊天选择图片 app 统一使用
     *
     * @param context
     * @param count
     * @param callBack
     * @param isChat   true ；聊天使用 包含视频 gif
     */
    public static void jumpForGalleryFromResult(FragmentActivity context, int count, boolean isChat, boolean originalDefaultCheck, @NonNull OnGalleryCountCallBack callBack) {
        //请求权限
        PermissionAvoidManager manager = new PermissionAvoidManager(context);
        manager.requestPermission(HiPermissionUtil.getStoragePermissions(), (hasPermission, shouldShowAllRequestPermissionRationale) -> {
            if (hasPermission) {
                SelectionCreator selectionCreator;
                if (isChat) {
                    selectionCreator = getChatImageConfig(context);
                } else {
                    selectionCreator = getDefaultImageConfig(context, originalDefaultCheck);
                }
                selectionCreator.maxSelectable(count)
                        .forResult(REQ_GALLERY, new AvoidOnResult.Callback() {
                            @Override
                            public void onActivityResult(int requestCode, int resultCode, Intent data) {
                                callBack.onActivityResult(requestCode, resultCode, data);

                                if (resultCode != FragmentActivity.RESULT_OK || data == null) { //适配，h5调用选择图片，没有数据返回需要重新设置回调状态
                                    callBack.onGalleryListener(new ArrayList<>(), false);
                                    return;
                                }
                                boolean originalEnable = Matisse.obtainOriginalState(data);
                                //返回原图集合
                                List<Uri> fileUris = Matisse.obtainResult(data);
                                if (LList.isEmpty(fileUris)) {
                                    callBack.onGalleryListener(new ArrayList<>(), originalEnable);
                                    return;
                                }
                                AppThreadFactory.POOL.execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        List<File> compressList = new ArrayList<>();
                                        // 如果是选择原图，且图片包含旋转角度时，需要主动生成旋转后的图片返回，临时图片保存到集合，使用完毕后删除
                                        List<File> rotateFileList = new ArrayList<>();
                                        try {
                                            for (Uri uri : fileUris) {
                                                if (uri == null) {
                                                    continue;
                                                }
                                                String path = PathUtils.getPath(context, uri);
                                                if (isChat) {
                                                    if (checkType(context, uri, MimeType.ofVideo())) {
                                                        compressList.add(new File(path));
                                                    } else {
                                                        String mimeType = PathUtils.getMimeType(context, uri);
                                                        if (MIME_TYPE_GIF.equalsIgnoreCase(mimeType)) {
                                                            compressList.add(new File(path));
                                                        } else {
                                                            if (originalEnable) {
                                                                addOriginalFile(compressList, path, rotateFileList);
                                                            } else {
                                                                compressList.add(Luban.with(BaseApplication.getApplication()).get(path));
                                                            }
                                                        }
                                                    }
                                                } else {
                                                    if (originalEnable) {
                                                        addOriginalFile(compressList, path, rotateFileList);
                                                    } else {
                                                        compressList.add(Luban.with(BaseApplication.getApplication()).get(path));
                                                    }
                                                }
                                            }
                                            //返回业务处
                                            AppThreadFactory.getMainHandler().post(new Runnable() {
                                                @Override
                                                public void run() {
                                                    callBack.onGalleryListener(compressList, originalEnable);
                                                    ExecutorFactory.execLocalTask(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            // 保存需要删除的临时图片
                                                            saveTempFilePath(rotateFileList, context);
                                                        }
                                                    });
                                                }
                                            });

                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            //返回业务处失败
                                            AppThreadFactory.getMainHandler().post(new Runnable() {
                                                @Override
                                                public void run() {
                                                    callBack.onGalleryListener(new ArrayList<>(), originalEnable);
                                                    ExecutorFactory.execLocalTask(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            // 保存需要删除的临时图片
                                                            saveTempFilePath(rotateFileList, context);
                                                        }
                                                    });
                                                }
                                            });
                                        }
                                    }

                                    /**
                                     * 添加原图到集合
                                     * 如果原图有旋转角度，则将原图默认旋转后返回
                                     *
                                     * @param compressList
                                     * @param path
                                     */
                                    private void addOriginalFile(List<File> compressList, String path, List<File> rotateFileList) {
                                        if (compressList == null || TextUtils.isEmpty(path)) {
                                            return;
                                        }

                                        try {
                                            ExifInterface exifInterface = new ExifInterface(path);
                                            int orientation = exifInterface.getAttributeInt(ExifInterface.TAG_ORIENTATION, 0);
                                            // 此处返回的可能是处理旋转后的临时图片文件，需要在使用结束后删除
                                            File fileRotate = ImageUtils.rotatePicWhenHasAngle(orientation, path, context);
                                            if (fileRotate == null) {
                                                compressList.add(new File(path));
                                                return;
                                            }
                                            rotateFileList.add(fileRotate);
                                            // 注意，此旋转后的临时图片缓存到sdcard下，需要清理，避免空间占用
                                            compressList.add(fileRotate);
                                        } catch (IOException e) {
                                            TLog.info(TAG, e.getMessage());
                                            compressList.add(new File(path));
                                        }
                                    }
                                });

                            }
                        });
            } else {
                Router.getService(IPermissionDialogService.class, CommonConstants.PERMISSION_DIALOG_SERVICE_KEY).showStorageFailedDialog(context,null, null);
            }

        });


    }

    /**
     * 根据阈值配置和是否选择原图, 进行策略性压缩
     *
     * @param context
     * @param count
     * @param originalDefaultCheck
     * @param threshold                 文件压缩阈值大小
     * @param callBack
     */
    public static void jumpForGalleryFromResultWithZipStrategy(FragmentActivity context, int count, boolean originalDefaultCheck, long threshold, @NonNull OnGalleryCountCallBack callBack) {
        //请求权限
        PermissionAvoidManager manager = new PermissionAvoidManager(context);
        manager.requestPermission(HiPermissionUtil.getStoragePermissions(), (hasPermission, shouldShowAllRequestPermissionRationale) -> {
            if (hasPermission) {
                SelectionCreator selectionCreator;
                selectionCreator = getDefaultImageConfig(context, originalDefaultCheck);
                selectionCreator.maxSelectable(count)
                        .forResult(REQ_GALLERY, new AvoidOnResult.Callback() {
                            @Override
                            public void onActivityResult(int requestCode, int resultCode, Intent data) {
                                callBack.onActivityResult(requestCode, resultCode, data);

                                if (resultCode != FragmentActivity.RESULT_OK || data == null) { //适配，h5调用选择图片，没有数据返回需要重新设置回调状态
                                    callBack.onGalleryListener(new ArrayList<>(), false);
                                    return;
                                }
                                boolean originalEnable = Matisse.obtainOriginalState(data);
                                //返回原图集合
                                List<Uri> fileUris = Matisse.obtainResult(data);
                                if (LList.isEmpty(fileUris)) {
                                    callBack.onGalleryListener(new ArrayList<>(), originalEnable);
                                    return;
                                }
                                AppThreadFactory.POOL.execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        List<File> compressList = new ArrayList<>();
                                        // 如果是选择原图，且图片包含旋转角度时，需要主动生成旋转后的图片返回，临时图片保存到集合，使用完毕后删除
                                        List<File> rotateFileList = new ArrayList<>();
                                        try {
                                            for (Uri uri : fileUris) {
                                                if (uri == null) {
                                                    continue;
                                                }
                                                String path = PathUtils.getPath(context, uri);
                                                if (originalEnable) {
                                                    addOriginalFile(compressList, path, rotateFileList);
                                                } else {
                                                    // 文件大于阈值才进行压缩，否则返回原图
                                                    File fileSelected = new File(path);
                                                    if (fileSelected.length() > threshold) {
                                                        compressList.add(Luban.with(BaseApplication.getApplication()).get(path));
                                                    } else {
                                                        addOriginalFile(compressList, path, rotateFileList);
                                                    }
                                                }
                                            }
                                            //返回业务处
                                            AppThreadFactory.getMainHandler().post(new Runnable() {
                                                @Override
                                                public void run() {
                                                    callBack.onGalleryListener(compressList, originalEnable);
                                                    ExecutorFactory.execLocalTask(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            // 保存需要删除的临时图片
                                                            saveTempFilePath(rotateFileList, context);
                                                        }
                                                    });
                                                }
                                            });

                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            //返回业务处失败
                                            AppThreadFactory.getMainHandler().post(new Runnable() {
                                                @Override
                                                public void run() {
                                                    callBack.onGalleryListener(new ArrayList<>(), originalEnable);
                                                    ExecutorFactory.execLocalTask(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            // 保存需要删除的临时图片
                                                            saveTempFilePath(rotateFileList, context);
                                                        }
                                                    });
                                                }
                                            });
                                        }
                                    }

                                    /**
                                     * 添加原图到集合
                                     * 如果原图有旋转角度，则将原图默认旋转后返回
                                     *
                                     * @param compressList
                                     * @param path
                                     */
                                    private void addOriginalFile(List<File> compressList, String path, List<File> rotateFileList) {
                                        if (compressList == null || TextUtils.isEmpty(path)) {
                                            return;
                                        }

                                        try {
                                            ExifInterface exifInterface = new ExifInterface(path);
                                            int orientation = exifInterface.getAttributeInt(ExifInterface.TAG_ORIENTATION, 0);
                                            // 此处返回的可能是处理旋转后的临时图片文件，需要在使用结束后删除
                                            File fileRotate = ImageUtils.rotatePicWhenHasAngle(orientation, path, context);
                                            if (fileRotate == null) {
                                                compressList.add(new File(path));
                                                return;
                                            }
                                            rotateFileList.add(fileRotate);
                                            // 注意，此旋转后的临时图片缓存到sdcard下，需要清理，避免空间占用
                                            compressList.add(fileRotate);
                                        } catch (IOException e) {
                                            TLog.info(TAG, e.getMessage());
                                            compressList.add(new File(path));
                                        }
                                    }
                                });

                            }
                        });
            } else {
                Router.getService(IPermissionDialogService.class, CommonConstants.PERMISSION_DIALOG_SERVICE_KEY).showStorageFailedDialog(context,null, null);
            }
        });
    }

    private static void saveTempFilePath(List<File> rotateFileList, FragmentActivity context) {
        if (!LList.isEmpty(rotateFileList)) {
            TempFileBean tempFileBean = null;
            String path = KV.getKV(context.getPackageName()).getString(FileUtils.KEY_TEMP_FILE_PATH, "");
            if (!TextUtils.isEmpty(path)) {
                tempFileBean = GsonUtils.getGson().fromJson(path, TempFileBean.class);
                for (File file : rotateFileList) {
                    tempFileBean.getFilePathList().add(file.getAbsolutePath());
                }
            } else {
                tempFileBean = new TempFileBean();
                List<String> pathList = new ArrayList<>();
                for (File file : rotateFileList) {
                    pathList.add(file.getAbsolutePath());
                }
                tempFileBean.setFilePathList(pathList);
            }
            KV.getKV(context.getPackageName()).putString(FileUtils.KEY_TEMP_FILE_PATH, GsonUtils.getGson().toJson(tempFileBean));
        }
    }

    private static boolean checkType(@NonNull Context context, @Nullable Uri uri, @NonNull Set<MimeType> extensions) {
        MimeTypeMap map = MimeTypeMap.getSingleton();
        if (uri == null) {
            return false;
        }
        ContentResolver resolver = context.getContentResolver();
        String type = map.getExtensionFromMimeType(resolver.getType(uri));
        String path = null;
        boolean pathParsed = false;
        for (MimeType extension : extensions) {
            Set<String> exts = extension.getExtensions();
            if (exts.contains(type)) {
                return true;
            }
            if (!pathParsed) {
                path = PhotoMetadataUtils.getPath(resolver, uri);
                if (!TextUtils.isEmpty(path)) {
                    path = path.toLowerCase(Locale.US);
                }
                pathParsed = true;
            }
            for (String ext : exts) {
                if (path != null && path.endsWith(ext)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * app 选择照片默认配置
     *
     * @param context
     *
     * @return
     */
    @NonNull
    private static SelectionCreator getDefaultImageConfig(@NonNull FragmentActivity context) {
        //目前去掉gif 拆切不支持
        return getDefaultImageConfig(context, true);
    }

    @NonNull
    private static SelectionCreator getDefaultImageConfig(@NonNull FragmentActivity context, boolean originalDefaultCheck) {
        //目前去掉gif 拆切不支持
        return Matisse.from(context)
                .choose(MimeType.of(MimeType.JPEG, MimeType.PNG, MimeType.BMP, MimeType.WEBP, MimeType.HEIC), true)
                .showSingleMediaType(true)
                .captureStrategy(new CaptureStrategy(true, context.getPackageName() + ".fileprovider"))
                .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
                .thumbnailScale(0.85f)
                .spanCount(4)
                .countable(true)
                .enableEdit(true)
                .originalEnable(true)
                .maxOriginalSize((int) SettingBuilder.getInstance().getMediaMaxSize())
                .originalDefaultCheck(originalDefaultCheck)
                .previewDefaultSend(true)
                .theme(getGalleryThemeId());
    }

    /**
     * app 选择照片聊天会话使用 包含gif 视频
     *
     * @param context
     *
     * @return
     */
    @NonNull
    private static SelectionCreator getChatImageConfig(@NonNull FragmentActivity context) {
        return Matisse.from(context)
                .choose(MimeType.ofAll(), false)
                .showSingleMediaType(true)
                .captureStrategy(new CaptureStrategy(true, context.getPackageName() + ".fileprovider"))
                .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
                .thumbnailScale(0.85f)
                .spanCount(4)
                .countable(true)
                .enableEdit(true)
                .originalEnable(true)
                .originalDefaultCheck(true)
                .previewDefaultSend(true)
                .theme(getGalleryThemeId())
                .withCustomAnimations(R.anim.activity_camera_enter_up_glide, R.anim.activity_camera_exit_up_glide);
    }

    /**
     * 选择头像使用
     *
     * @param activity
     * @param requestCode
     * @param callback
     */
    public static void chooseImageFromAlbum(FragmentActivity activity, int requestCode, AvoidOnResult.Callback callback) {
        PermissionAvoidManager manager = new PermissionAvoidManager(activity);
        manager.requestPermission(HiPermissionUtil.getStoragePermissions(), new PermissionAvoidManager.OnCommonPermissionCallBack() {

            @Override
            public void onRequestPermissionsResult(boolean hasPermission, boolean shouldShowAllRequestPermissionRationale) {
                if (hasPermission) {
                    Matisse.from(activity)
                            .choose(MimeType.of(MimeType.JPEG, MimeType.PNG, MimeType.BMP, MimeType.WEBP), true)
                            .withCustomAnimations(R.anim.activity_new_enter_up_glide, R.anim.activity_new_exit_up_glide)
                            .showSingleMediaType(true)
                            .captureStrategy(new CaptureStrategy(true, activity.getPackageName() + ".fileprovider"))
                            .maxSelectable(1)
                            .theme(getGalleryThemeId())
                            .enableCrop()
                            .ucropThemeId(getUCropThemeId())
                            .withAspectRatio(1, 1)
                            .withMaxResultSize(1024, 1024)
                            .forResult(requestCode, callback);
                } else {
                    Router.getService(IPermissionDialogService.class, CommonConstants.PERMISSION_DIALOG_SERVICE_KEY).showStorageFailedDialog(activity,null, null);
                }
            }
        });
    }

    /**
     * 选择图片从相册
     *
     * @param activity
     * @param requestCode
     * @param callback
     */
    public static void chooseImageFromAlbum(FragmentActivity activity, Pair<Float, Float> aspectRatio, int requestCode, AvoidOnResult.Callback callback) {
        PermissionAvoidManager manager = new PermissionAvoidManager(activity);
        manager.requestPermission(HiPermissionUtil.getStoragePermissions(), new PermissionAvoidManager.OnCommonPermissionCallBack() {

            @Override
            public void onRequestPermissionsResult(boolean hasPermission, boolean shouldShowAllRequestPermissionRationale) {
                if (hasPermission) {
                    Matisse.from(activity)
                            .choose(MimeType.of(MimeType.JPEG, MimeType.PNG, MimeType.BMP, MimeType.WEBP), true)
                            .withCustomAnimations(R.anim.activity_new_enter_up_glide, R.anim.activity_new_exit_up_glide)
                            .showSingleMediaType(true)
                            .captureStrategy(new CaptureStrategy(true, activity.getPackageName() + ".fileprovider"))
                            .maxSelectable(1)
                            .theme(getGalleryThemeId())
                            .enableCrop()
                            .ucropThemeId(getUCropThemeId())
                            .withAspectRatio(aspectRatio.first, aspectRatio.second)
                            .withMaxResultSize(1024, 1024)
                            .forResult(requestCode, callback);
                } else {
                    Router.getService(IPermissionDialogService.class, CommonConstants.PERMISSION_DIALOG_SERVICE_KEY).showStorageFailedDialog(activity,null, null);
                }
            }
        });
    }

    /**
     * 选择头像使用
     *
     * @param activity
     * @param callback
     */
    public static void chooseImageFromPhoto(FragmentActivity activity, AvoidOnResult.Callback callback) {
        String[] storagePermissions = HiPermissionUtil.getStoragePermissions();
        String[] permissions = new String[storagePermissions.length + 1];
        System.arraycopy(storagePermissions, 0, permissions, 0, storagePermissions.length);
        permissions[permissions.length - 1] = Manifest.permission.CAMERA;

        PermissionAvoidManager manager = new PermissionAvoidManager(activity);
        manager.requestPermission(permissions, new PermissionAvoidManager.OnCommonPermissionCallBack() {

            @Override
            public void onRequestPermissionsResult(boolean hasPermission, boolean shouldShowAllRequestPermissionRationale) {
                if (hasPermission) {
                    Matisse.from((FragmentActivity) activity)
                            .choose(MimeType.of(MimeType.JPEG, MimeType.PNG, MimeType.BMP, MimeType.WEBP), true)
                            .withCustomAnimations(R.anim.activity_new_enter_up_glide, R.anim.activity_new_exit_up_glide)
                            .showSingleMediaType(true)
                            .captureStrategy(new CaptureStrategy(true, activity.getPackageName() + ".fileprovider"))
                            .theme(getGalleryThemeId())
                            .enableCrop()
                            .ucropThemeId(getUCropThemeId())
                            .withAspectRatio(1, 1)
                            .captureThenCrop(callback);
                } else {
                    Router.getService(IPermissionDialogService.class, CommonConstants.PERMISSION_DIALOG_SERVICE_KEY).showStorageFailedDialog(activity,null, null);
                }
            }
        });
    }

    /**
     * 选择图片从拍照
     */
    public static void chooseImageFromPhoto(FragmentActivity activity, Pair<Float, Float> aspectRatio, AvoidOnResult.Callback callback) {
        String[] permissions = new String[]{Manifest.permission.CAMERA};
        PermissionAvoidManager manager = new PermissionAvoidManager(activity);
        manager.requestPermission(permissions, new PermissionAvoidManager.OnCommonPermissionCallBack() {

            @Override
            public void onRequestPermissionsResult(boolean hasPermission, boolean shouldShowAllRequestPermissionRationale) {
                if (hasPermission) {
                    Matisse.from(activity)
                            .choose(MimeType.of(MimeType.JPEG, MimeType.PNG, MimeType.BMP, MimeType.WEBP), true)
                            .withCustomAnimations(R.anim.activity_new_enter_up_glide, R.anim.activity_new_exit_up_glide)
                            .showSingleMediaType(true)
                            .captureStrategy(new CaptureStrategy(true, activity.getPackageName() + ".fileprovider"))
                            .theme(getGalleryThemeId())
                            .enableCrop()
                            .ucropThemeId(getUCropThemeId())
                            .withAspectRatio(aspectRatio.first, aspectRatio.second)
                            .captureThenCrop(callback);
                }
            }
        });
    }

    private static int getGalleryThemeId() {
        return useNewTheme ? R.style.BossGalleryNew : R.style.BossGallery;
    }

    private static int getUCropThemeId() {
        return useNewTheme ? R.style.BossHi_UCropNew : R.style.BossHi_UCrop;
    }

    private static void showLackedPermissionTips(Context context, String[] permissions) {
        for (String permission : permissions) {
            if (ActivityCompat.checkSelfPermission(context, permission) !=
                    PackageManager.PERMISSION_GRANTED){
                if (TextUtils.equals(permission ,Manifest.permission.CAMERA)){
                    Router.getService(IPermissionDialogService.class, CommonConstants.PERMISSION_DIALOG_SERVICE_KEY).showCameraFailedDialog(context,null, null);
                    return;
                } else if (TextUtils.equals(permission ,Manifest.permission.RECORD_AUDIO)){
                    Router.getService(IPermissionDialogService.class, CommonConstants.PERMISSION_DIALOG_SERVICE_KEY).showMicrophoneFailedDialog(context,null, null);
                    return;
                }
            }
        }
    }


    /**
     * 业务层选择单个图库照片上传
     */
    public interface OnGalleryCallBack extends OriginActivityCallback {

        void onGalleryListener(File imageFile);
    }

    /**
     * 业务层选择图片选择多个图片
     */
    public interface OnGalleryCountCallBack extends OriginActivityCallback {

        void onGalleryListener(List<File> fileList, boolean originalEnable);
    }

    /**
     * 业务层选择拍照上传图片回掉
     */
    public interface OnCameraCallBack extends OriginActivityCallback {

        void onCameraCallback(@Nullable File cameraFilePath);
    }

    /**
     * 原生回调支持
     */
    public interface OriginActivityCallback {

        /**
         * 默认回调
         * @param requestCode
         * @param resultCode
         * @param data
         */
        default void onActivityResult(int requestCode, int resultCode, Intent data){}
    }

}
