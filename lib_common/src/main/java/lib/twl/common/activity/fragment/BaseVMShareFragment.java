package lib.twl.common.activity.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;

import java.lang.reflect.ParameterizedType;

import lib.twl.common.activity.AsyncInflateManager;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.base.BaseViewModel;

/**
 * create by sunyangyang
 * on 2020/4/23
 *
 * @param <D> 对应的ViewDataBinding
 * @param <M> 共享activity的viewmodel,M必须和activity的保持一致，否则获取不到
 */
public abstract class BaseVMShareFragment<D extends ViewDataBinding, M extends BaseViewModel, AM extends BaseViewModel> extends LazyLoadFragment {
    private D mDataBinding;
    private M mViewModel;
    private AM mActivityViewModel;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View view = AsyncInflateManager.getInstance().getInflateView(getContentLayoutId());
        if (view != null) {
            mDataBinding = DataBindingUtil.bind(view);
        } else {
            mDataBinding = DataBindingUtil.inflate(inflater, getContentLayoutId(), container, false);
        }
        View root = mDataBinding.getRoot();

        if (!shouldLazyLoad()) {
            initViewHolder();
        } else {
            lazyInitData();
        }
        return root;
    }

    private void initViewHolder() {
        ViewModelProvider.AndroidViewModelFactory factory = ViewModelProvider.AndroidViewModelFactory.getInstance(BaseApplication.getApplication());
        /**
         * owner为fragment的activity，viewmodel是与activity共享的
         */

        if (activity != null && activity instanceof FragmentActivity) {
            ViewModelProvider provider = new ViewModelProvider((FragmentActivity) activity, factory);
            Class<AM> entityClass = (Class<AM>) ((ParameterizedType) activity.getClass().getGenericSuperclass()).getActualTypeArguments()[1];
            mActivityViewModel = provider.get(entityClass);
            if (getActivityBindingVariable() > 0) {
                mDataBinding.setVariable(getActivityBindingVariable(), mActivityViewModel);
            }
        }

        ViewModelProvider providerOwner = new ViewModelProvider(this, factory);
        Class<M> entityClassOwner = (Class<M>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
        mViewModel = providerOwner.get(entityClassOwner);
        if (getBindingVariable() > 0) {
            mDataBinding.setVariable(getBindingVariable(), mViewModel);
        }

        if (getCallbackVariable() >= 0 && getCallback() != null) {
            mDataBinding.setVariable(getCallbackVariable(), getCallback());
        }
        mDataBinding.executePendingBindings();
        mIsDataInited = true;
        initFragment();
    }

    @Override
    protected void lazyInitData() {
        if (!mIsDataInited && mIsVisibleToUser && mViewCreated) {
            initViewHolder();
        }
    }

    @LayoutRes
    public abstract int getContentLayoutId();

    protected abstract void initFragment();

    public abstract int getCallbackVariable();

    public abstract Object getCallback();

    public abstract int getBindingVariable();

    public abstract int getActivityBindingVariable();

    public D getDataBinding() {
        return mDataBinding;
    }

    public M getViewModel() {
        return mViewModel;
    }

    public AM getActivityViewModel() {
        return mActivityViewModel;
    }
}
