package lib.twl.common.model;

import java.io.Serializable;
import java.util.Objects;

public class ImageInfo implements Serializable {
    private static final long serialVersionUID = -6214516774598519616L;
    public int width;
    public int height;
    public String url;
    public String postfix;

    public ImageInfo() {

    }

    public ImageInfo(int width, int height, String url) {
        this.width = width;
        this.height = height;
        this.url = url;
    }

    public ImageInfo(int width, int height, String url, String postfix) {
        this.width = width;
        this.height = height;
        this.url = url;
        this.postfix = postfix;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public String getUrl() {
        return url;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public void setPostfix(String postfix) {
        this.postfix = postfix;
    }

    public String getPostfix() {
        return postfix;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ImageInfo imageInfo = (ImageInfo) o;
        return width == imageInfo.width
                && height == imageInfo.height
                && Objects.equals(url, imageInfo.url)
                && Objects.equals(postfix, imageInfo.postfix);
    }

    @Override
    public int hashCode() {
        return Objects.hash(width, height, url, postfix);
    }
}
