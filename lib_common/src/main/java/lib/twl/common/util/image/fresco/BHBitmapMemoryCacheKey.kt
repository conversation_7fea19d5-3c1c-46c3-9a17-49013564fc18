package lib.twl.common.util.image.fresco

import android.net.Uri
import com.facebook.cache.common.CacheKey
import com.facebook.imagepipeline.cache.BitmapMemoryCacheKey
import com.facebook.imagepipeline.common.ImageDecodeOptions
import com.facebook.imagepipeline.common.ResizeOptions
import com.facebook.imagepipeline.common.RotationOptions
import com.twl.utils.StringUtils

/**
 *
 * 定制的BitmapMemoryCacheKey
 * Fresco 3.x中BitmapMemoryCacheKey变为final，使用组合模式替代继承
 *
 * Created by tanshi<PERSON> on 2023/5/12
 */
class BHBitmapMemoryCacheKey(
    private val sourceString: String?,
    private val resizeOptions: ResizeOptions?,
    private val rotationOptions: RotationOptions?,
    private val imageDecodeOptions: ImageDecodeOptions?,
    private val postprocessorCacheKey: CacheKey?,
    private val postprocessorName: String?,
    private val callerContext: Any?
) : <PERSON><PERSON><PERSON>ey {

    private val delegate: BitmapMemoryCacheKey = BitmapMemoryCacheKey(
        sourceString ?: "",
        resizeOptions,
        rotationOptions ?: RotationOptions.autoRotate(),
        imageDecodeOptions ?: ImageDecodeOptions.defaults(),
        postprocessorCacheKey,
        postprocessorName
    )

    // 移除这个属性，避免与getUriString()方法冲突

    override fun containsUri(uri: Uri?): Boolean {
        return StringUtils.isEquals(getUriString(), uri.toString())
    }

    override fun toString(): String {
        return delegate.toString()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is BHBitmapMemoryCacheKey) return false
        return delegate == other.delegate
    }

    override fun hashCode(): Int {
        return delegate.hashCode()
    }

    override fun isResourceIdForDebugging(): Boolean {
        return delegate.isResourceIdForDebugging
    }

    override fun getUriString(): String? {
        return delegate.uriString
    }
}