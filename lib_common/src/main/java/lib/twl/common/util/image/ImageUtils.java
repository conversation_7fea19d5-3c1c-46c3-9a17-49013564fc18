package lib.twl.common.util.image;

import static android.media.ExifInterface.ORIENTATION_ROTATE_180;
import static android.media.ExifInterface.ORIENTATION_ROTATE_270;
import static android.media.ExifInterface.ORIENTATION_ROTATE_90;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.facebook.binaryresource.BinaryResource;
import com.facebook.binaryresource.FileBinaryResource;
import com.facebook.cache.common.CacheKey;
import com.facebook.imageformat.ImageFormat;
import com.facebook.imageformat.ImageFormatChecker;
import com.facebook.imagepipeline.cache.DefaultCacheKeyFactory;
import com.facebook.imagepipeline.core.ImagePipelineFactory;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.file.FileUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;

import lib.twl.common.base.BaseApplication;
import lib.twl.common.photoselect.bean.ImageSizeInfo;
import lib.twl.common.util.ProcessHelper;

/**
 * 通用的图片工具类
 *
 * Created by zhouyou on 2016/6/24.
 */
public class ImageUtils {

    private static final String TAG = "ImageUtils";

    public static final String DIR_ROTATE_TEMP = "rotateTemp";
    public static final String DIR_COMPRESS_PIC = "compressPic";

    /**
     * 根据指定路径下的图片，返回bitmap。
     *
     * @param config
     * @param path
     * @param width
     * @param height
     *
     * @return
     */
    public static Bitmap decodeSampledBitmapFromPath(Bitmap.Config config, String path, int width, int height) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        // 获取图片的宽高但是不把图片加载到内存中
        options.inJustDecodeBounds = true;
        //加载图片信息
        BitmapFactory.decodeFile(path, options);
        //计算缩放比
        options.inSampleSize = calculateInSampleSize(options, width, height);
        //图片质量
        options.inPreferredConfig = config;
        // 使用获得到的InSampleSize再次解析图片
        options.inJustDecodeBounds = false;
        return BitmapFactory.decodeFile(path, options);
    }

    /**
     * 压缩图片文件，综合质量压缩以及采样率压缩。
     * 注意：压缩文件使用完毕后，需要及时删除，避免占用存储空间
     *
     * @param fileSrc 图片源文件
     * @param quality 压缩质量，如0.6
     * @param width   图片的宽度
     * @param height  图片的高度
     *
     * @return 返回压缩后生成的临时文件
     */
    public static File compressImageFile(Context c, File fileSrc, float quality, int width, int height) {

        if (c == null || fileSrc == null || !fileSrc.exists() || fileSrc.isDirectory()) {
            return null;
        }

        File file = getImageCacheFileByName(c, "", DIR_COMPRESS_PIC);

        FileInputStream fileInputStream = null;
        FileOutputStream fos = null;
        ByteArrayOutputStream stream = null;

        try {
            fileInputStream = new FileInputStream(fileSrc);

            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inSampleSize = computeSampleSize(width, height);

            Bitmap tagBitmap = BitmapFactory.decodeStream(fileInputStream, null, options);
            stream = new ByteArrayOutputStream();

            if (tagBitmap != null) {
                tagBitmap.compress(Bitmap.CompressFormat.JPEG, (int) (quality * 100), stream);
                tagBitmap.recycle();
            }

            fos = new FileOutputStream(file);
            fos.write(stream.toByteArray());
            fos.flush();
        } catch (IOException e) {
            TLog.info(TAG, e.getMessage());
        } finally {
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
                if (fos != null) {
                    fos.close();
                }
                if (stream != null) {
                    stream.close();
                }
            } catch (IOException e) {
                TLog.info(TAG, e.getMessage());
            }
        }

        return file;
    }

    /**
     * 综合文件宽高数据，在宽高不变时计算合适的压缩采样率
     *
     * @param width  宽
     * @param height 高
     *
     * @return 图片压缩采样率
     */
    private static int computeSampleSize(int width, int height) {
        if (width == 0 || height == 0) {
            return 1;
        }

        int srcWidth = width;
        int srcHeight = height;

        srcWidth = srcWidth % 2 == 1 ? srcWidth + 1 : srcWidth;
        srcHeight = srcHeight % 2 == 1 ? srcHeight + 1 : srcHeight;

        int longSide = Math.max(srcWidth, srcHeight);
        int shortSide = Math.min(srcWidth, srcHeight);

        float scale = ((float) shortSide / longSide);
        if (scale <= 1 && scale > 0.5625) {
            if (longSide < 1664) {
                return 1;
            } else if (longSide < 4990) {
                return 2;
            } else if (longSide > 4990 && longSide < 10240) {
                return 4;
            } else {
                return longSide / 1280 == 0 ? 1 : longSide / 1280;
            }
        } else if (scale <= 0.5625 && scale > 0.5) {
            return longSide / 1280 == 0 ? 1 : longSide / 1280;
        } else {
            return (int) Math.ceil(longSide / (1280.0 / scale));
        }
    }

    /**
     * 获取图片临时缓存地址，用于临时存储压缩图片
     *
     * @param context
     * @param suffix
     *
     * @return
     */
    public static File getImageCacheFileByName(Context context, String suffix, String dirName) {
        String cacheDir = getImageCacheDir(context, TextUtils.isEmpty(dirName) ? "TempDir" : dirName).getAbsolutePath();
        String cacheBuilder = cacheDir + "/"
                + System.currentTimeMillis()
                + (Math.random() * 1000)
                + (TextUtils.isEmpty(suffix) ? ".jpg" : suffix);
        return new File(cacheBuilder);
    }

    /**
     * 获取图片临时缓存地址，用于临时存储压缩图片
     *
     * @param context   上下文
     * @param cacheName 缓存文件夹名
     *
     * @return 缓存文件夹
     */
    public static File getImageCacheDir(Context context, String cacheName) {
        File f = FileUtils.getCacheDir(context);
        File result = new File(f.getAbsolutePath(), cacheName);
        if (!result.exists()) {
            result.mkdirs();
        }
        return result;
    }

    private static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        int width = options.outWidth;
        int height = options.outHeight;
        int inSampleSize = 1;
        if (width > reqWidth || height > reqHeight) {
            int widthRatio = Math.round(width * 1.0f / reqWidth);
            int heightRatio = Math.round(height * 1.0f / reqHeight);
            inSampleSize = Math.max(widthRatio, heightRatio);
        }
        return inSampleSize;
    }

    public static ImageSizeInfo getImageViewSize(ImageView imageView) {
        ViewGroup.LayoutParams lp = imageView.getLayoutParams();
        int width = imageView.getWidth();
        if (width <= 0) {
            width = lp.width;
        }
        if (width <= 0) {
            width = getImageViewFieldValue(imageView, "mMaxWidth");
        }
        if (width <= 0) {
            DisplayMetrics dm = imageView.getContext().getResources().getDisplayMetrics();
            width = dm.widthPixels;
        }
        int height = imageView.getHeight();
        if (height <= 0) {
            height = lp.height;
        }
        if (height <= 0) {
            height = getImageViewFieldValue(imageView, "mMaxHeight");
        }
        if (height <= 0) {
            DisplayMetrics dm = imageView.getContext().getResources().getDisplayMetrics();
            height = dm.heightPixels;
        }
        ImageSizeInfo imageSizeInfo = new ImageSizeInfo();
        imageSizeInfo.width = width;
        imageSizeInfo.height = height;
        return imageSizeInfo;
    }

    /**
     * 通过反射获取图片ImageView的某个属性值
     *
     * @param object
     * @param fieldName
     *
     * @return
     */
    private static int getImageViewFieldValue(Object object, String fieldName) {
        int value = 0;
        Field field;
        try {
            field = ImageView.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            int fieldValue = field.getInt(object);
            if (fieldValue > 0 && fieldValue < Integer.MAX_VALUE) {
                value = fieldValue;
            }
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }


        return value;
    }

    /**
     * 获得图片类型
     *
     * @return
     */
    public static String getImageType(String path) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(path, options);
        return options.outMimeType;
    }

    /**
     * 计算聊天原图对应缩略图宽高
     */
    public static ImageSize chatThumbSize(File filePath) {
        float max = 300;
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(filePath.toString(), options);
        float oriWidth = options.outWidth;
        float orgHeight = options.outHeight;
        float scale = max / oriWidth;

        int thumbWidth = (int) (scale * oriWidth);
        int thumbHeight = (int) (scale * orgHeight);
        ImageSize size = new ImageSize();
        size.setWidth(thumbWidth);
        size.setHeight(thumbHeight);
        return size;
    }


    public static ImageSize chatSourceSize(File filePath) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(filePath.toString(), options);
        int oriWidth = options.outWidth;
        int orgHeight = options.outHeight;
        ImageSize size = new ImageSize();
        size.setWidth(oriWidth);
        size.setHeight(orgHeight);
        return size;
    }

    /**
     * 判断图片是否包含旋转角度
     * 如果是则自动旋转成正常方向
     *
     * @param orientation
     * @param path
     * @return 避免影响原图，返回一张处理后的临时图片文件
     */
    public static File rotatePicWhenHasAngle(int orientation, String path, Context c) {
        int degrees = 0;
        switch (orientation) {
            case ORIENTATION_ROTATE_90:
                degrees = 90;
                break;
            case ORIENTATION_ROTATE_180:
                degrees = 180;
                break;
            case ORIENTATION_ROTATE_270:
                degrees = 270;
                break;
            default:
                break;
        }

        if (degrees == 0) {
            return null;
        }

        FileOutputStream fos = null;
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        try {
            Bitmap tagBitmap = BitmapFactory.decodeFile(path);
            if (tagBitmap == null) {
                return null;
            }

            // 旋转
            Matrix mat = new Matrix();
            mat.postRotate(degrees);
            File fileTemp = getImageCacheFileByName(c, "", DIR_ROTATE_TEMP);
            Bitmap resultBitmap = Bitmap.createBitmap(tagBitmap, 0, 0, tagBitmap.getWidth(), tagBitmap.getHeight(), mat, true);

            // bitmap保存为文件
            resultBitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream);
            resultBitmap.recycle();
            // 保存旋转后的临时文件并返回
            fos = new FileOutputStream(fileTemp);
            fos.write(stream.toByteArray());
            fos.flush();
            fos.close();
            stream.close();

            return fileTemp;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
                stream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * Image宽高保存Bean类。
     */
    public static class ImageSize {

        private int width;

        private int height;

        public ImageSize() {
        }

        public ImageSize(int width, int height) {
            this.width = width;
            this.height = height;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public int getWidth() {
            return width;
        }

        public int getHeight() {
            return height;
        }

        @Override
        public String toString() {
            return "ImageSize{" +
                    "width=" + width +
                    ", height=" + height +
                    '}';
        }
    }

    /**
     * 判断指定文件是否是HEIF/HEIC文件
     */
    public static boolean isHeicOrHeifImage(File file) {
//        String suffix = FileUtils.getFileSuffix(file);
//        return suffix.contains("heic") || suffix.contains("HEIC")
//                || suffix.contains("heif") || suffix.contains("HEIF");
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] header = new byte[12];
            if (fis.read(header) != 12) return false;
            String headerStr = new String(header);
            TLog.info(TAG, "isHeicOrHeifImage,headerStr:" + headerStr);
            return headerStr.contains("ftypheic");
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 将heif/heic等特殊格式的文件转换为JPEG格式
     */
    public static File convertToJPEG(File file) {

        TLog.info(TAG, "convertToJPEG,filepath:" + file.getAbsolutePath());

        File tempFile = new File(ProcessHelper.getContext().getFilesDir(), file.getName());
        if (tempFile.exists()) {
            tempFile.delete();
        }

        try (FileOutputStream out = new FileOutputStream(tempFile)) {
            Bitmap bmp = BitmapFactory.decodeFile(file.getPath());
            bmp.compress(Bitmap.CompressFormat.JPEG, 100, out);
            out.flush();
        } catch (IOException e) {
            TLog.error(TAG, "convertHeifToJpeg: %s", e.getMessage());
        }

        return tempFile;
    }

    /**
     * 将drawable转换本地可加载文件路径
     */
    public static File formDrawableToFile(Drawable drawable) {
        Bitmap bitmap = getBitmapFromDrawable(drawable);

        File result = new File("temp");
        try {
            result = File.createTempFile("temp", ".jpeg");
        } catch (IOException e) {
            TLog.error(TAG, "formDrawableToFile: %s", e.getMessage());
        }

        try (FileOutputStream out = new FileOutputStream(result)) {
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out);
            out.flush();
        } catch (IOException e) {
            TLog.error(TAG, "formDrawableToFile: %s", e.getMessage());
        }
        return result;
    }

    private static Bitmap getBitmapFromDrawable(Drawable drawable) {
        int w = drawable.getIntrinsicWidth();
        int h = drawable.getIntrinsicHeight();
        Bitmap bitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, w, h);
        drawable.draw(canvas);
        return bitmap;
    }

    private static String getImageFormat(String url) {
        if (TextUtils.isEmpty(url)) {
            return "";
        }
        if (!url.startsWith("file://")) {
            ImageRequest imageRequest = ImageRequestBuilder.newBuilderWithSource(Uri.parse(url))
                    .setProgressiveRenderingEnabled(true).build();

            CacheKey cacheKey = DefaultCacheKeyFactory.getInstance().getEncodedCacheKey(imageRequest,
                    BaseApplication.getApplication());

            if (ImagePipelineFactory.getInstance().getMainFileCache().hasKey(cacheKey)) {
                BinaryResource resource = ImagePipelineFactory.getInstance().getMainFileCache().getResource(cacheKey);
                if (resource != null) {
                    File cacheFile = ((FileBinaryResource) resource).getFile();
                    try (FileInputStream inputStream = new FileInputStream(cacheFile)) {
                        ImageFormat imageFormat = ImageFormatChecker.getImageFormat(inputStream);
                        return imageFormat.getName().toLowerCase();
                    } catch (IOException e) {
                        e.printStackTrace();
                        return "";
                    }
                }
            }
        }
        //本地文件或没有cache
        return url.substring(url.lastIndexOf(".") + 1).toLowerCase();
    }

    public static boolean isGif(String url) {
        return TextUtils.equals("gif", getImageFormat(url));
    }

    public static void isGifAsync(View view, String url, Callback callback) {
        Glide.with(view.getContext()).downloadOnly().load(url).into(new CustomTarget<File>() {
            @Override
            public void onResourceReady(@NonNull File resource, @Nullable Transition<? super File> transition) {
                String filePath = resource.getPath();
                BitmapFactory.Options options = new BitmapFactory.Options();
                options.inJustDecodeBounds = true;
                BitmapFactory.decodeFile(filePath, options);
                String mimeType = options.outMimeType;
                Object tag = view.getTag();
                if (tag instanceof String) {
                    String tagValue = (String) tag;
                    if (!TextUtils.equals(tagValue, url)) {
                        return;
                    }
                } else {
                    return;
                }
                if (!TextUtils.isEmpty(mimeType) && mimeType.toLowerCase().endsWith("gif")) {
                    callback.updateValue(true);
                } else {
                    callback.updateValue(false);
                }
            }

            @Override
            public void onLoadCleared(@Nullable Drawable placeholder) {

            }

            @Override
            public void onLoadFailed(@Nullable Drawable errorDrawable) {
                callback.updateValue(false);
            }
        });
    }

    public static int computeSize(File imageFile) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        options.inSampleSize = 1;
        BitmapFactory.decodeFile(imageFile.getPath(), options);
        int srcWidth = options.outWidth;
        int srcHeight = options.outHeight;
        srcWidth = srcWidth % 2 == 1 ? srcWidth + 1 : srcWidth;
        srcHeight = srcHeight % 2 == 1 ? srcHeight + 1 : srcHeight;
        int longSide = Math.max(srcWidth, srcHeight);
        int shortSide = Math.min(srcWidth, srcHeight);
        float scale = ((float) shortSide / longSide);
        if (scale <= 1 && scale > 0.5625) {
            if (longSide < 1664) {
                return 1;
            } else if (longSide < 4990) {
                return 2;
            } else if (longSide > 4990 && longSide < 10240) {
                return 4;
            } else {
                return longSide / 1280 == 0 ? 1 : longSide / 1280;
            }
        } else if (scale <= 0.5625 && scale > 0.5) {
            return longSide / 1280 == 0 ? 1 : longSide / 1280;
        } else {
            return (int) Math.ceil(longSide / (1280.0 / scale));
        }
    }

    /**
     * 根据路径获取图片宽高
     *
     * @param path 图片本地路径
     * @return 宽&高
     */
    public static int[] getImageWidthHeight(String path) {
        BitmapFactory.Options options = new BitmapFactory.Options();

        /**
         * 最关键在此，把options.inJustDecodeBounds = true;
         * 这里再decodeFile()，返回的bitmap为空，但此时调用options.outHeight时，已经包含了图片的高了
         */
        options.inJustDecodeBounds = true;
        Bitmap bitmap = BitmapFactory.decodeFile(path, options); // 此时返回的bitmap为null
        /**
         *options.outHeight为原始图片的高
         */
        return new int[]{options.outWidth, options.outHeight};
    }

    public interface Callback {
        void updateValue(boolean isGif);
    }

}
