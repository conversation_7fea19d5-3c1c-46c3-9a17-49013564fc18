package lib.twl.common.util.image.fresco

import android.net.Uri
import com.facebook.cache.common.CacheKey
import com.facebook.imagepipeline.cache.CacheKeyFactory
import com.facebook.imagepipeline.request.ImageRequest
import com.techwolf.lib.tlog.TLog

/**
 *
 * 自定义的CacheKeyFactory
 * 主要是为了解决Fresco根据图片url缓存判断存在的问题
 * 详细可见：https://github.com/facebook/fresco/issues/2505
 *
 * Created by tanshicheng on 2023/5/14
 */
open class BHCacheKeyFactory : CacheKeyFactory {

    companion object {
        private const val TAG = "BHCacheKeyFactory"
    }

    override fun getBitmapCacheKey(request: ImageRequest, callerContext: Any?): CacheKey {
        val cacheKey = BHBitmapMemoryCacheKey(
            getCacheKeySourceUri(request.sourceUri).toString(),
            request.resizeOptions,
            request.rotationOptions,
            request.imageDecodeOptions,
            null,
            null,
            callerContext
        )
        TLog.info(TAG, "getBitmapCacheKey: uri=${request.sourceUri}, cacheKey=${cacheKey.uriString}")
        return cacheKey
    }

    private fun getCacheKeySourceUri(sourceUri: Uri): Uri {
        return sourceUri
    }

    override fun getPostprocessedBitmapCacheKey(request: ImageRequest, callerContext: Any?): CacheKey? {
        val postprocessor = request.postprocessor
        val postprocessorCacheKey: CacheKey?
        val postprocessorName: String?
        if (postprocessor != null) {
            postprocessorCacheKey = postprocessor.postprocessorCacheKey
            postprocessorName = postprocessor.javaClass.name
        } else {
            postprocessorCacheKey = null
            postprocessorName = null
        }
        val cacheKey = BHBitmapMemoryCacheKey(
            getCacheKeySourceUri(request.sourceUri).toString(),
            request.resizeOptions,
            request.rotationOptions,
            request.imageDecodeOptions,
            postprocessorCacheKey,
            postprocessorName,
            callerContext
        )
        TLog.info(TAG, "getPostprocessedBitmapCacheKey: uri=${request.sourceUri}, postprocessor=$postprocessorName")
        return cacheKey
    }

    override fun getEncodedCacheKey(request: ImageRequest, callerContext: Any?): CacheKey {
        val cacheKey = getEncodedCacheKey(request, request.sourceUri, callerContext)
        TLog.info(TAG, "getEncodedCacheKey: uri=${request.sourceUri}")
        return cacheKey
    }

    override fun getEncodedCacheKey(request: ImageRequest, sourceUri: Uri, callerContext: Any?): CacheKey {
        val cacheKey = BHSimpleCacheKey(getCacheKeySourceUri(sourceUri).toString())
        TLog.info(TAG, "getEncodedCacheKey(with sourceUri): uri=$sourceUri")
        return cacheKey
    }
}