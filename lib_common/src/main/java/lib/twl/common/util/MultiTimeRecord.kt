package lib.twl.common.util

import java.util.Collections

object MultiTimeRecord {

    const val TAG = "MultiTimeRecord"
    private val startTimeMap = Collections.synchronizedMap(mutableMapOf<String, GroupTimeValue>())

    fun startTimeFirstByTraceId(timeKey: String, traceId: String) {
        val v = startTimeMap["$timeKey-$traceId"]
        if (v == null) {
            startTimeMap["$timeKey-$traceId"] = GroupTimeValue(firstTime = System.currentTimeMillis())
        } else {
            if (v.firstTimeValid().not()) {
                v.firstTime = System.currentTimeMillis()
            }
        }
    }

    fun startTimeSecondByTraceId(timeKey: String, traceId: String) {
        val v = startTimeMap["$timeKey-$traceId"]
        if (v == null) {
            startTimeMap["$timeKey-$traceId"] = GroupTimeValue(secondTime = System.currentTimeMillis())
        } else {
            if (v.secondTimeValid().not()) {
                v.secondTime = System.currentTimeMillis()
            }
        }
    }

    fun finishTimeFirstByTraceId(timeKey: String, traceId: String) {
        val v = startTimeMap["$timeKey-$traceId"]
        v?.let {
            if (v.firstTimeValid()) {
                v.firstTime = System.currentTimeMillis().minus(v.firstTime)
            }
        }
    }


    fun finishTimeSecondByTraceId(timeKey: String, traceId: String) {
        val v = startTimeMap["$timeKey-$traceId"]
        v?.let {
            if (v.secondTimeValid()) {
                v.secondTime = System.currentTimeMillis().minus(v.secondTime)
            }
        }
    }

    fun finishTimeAllByTraceId(timeKey: String, traceId: String) {
        val v = startTimeMap["$timeKey-$traceId"]
        v?.let {
            if (v.isValidTime()) {
                val current = System.currentTimeMillis()
                v.firstTime = current.minus(v.firstTime)
                v.secondTime = current.minus(v.secondTime)
            }
        }
    }

    fun getRecordTime(timeKey: String, traceId: String): GroupTimeValue {
        return startTimeMap.remove("$timeKey-$traceId") ?: GroupTimeValue()
    }

}

data class GroupTimeValue(
    var firstTime: Long = -1L,
    var secondTime: Long = -1L,
) {
    fun isValidTime(): Boolean {
        return firstTime >= 0 && secondTime >= 0
    }

    fun firstTimeValid(): Boolean {
        return firstTime >= 0
    }
    fun secondTimeValid(): Boolean {
        return secondTime >= 0
    }
}