package lib.twl.common.util;

import static java.util.Calendar.FRIDAY;
import static java.util.Calendar.MONDAY;
import static java.util.Calendar.SATURDAY;
import static java.util.Calendar.SUNDAY;
import static java.util.Calendar.THURSDAY;
import static java.util.Calendar.TUESDAY;
import static java.util.Calendar.WEDNESDAY;

import com.techwolf.lib.tlog.TLog;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * Created by leo on 2014/12/20.
 */
public class LDate {
    private static final String TAG = "LDate";

    public static final int YEAR_TR = 16;//根据年月日生成时间戳，year的位移
    public static final int MONTH_TR = 8;//根据年月日生成时间戳，month的位移
    private static final String DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss SSS";
    public static SimpleDateFormat MdFormat = new SimpleDateFormat("M月d日", Locale.CHINA);
    public static SimpleDateFormat MmDdFormat = new SimpleDateFormat("MM月dd日", Locale.CHINA);
    public static SimpleDateFormat HmFormat = new SimpleDateFormat("HH:mm", Locale.CHINA);
    public static SimpleDateFormat yMdFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.CHINA);
    public static SimpleDateFormat yMdHmFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA);
    public static SimpleDateFormat titleMdFormat = new SimpleDateFormat("yyyy年M月d日", Locale.CHINA);
    public static SimpleDateFormat yyyyMMdd_HH_mm_ssFormat = new SimpleDateFormat("yyyyMMdd-HH-mm-ss", Locale.CHINA);
    public static SimpleDateFormat yMdHmFormatC = new SimpleDateFormat("yyyy年M月d日 HH:mm", Locale.CHINA);
    private static SimpleDateFormat yyyyMM = new SimpleDateFormat("yyyy-MM", Locale.CHINA);

    public static final long getLongTimeFromString(String time, DateFormat format) {
        try {
            return format.parse(time).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static final String getDate() {
        return getDate(DEFAULT_FORMAT);
    }

    public static final String getDate(String format) {
        return getDate(System.currentTimeMillis(), format);
    }

    public static final String getDate(long time, String format) {
        time = Math.abs(time);
        Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("GMT+8:00"));
        cal.setTimeInMillis(time);
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(cal.getTime());
    }

    public static final String getDate(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    public static final String getDate(long time, DateFormat format) {
        try {
            return format.format(time);
        } catch (Exception e) {
            TLog.error(TAG, "getDate error:" + e.getMessage() + " time：" + time);
            return "";
        }
    }

    public static final String getTime(long time, String format) {
        time = Math.abs(time);
        try {
            time += new SimpleDateFormat("yyyy").parse("1970").getTime();
        } catch (ParseException e) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(time));
    }

    public static final String getTime(long time, DateFormat format) {
        return format.format(time);
    }

    public static final long getAllYear(long time) {
        return (time / (1000 * 60 * 60 * 24 * 365));
    }

    public static final long getAllMonth(long time) {
        return (time / (1000 * 60 * 60 * 24) / 12);
    }

    public static final long getAllDay(long time) {
        return (time / (1000 * 60 * 60 * 24));
    }

    public static final long getAllHour(long time) {
        return (time / (1000 * 60 * 60));
    }

    public static final long getAllMinute(long time) {
        return (time / (1000 * 60));
    }

    public static final long getAllSecond(long time) {
        return (time / 1000);
    }

    public static String getTimeAgo4(long time) {
        if (isBeforeToday(time)) {
            return MdFormat.format(time);
        }

        if (isDuringOneMinute(time)) {
            return "刚刚";
        } else {
            return HmFormat.format(time);
        }
    }

    public static boolean isSameYear(long time) {
        Calendar cNow = Calendar.getInstance(Locale.getDefault());
        cNow.setTime(new Date(System.currentTimeMillis()));
        Calendar cTime = Calendar.getInstance(Locale.getDefault());
        cTime.setTime(new Date(time));
        return cNow.get(Calendar.YEAR) == cTime.get(Calendar.YEAR);
    }

    public static boolean isBeforeToday(long time) {
        Calendar cNow = Calendar.getInstance(Locale.getDefault());
        cNow.setTime(new Date(System.currentTimeMillis()));
        Calendar cTime = Calendar.getInstance(Locale.getDefault());
        cTime.setTime(new Date(time));
        return cNow.get(Calendar.YEAR) >= cTime.get(Calendar.YEAR)
                && cNow.get(Calendar.DAY_OF_YEAR) > cTime.get(Calendar.DAY_OF_YEAR);
    }

    public static boolean isDuringOneMinute(long time) {
        long current = System.currentTimeMillis();
        return (current - time) / 1000 <= 60;
    }

    public static long getDuringMinute(long time) {
        long current = System.currentTimeMillis();
        return (current - time) / 1000 / 60;
    }

    /**
     * 判断传入的值是否为今天
     *
     * @param time
     * @return
     */
    public static final boolean isToday(long time) {
        Calendar cNow = Calendar.getInstance(Locale.getDefault());
        cNow.setTime(new Date(System.currentTimeMillis()));
        Calendar cTime = Calendar.getInstance(Locale.getDefault());
        cTime.setTime(new Date(time));
        if (cNow.get(Calendar.DAY_OF_MONTH) != cTime.get(Calendar.DAY_OF_MONTH)) {
            return false;
        }
        if (cNow.get(Calendar.MONTH) != cTime.get(Calendar.MONTH)) {
            return false;
        }
        return cNow.get(Calendar.YEAR) == cTime.get(Calendar.YEAR);
    }

    public static boolean isYesterday(long time) {
        Calendar cNow = Calendar.getInstance(Locale.getDefault());
        cNow.setTime(new Date(System.currentTimeMillis()));
        cNow.add(Calendar.DAY_OF_YEAR, -1);
        Calendar cTime = Calendar.getInstance(Locale.getDefault());
        cTime.setTime(new Date(time));
        if (cNow.get(Calendar.DAY_OF_MONTH) != cTime.get(Calendar.DAY_OF_MONTH)) {
            return false;
        }
        if (cNow.get(Calendar.MONTH) != cTime.get(Calendar.MONTH)) {
            return false;
        }
        return cNow.get(Calendar.YEAR) == cTime.get(Calendar.YEAR);
    }

    public static boolean isTomorrow(long time) {
        Calendar cNow = Calendar.getInstance(Locale.getDefault());
        cNow.setTime(new Date(System.currentTimeMillis()));
        cNow.add(Calendar.DAY_OF_YEAR, 1);
        Calendar cTime = Calendar.getInstance(Locale.getDefault());
        cTime.setTime(new Date(time));
        if (cNow.get(Calendar.DAY_OF_MONTH) != cTime.get(Calendar.DAY_OF_MONTH)) {
            return false;
        }
        if (cNow.get(Calendar.MONTH) != cTime.get(Calendar.MONTH)) {
            return false;
        }
        return cNow.get(Calendar.YEAR) == cTime.get(Calendar.YEAR);
    }

    /**
     * 从当天的凌晨算起，往前的七天之内
     *
     * @param time
     * @return
     */
    public static boolean isPreViewWeek(long time) {
        Calendar today = Calendar.getInstance(Locale.FRENCH);
        today.setTimeInMillis(System.currentTimeMillis());
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);
        //同一周内显示【周一】【周二】【周三】...
        int dayOfWeek = today.get(Calendar.DAY_OF_WEEK);
        int dayOffset = 0;
        switch (dayOfWeek) {
            case MONDAY:
                dayOffset = 0;
                break;
            case TUESDAY:
                dayOffset = 1;
                break;
            case WEDNESDAY:
                dayOffset = 2;
                break;
            case THURSDAY:
                dayOffset = 3;
                break;
            case FRIDAY:
                dayOffset = 4;
                break;
            case SATURDAY:
                dayOffset = 5;
                break;
            case SUNDAY:
                dayOffset = 6;
                break;
            default:

        }
        today.add(Calendar.DAY_OF_YEAR, -dayOffset);

        return time > today.getTimeInMillis();

    }

    /**
     * 明天到下周一之前
     *
     * @param time
     * @return
     */
    public static boolean isCurrentWeek(long time) {
        long currentTimeMillis = System.currentTimeMillis();
        Calendar nextMonday = Calendar.getInstance(Locale.FRENCH);
        nextMonday.setTimeInMillis(currentTimeMillis);
        int dayOfWeek = nextMonday.get(Calendar.DAY_OF_WEEK);
        int dayOffset = 0;
        switch (dayOfWeek) {
            case Calendar.MONDAY:
                dayOffset = 7;
                break;
            case Calendar.TUESDAY:
                dayOffset = 6;
                break;
            case Calendar.WEDNESDAY:
                dayOffset = 5;
                break;
            case Calendar.THURSDAY:
                dayOffset = 4;
                break;
            case Calendar.FRIDAY:
                dayOffset = 3;
                break;
            case Calendar.SATURDAY:
                dayOffset = 2;
                break;
            case Calendar.SUNDAY:
                dayOffset = 1;
                break;
            default:

        }
        nextMonday.add(Calendar.DAY_OF_YEAR, dayOffset);
        nextMonday.set(Calendar.HOUR_OF_DAY, 0);
        nextMonday.set(Calendar.MINUTE, 0);
        nextMonday.set(Calendar.SECOND, 0);
        nextMonday.set(Calendar.MILLISECOND, 0);
        return time > currentTimeMillis && time < nextMonday.getTimeInMillis();
    }

    public static String dayOfWeek(long time) {
        //选择法国,因为US每周第一天是周日,法国第一天是周一
        Calendar instance = Calendar.getInstance(Locale.FRENCH);
        //同一周内显示【周一】【周二】【周三】...
        instance.setTimeInMillis(time);
        int day = instance.get(Calendar.DAY_OF_WEEK);
        switch (day) {
            case SUNDAY:
                return "星期日";
            case MONDAY:
                return "星期一";
            case TUESDAY:
                return "星期二";
            case WEDNESDAY:
                return "星期三";
            case THURSDAY:
                return "星期四";
            case FRIDAY:
                return "星期五";
            case SATURDAY:
                return "星期六";
            default:
                return "";
        }
    }

    /**
     * 将long类型的时间转换format格式
     *
     * @param time
     * @param format
     * @return
     */
    public static String getTimeInPattern(long time, String format) {
        try {

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format, Locale.getDefault());
            return simpleDateFormat.format(new Date(time));
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 判断两个时间是否属于同一个月
     *
     * @param pre
     * @param next
     * @return
     */
    public static boolean isInTheSameMonth(long pre, long next) {
        return getTimeInPattern(pre, "yyyy年MM月").equals(getTimeInPattern(next, "yyyy年MM月"));
    }

    public static long dayTimeFormatToLong(int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    /**
     * 根据年月日生成可准确排序是时间戳
     *
     * @param calendar
     * @return
     */
    public static long generateTimeStamp(Calendar calendar) {
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int time = (year << YEAR_TR) + (month << MONTH_TR) + day;
        return time;
    }

    /**
     * 根据年月日生成可准确排序是时间戳
     *
     * @return
     */
    public static long generateTimeStamp(int year, int month) {
        int time = (year << YEAR_TR) + (month << MONTH_TR) + 1;
        return time;
    }

    /**
     * 返回年月（是当月返回本月）
     *
     * @param time
     * @return
     */
    public static String getSearchRecordFileMoth(long time) {
        long current = System.currentTimeMillis();
        long c = current - time;
        long month = getAllMonth(c);
        if (month > 0) {
            return getTimeInPattern(time, "yyyy年M月");
        } else {
            return "这个月";
        }

    }

    public static String getDate(int year, int month, boolean isPre) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        if (isPre) {
            calendar.set(Calendar.DATE, 1);
        } else {
            calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
        }
        return yMdFormat.format(calendar.getTime());
    }

    public static String getTitleDate(String dateString) {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = yMdFormat.parse(dateString);
            calendar.setTime(date);
            return titleMdFormat.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getTitleOnlyMonthDayDate(String dateString) {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = yMdFormat.parse(dateString);
            calendar.setTime(date);
            return MmDdFormat.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static Calendar yyyyMMParse(String date) {
        Calendar calendar = Calendar.getInstance();
        try {
            Date timeDate = yyyyMM.parse(date);
            calendar.setTime(timeDate);
        } catch (ParseException e) {
            TLog.error(TAG, "yyyyMMParse error:" + e.getMessage());
        }
        return calendar;
    }


    /**
     * 将日期和时间点拼接转换成时间戳
     *
     * @param day   日期 eg：2022-04-06
     * @param clock 时间点 分钟为单位的  eg：17:00
     */
    public static long transformStringToTimestamp(String day, String clock) {
        //拼接
        String timeStr = day + " " + clock;
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA);
        try {
            date = dateFormat.parse(timeStr);
        } catch (ParseException e) {
            TLog.error(TAG, "transformStringToTimestamp error:" + e.getMessage());
        }
        return date != null ? date.getTime() : 0L;
    }
}
