package lib.twl.common.util.image

import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.imagepipeline.request.ImageRequest
import com.techwolf.lib.tlog.TLog
import android.net.Uri

/**
 * 图片缓存测试工具类
 * 用于测试 BHCacheKeyFactory 的兼容性和缓存命中率
 */
object CacheTestHelper {
    
    private const val TAG = "CacheTestHelper"
    
    /**
     * 测试指定图片的缓存状态
     */
    fun testImageCacheStatus(imageUrl: String) {
        TLog.info(TAG, "=== Testing cache status for: $imageUrl ===")
        
        val request = ImageRequest.fromUri(Uri.parse(imageUrl))
        val pipeline = Fresco.getImagePipeline()
        
        // 检查内存缓存
        val inMemoryCache = pipeline.isInBitmapMemoryCache(request)
        TLog.info(TAG, "In memory cache: $inMemoryCache")
        
        // 检查磁盘缓存
        val inDiskCache = pipeline.isInDiskCacheSync(request)
        TLog.info(TAG, "In disk cache: $inDiskCache")
        
        // 获取缓存键信息
        val cacheKeyFactory = pipeline.cacheKeyFactory
        val bitmapCacheKey = cacheKeyFactory.getBitmapCacheKey(request, null)
        val encodedCacheKey = cacheKeyFactory.getEncodedCacheKey(request, null)
        
        TLog.info(TAG, "Bitmap cache key: ${bitmapCacheKey}")
        TLog.info(TAG, "Encoded cache key: ${encodedCacheKey}")
        TLog.info(TAG, "=== End cache status test ===")
    }
    
    /**
     * 批量测试图片缓存状态
     */
    fun testMultipleImagesCacheStatus(imageUrls: List<String>) {
        TLog.info(TAG, "=== Batch testing ${imageUrls.size} images ===")
        
        var memoryHits = 0
        var diskHits = 0
        
        imageUrls.forEachIndexed { index, url ->
            TLog.info(TAG, "Testing image ${index + 1}/${imageUrls.size}: $url")
            
            val request = ImageRequest.fromUri(Uri.parse(url))
            val pipeline = Fresco.getImagePipeline()
            
            if (pipeline.isInBitmapMemoryCache(request)) {
                memoryHits++
                TLog.info(TAG, "  ✓ Memory cache hit")
            } else if (pipeline.isInDiskCacheSync(request)) {
                diskHits++
                TLog.info(TAG, "  ✓ Disk cache hit")
            } else {
                TLog.info(TAG, "  ✗ Cache miss")
            }
        }
        
        val totalHits = memoryHits + diskHits
        val hitRate = if (imageUrls.isNotEmpty()) (totalHits * 100.0 / imageUrls.size) else 0.0
        
        TLog.info(TAG, "=== Batch test results ===")
        TLog.info(TAG, "Total images: ${imageUrls.size}")
        TLog.info(TAG, "Memory hits: $memoryHits")
        TLog.info(TAG, "Disk hits: $diskHits")
        TLog.info(TAG, "Total hits: $totalHits")
        TLog.info(TAG, "Hit rate: ${String.format("%.1f", hitRate)}%")
        TLog.info(TAG, "=== End batch test ===")
    }
    
    /**
     * 清除所有缓存（用于测试）
     */
    fun clearAllCaches() {
        TLog.info(TAG, "Clearing all Fresco caches...")
        val pipeline = Fresco.getImagePipeline()
        pipeline.clearMemoryCaches()
        pipeline.clearDiskCaches()
        TLog.info(TAG, "All caches cleared")
    }
    
    /**
     * 获取缓存统计信息
     */
    fun printCacheStatistics() {
        FrescoHelper.printCacheStats()
    }
    
    /**
     * 重置缓存统计
     */
    fun resetCacheStatistics() {
        FrescoHelper.resetCacheStats()
    }
    
    /**
     * 测试缓存键一致性
     * 同一个URL多次生成的缓存键应该相同
     */
    fun testCacheKeyConsistency(imageUrl: String, testCount: Int = 5) {
        TLog.info(TAG, "=== Testing cache key consistency for: $imageUrl ===")
        
        val cacheKeys = mutableSetOf<String>()
        val pipeline = Fresco.getImagePipeline()
        val cacheKeyFactory = pipeline.cacheKeyFactory
        
        repeat(testCount) { i ->
            val request = ImageRequest.fromUri(Uri.parse(imageUrl))
            val bitmapCacheKey = cacheKeyFactory.getBitmapCacheKey(request, null)
            val encodedCacheKey = cacheKeyFactory.getEncodedCacheKey(request, null)
            
            val keyString = "bitmap:${bitmapCacheKey}, encoded:${encodedCacheKey}"
            cacheKeys.add(keyString)
            
            TLog.info(TAG, "Test ${i + 1}: $keyString")
        }
        
        if (cacheKeys.size == 1) {
            TLog.info(TAG, "✓ Cache key consistency test PASSED - all keys are identical")
        } else {
            TLog.error(TAG, "✗ Cache key consistency test FAILED - found ${cacheKeys.size} different keys")
            cacheKeys.forEachIndexed { index, key ->
                TLog.error(TAG, "  Key $index: $key")
            }
        }
        TLog.info(TAG, "=== End cache key consistency test ===")
    }
}
