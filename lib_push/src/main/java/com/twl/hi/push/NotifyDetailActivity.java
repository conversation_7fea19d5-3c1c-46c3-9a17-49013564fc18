package com.twl.hi.push;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.appcompat.app.AppCompatActivity;

import com.google.gson.Gson;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.util.PushConstants;
import com.twl.utils.StringUtils;
import com.xiaomi.mipush.sdk.MiPushMessage;
import com.xiaomi.mipush.sdk.PushMessageHelper;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import lib.twl.push.R;

public class NotifyDetailActivity extends AppCompatActivity {

    public static final String TAG = "NotifyDetailActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_hw_notify_detail);
        String message = "";
        TLog.info(TAG, "========NotifyDetailActivity======onCreate");
        Intent intent = getIntent();
        if (intent != null) {
            MiPushMessage miPushMessage = (MiPushMessage) intent.getSerializableExtra(PushMessageHelper.KEY_MESSAGE);
            // 小米push
            if (miPushMessage != null) {
                Map<String, String> extrasMap = miPushMessage.getExtra();
                TLog.info(TAG, "========NotifyDetailActivity======message MiPush :\n" + (new Gson()).toJson(extrasMap));
                PushSdkManager.getInstance().onNotificationMessageClicked(PushSdkConfig.miType, extrasMap);
            } else if (getIntent().getExtras() != null && !TextUtils.isEmpty(getIntent().getExtras().getString("message"))) {
                // oppo push
                message = getIntent().getExtras().getString("message");
                if (!TextUtils.isEmpty(message)) {
                    TLog.info(TAG, "========NotifyDetailActivity======message:" + message);
                    Map<String, String> extrasMap = new HashMap<>();
                    try {
                        JSONObject extraJson = new JSONObject(message);
                        extrasMap.put(PushConstants.PUSH_MSG_TYPE, extraJson.optString(PushConstants.PUSH_MSG_TYPE));
                        extrasMap.put(PushConstants.PUSH_MSG_ID, extraJson.optString(PushConstants.PUSH_MSG_ID));
                        extrasMap.put(PushConstants.PUSH_MSG_FROM, extraJson.optString(PushConstants.PUSH_MSG_FROM));
                        extrasMap.put(PushConstants.PUSH_MSG_TO, extraJson.optString(PushConstants.PUSH_MSG_TO));
                        if (extraJson.has(PushConstants.PUSH_PROTOCOL)) {
                            extrasMap.put(PushConstants.PUSH_PROTOCOL, extraJson.optString(PushConstants.PUSH_PROTOCOL));
                        }
                        extrasMap.put("landing", extraJson.optString("landing"));
                    } catch (Exception e) {
                        TLog.error(TAG, e.getMessage());
                    }
                    TLog.info(TAG, (new Gson()).toJson(extrasMap));
                    PushSdkManager.getInstance().onNotificationMessageClicked(PushSdkConfig.oppoType, extrasMap);
                }
            } else if (isValidPush("hw_push")) { // 华为push
                message = getIntent().getData().getQueryParameter("message");
                if (!TextUtils.isEmpty(message)) {
                    TLog.info(TAG, "========NotifyDetailActivity======message:" + message);
                    Map<String, String> extrasMap = new HashMap<>();
                    try {
                        // [{"mtype":"1"},{"mid":"4506173615064781"},{"from":"8990BBBDF92CE9E12E4ACA06C30B8EBC1"},{"to":"9F85EC726F9F190CF78C2A1BE32268A01"}]
                        JSONArray extraJson = new JSONArray(URLDecoder.decode(message, "UTF-8"));
                        for (int i = 0; i < extraJson.length(); i++) {
                            String key = extraJson.getJSONObject(i).keys().next();
                            extrasMap.put(key, extraJson.getJSONObject(i).optString(key));
                        }
                    } catch (JSONException | UnsupportedEncodingException e) {
                        TLog.error(TAG, e.getMessage());
                    }
                    TLog.info(TAG, (new Gson()).toJson(extrasMap));
                    PushSdkManager.getInstance().onNotificationMessageClicked(PushSdkConfig.hwType, extrasMap);
                }
            } else if (isValidPush("honor_push")) { // 荣耀push
                if (getIntent().getData() != null) {
                    message = getIntent().getData().getQueryParameter("message");
                }
                if (!TextUtils.isEmpty(message)) {
                    TLog.info(TAG, "========NotifyDetailActivity======message:" + message);
                    // {"from":"8990BBBDF92CE9E12E4ACA06C30B8EBC1","mid":"4506173615064864","mtype":"1","to":"9F85EC726F9F190CF78C2A1BE32268A01"}
                    Map<String, String> extrasMap = new HashMap<>();
                    try {
                        JSONObject extraJson = new JSONObject(URLDecoder.decode(message, "UTF-8"));
                        Iterator<String> keys = extraJson.keys();
                        while (keys.hasNext()) {
                            String key = keys.next();
                            extrasMap.put(key, extraJson.optString(key));
                        }
                    } catch (JSONException | UnsupportedEncodingException e) {
                        TLog.error(TAG, e.getMessage());
                    }
                    TLog.info(TAG, (new Gson()).toJson(extrasMap));
                    PushSdkManager.getInstance().onNotificationMessageClicked(PushSdkConfig.honorType, extrasMap);
                }
            } else if (isValidPush("vivo_push")) { //vivo push
                // vivo push
                message = getIntent().getData().getQueryParameter("message");
                if (!TextUtils.isEmpty(message)) {
                    TLog.info(TAG, "========NotifyDetailActivity======message:" + message);
                    Map<String, String> extrasMap = new HashMap<>();
                    try {
                        JSONObject extraJson = new JSONObject(message);
                        extrasMap.put(PushConstants.PUSH_MSG_TYPE, extraJson.optString(PushConstants.PUSH_MSG_TYPE));
                        extrasMap.put(PushConstants.PUSH_MSG_ID, extraJson.optString(PushConstants.PUSH_MSG_ID));
                        extrasMap.put(PushConstants.PUSH_MSG_FROM, extraJson.optString(PushConstants.PUSH_MSG_FROM));
                        extrasMap.put(PushConstants.PUSH_MSG_TO, extraJson.optString(PushConstants.PUSH_MSG_TO));
                        if (extraJson.has(PushConstants.PUSH_PROTOCOL)) {
                            extrasMap.put(PushConstants.PUSH_PROTOCOL, extraJson.optString(PushConstants.PUSH_PROTOCOL));
                        }
                    } catch (JSONException e) {
                        TLog.error(TAG, e.getMessage());
                    }
                    TLog.info(TAG, (new Gson()).toJson(extrasMap));
                    PushSdkManager.getInstance().onNotificationMessageClicked(PushSdkConfig.vivoType, extrasMap);
                }
            }
        }
        finish();
    }

    private boolean isValidPush(String typeName) {
        return getIntent().getData() != null && StringUtils.isNotEmpty(getIntent().getData().getQueryParameter("message")) &&
                getIntent().getData().getPath() != null && getIntent().getData().getPath().contains(typeName);
    }
}
