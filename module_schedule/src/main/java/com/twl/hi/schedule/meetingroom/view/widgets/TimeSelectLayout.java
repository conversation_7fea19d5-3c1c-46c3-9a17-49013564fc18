package com.twl.hi.schedule.meetingroom.view.widgets;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.twl.hi.schedule.meetingroom.view.widgets.MeetingTimeSelect;


/**
 * <AUTHOR>
 * @date 2021/7/8.
 */
public class TimeSelectLayout extends FrameLayout {
    private MeetingTimeSelect.SelectRect rect;

    public TimeSelectLayout(@NonNull Context context) {
        super(context);
    }

    public TimeSelectLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public TimeSelectLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setRect(MeetingTimeSelect.SelectRect rect) {
        this.rect = rect;
    }

    public MeetingTimeSelect.SelectRect getRect() {
        return rect;
    }
}
