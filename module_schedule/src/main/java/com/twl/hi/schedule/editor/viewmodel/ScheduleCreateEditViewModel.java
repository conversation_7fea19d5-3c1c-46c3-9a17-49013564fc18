package com.twl.hi.schedule.editor.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.amap.api.services.core.PoiItem;
import com.google.gson.Gson;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.dialog.bottom.BottomSelectItemBean;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.BaseUpdateRequestCallback;
import com.twl.hi.foundation.api.request.ImageUploadRequestV2;
import com.twl.hi.foundation.api.request.VideoUploadRequest;
import com.twl.hi.foundation.api.response.ImageUploadResponse;
import com.twl.hi.foundation.api.response.VideoUploadResponse;
import com.twl.hi.foundation.api.response.bean.CalendarStyleEnum;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.foundation.logic.ContactService;
import com.twl.hi.foundation.logic.GroupService;
import com.twl.hi.foundation.logic.ScheduleService;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.CalendarGroupOptions;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.Group;
import com.twl.hi.foundation.model.GroupMember;
import com.twl.hi.foundation.model.Schedule;
import com.twl.hi.foundation.model.ScheduleFile;
import com.twl.hi.foundation.model.ScheduleLocation;
import com.twl.hi.foundation.model.ScheduleMeeting;
import com.twl.hi.foundation.model.ScheduleParticipant;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.foundation.utils.ScheduleUtils;
import com.twl.hi.schedule.R;
import com.twl.hi.schedule.detail.model.remote.GetScheduleWithCalendarCallback;
import com.twl.hi.schedule.detail.model.remote.GetScheduleWithCalendarRequest;
import com.twl.hi.schedule.detail.model.remote.GetScheduleWithCalendarResponse;
import com.twl.hi.schedule.editor.model.Avatar;
import com.twl.hi.schedule.editor.model.ScheduleDetailBusyTime;
import com.twl.hi.schedule.editor.model.remote.ScheduleOrInventoryAddRequest;
import com.twl.hi.schedule.editor.model.remote.ScheduleOrInventoryAddResponse;
import com.twl.hi.schedule.editor.model.remote.ScheduleOrInventoryUpdateRequest;
import com.twl.hi.schedule.editor.model.remote.ScheduleOrInventoryUpdateResponse;
import com.twl.hi.schedule.editor.view.ScheduleCreateEditActivity;
import com.twl.hi.schedule.freetime.model.ScheduleBusyTime;
import com.twl.hi.schedule.freetime.model.remote.ScheduleUserBusyRequest;
import com.twl.hi.schedule.freetime.model.remote.ScheduleUserBusyResponse;
import com.twl.hi.schedule.meetingroom.model.BestRoomBean;
import com.twl.hi.schedule.meetingroom.model.remote.BestMeetingRoomRequest;
import com.twl.hi.schedule.meetingroom.model.remote.BestMeetingRoomResponse;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.SettingBuilder;
import com.twl.utils.StringUtils;
import com.twl.utils.file.FileUtils;
import com.twl.utils.jurisdiction.JurisdictionUtils;
import com.zhihu.matisse.MimeType;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;

import hi.kernel.Constants;
import hi.kernel.HiKernel;
import lib.twl.common.photoselect.PhotoSelectManager;
import lib.twl.common.util.CalendarUtil;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.LList;
import lib.twl.common.util.ProcessHelper;
import lib.twl.common.util.lifecycle.NoStickLiveData;

public class ScheduleCreateEditViewModel extends FoundationViewModel {
    private static final String TAG = "ScheduleCreateEditViewModel";
    private static final int FILE_UPLOADING_DEFAULT = 0;
    public static final int FILE_UPLOADING = 1;
    private static final int FILE_UPLOADING_OVER = 2;

    // 参与人无变化
    public static final int CHANGE_STATE_NO_CHANGE = 0;
    // 新增了参与人或群组
    public static final int CHANGE_STATE_ADD = 1;
    // 删除了参与人或群组
    public static final int CHANGE_STATE_DEL = 2;
    // 新增又删除了参与人或群组
    public static final int CHANGE_STATE_ADD_AND_DEL = 3;

    // 时间、重复频率、地点、会议室、会议室用途未变化
    public static final int CHANGE_STATE_TIME_LOCATION_NO_CHANGE = 0;
    // 时间、重复频率、地点、会议室、会议室用途变化
    public static final int CHANGE_STATE_TIME_LOCATION_CHANGED = 1;
    public static final String MY_USER_ID = HiKernel.getHikernel().getAccount().getUserId();

    private int mFrom; //1:来自日程tab页，2：来自聊天长按，3：来自聊天页更多选项，11：从预约会议tab创建 打点需要,其他为-1
    private MutableLiveData<String> scheduleNotifyPeopleLiveData = new MutableLiveData<>();
    private MutableLiveData<List<Avatar>> participantAvatarsLiveData = new MutableLiveData<>();
    private MutableLiveData<ScheduleOrInventoryAddResponse> addLiveData = new MutableLiveData<>();
    private MutableLiveData<ScheduleOrInventoryUpdateResponse> updateLiveData = new MutableLiveData<>();
    private MutableLiveData<Schedule> mScheduleLiveData = new MutableLiveData<>();

    private int afterAll;
    private String currDate; //重复日程修改的日期，修改重复日程时必填
    private String mCalendarId;
    private String mDefaultCalendarId;
    private ObservableField<String> mDesc = new ObservableField<>();
    private ObservableField<String> mRoomName = new ObservableField<>(); //会议室名称
    private ObservableField<Boolean> mRepeatEmptyShow = new ObservableField<>(true);
    private ObservableField<Boolean> mAddressEmptyShow = new ObservableField<>(true);
    private Set<String> mVideoSuffix = new TreeSet<>();
    private Set<String> mPicSuffix = new TreeSet<>();
    private ObservableBoolean isUploading = new ObservableBoolean(false);
    private boolean mFileChanged = false;
    private MutableLiveData<Integer> mUploadStatus = new MutableLiveData<>(FILE_UPLOADING_DEFAULT);
    public ObservableField<String> input = new ObservableField<>();
    private List<BottomSelectItemBean> mRemindItemBeans;
    private boolean[] mRemindIndex = {false, false, false, true, false, false, false};
    private final ObservableField<String> mRemindValue = new ObservableField<>();
    private List<BottomSelectItemBean> mRepeatItemBeans;
    private final ObservableField<String> mRepeatValue = new ObservableField<>();
    private final ObservableField<String> mRepeatTimeValue = new ObservableField<>();
    private int mRepeatIndex;
    private final List<String> allIds = new ArrayList<>();
    private List<String> friendIds = new ArrayList<>();
    private List<String> groupIds = new ArrayList<>();
    //编辑前参与人列表
    private List<String> startFriendIds = new ArrayList<>();
    private List<String> startGroupIds = new ArrayList<>();
    //参与人是否有变化
    private boolean participantsChanged;
    //选择时间所需参数
    private float startTime;
    private float endTime;
    private Schedule mSchedule = new Schedule();  //修改后的数据

    private PoiItem addressLocation; // 已选中定位

    private Schedule mCurSchedule = new Schedule();  //修改前的数据
    public ObservableBoolean enableMeetingRoom = new ObservableBoolean(true);
    public ObservableBoolean showBestMeetingRoom = new ObservableBoolean(false);
    private MutableLiveData<BestRoomBean> bestRoomBeanTypeLiveData = new MutableLiveData<>();
    private MutableLiveData<List<ScheduleFile>> mFiles = new MutableLiveData<>();

    public int inviteNum; //最合适会议室人数
    private boolean allNoBusy; //是否全员空闲；
    private List<List<ScheduleDetailBusyTime>> busyTimes = new ArrayList<>();
    private ObservableField<String> busyTimeDesc = new ObservableField<>();
    private ObservableBoolean busyTimeHighlight = new ObservableBoolean();
    private ObservableBoolean lookBusy = new ObservableBoolean(false);
    private ObservableBoolean lookBusyIsUploading = new ObservableBoolean(false);
    private ObservableField<String> bestRoomMsg = new ObservableField<>();
    private ObservableBoolean showParticipantPerm = new ObservableBoolean(true);
    public ObservableBoolean hasAudioAndLED = new ObservableBoolean();
    private ObservableBoolean showMeetingRoomAccessControl = new ObservableBoolean();
    private ObservableBoolean videoMeetingVisible = new ObservableBoolean(false);

    private int mLastSelectedCalendarIndex;
    private List<CalendarGroupOptions.Entry> mCalendarGroupOptions;
    private List<BottomSelectItemBean> mSelectableCalendars;
    private ObservableField<String> mScheduleCalendarGroupName = new ObservableField<>();
    private MutableLiveData<CalendarStyleEnum> mCalendarStyle = new MutableLiveData<>(CalendarStyleEnum.BHScheduleColorTypeBrand);
    /**
     * 是否编辑日程后是否通知参与者
     */
    private boolean mIsNotifyParticipator = true;
    private String mChatId;
    private long mStartTimestamp;
    private final ObservableBoolean canJustInvite = new ObservableBoolean(false);
    private final ObservableBoolean canAddUser = new ObservableBoolean(true);
    private final ObservableBoolean canChangeCalendar = new ObservableBoolean(true);
    private boolean mIsShareBackToConversation = true;
    /**触发取消会议室的弹窗的事件*/
    private final NoStickLiveData<Boolean> mCancelMeetingRoomEvent = new NoStickLiveData<>();
    /**如果修改后的日程时间触发取消会议室弹窗，被用户拒绝，日程时间恢复到上一次设置的有效时间*/
    private String mValidMeetingRoomBeginTime = "";
    private String mValidMeetingRoomEndTime = "";
    private String mValidMeetingRoomStartTargetDate = "";

    private boolean mIsScheduleCreatedByMe = false;

    /**动态默认日程时间 修改结束时间时，该值会变为 结束时间和 开始时间的差值
     * 下一次修改开始时间时，结束时间会自动变化，使日程时长为该值
     * 单位: 分钟*/
    private long mDynamicDefaultDuration = 30 * 60 * 1000;
    public ScheduleCreateEditViewModel(Application application) {
        super(application);
        initSchedule();
        initAttachmentType();
        initRemindData();
        initRepeatData();
        mStartTimestamp = System.currentTimeMillis();
    }

    private void initSchedule() {
        // 视频会议的入口是否展示由服务端的功能可见性进行控制
        boolean enableVideoMeeting =
                SettingBuilder.getInstance().getJurisdictionVisible(JurisdictionUtils.SCHEDULE_VIDEO_MEETING_IDENTITY)
                        == JurisdictionUtils.IDENTITY_VISIBLE;
        TLog.info(TAG, "日程编辑页展示视频会议入口：" + enableVideoMeeting);
        videoMeetingVisible.set(enableVideoMeeting);
        mSchedule.setType(Constants.TYPE_SCHEDULE);
        mSchedule.setMeetingInfo(getDefaultScheduleMeeting());
        mCurSchedule.setMeetingInfo(getDefaultScheduleMeeting());
    }

    public LiveData<Boolean> getScheduleCalendarRequestResult() {
        return Transformations.map(
                ServiceManager.getInstance().getScheduleService().getCalendarGroupOptions(),
                options -> {
                    if (options == null) {
                        return false;
                    }

                    mDefaultCalendarId = options.defaultSelectedGroupId;
                    mCalendarGroupOptions = options.entries;
                    mSelectableCalendars = new ArrayList<>(mCalendarGroupOptions.size());
                    for (int i = 0; i < mCalendarGroupOptions.size(); i++) {
                        CalendarGroupOptions.Entry group = mCalendarGroupOptions.get(i);
                        BottomSelectItemBean bottomSelectItemBean = new BottomSelectItemBean(i, i, group.name, false);
                        bottomSelectItemBean.setColor(group.style.getCalendarColor());
                        mSelectableCalendars.add(bottomSelectItemBean);
                        if (options.defaultSelectedGroupId.equals(group.id)) {
                            mLastSelectedCalendarIndex = i;
                            mSchedule.setCalenderGroupId(group.id);
                            mCurSchedule.setCalenderGroupId(group.id); // 设置默认的选中日历
                            mScheduleCalendarGroupName.set(group.name);
                            mCalendarStyle.postValue(group.style);
                        }
                    }
                    return true;
                }
        );
    }

    private void initAttachmentType() {
        for (MimeType mimeType : MimeType.ofAll()) {
            if (mimeType.getMimeTypeName().startsWith(PhotoSelectManager.IMAGE)) {
                mPicSuffix.addAll(mimeType.getExtensions());
            } else {
                mVideoSuffix.addAll(mimeType.getExtensions());
            }
        }
    }

    private void initRepeatData() {
        mRepeatItemBeans = new ArrayList<>(7);
        String[] reminds = getResources().getStringArray(R.array.schedule_repeat_string_array);
        mRepeatItemBeans.add(new BottomSelectItemBean(0, 0, reminds[0], true));
        mRepeatItemBeans.add(new BottomSelectItemBean(1, 1, reminds[1], false));
        mRepeatItemBeans.add(new BottomSelectItemBean(2, 2, reminds[2], false));
        mRepeatItemBeans.add(new BottomSelectItemBean(3, 2, reminds[3], false));
        mRepeatItemBeans.add(new BottomSelectItemBean(4, 2, reminds[4], false));
        mRepeatItemBeans.add(new BottomSelectItemBean(5, 3, reminds[5], false));
        mRepeatItemBeans.add(new BottomSelectItemBean(6, 4, reminds[6], false));
        mRepeatValue.set(mRepeatItemBeans.get(0).getValue());
        mRepeatTimeValue.set(getResources().getString(R.string.never_stop));
    }

    private void initRemindData() {
        mRemindItemBeans = new ArrayList<>(7);
        String[] reminds = getResources().getStringArray(R.array.schedule_remind_string_array);
        mRemindItemBeans.add(new BottomSelectItemBean(0, -1, reminds[0], false));
        mRemindItemBeans.add(new BottomSelectItemBean(1, 0, reminds[1], false));
        mRemindItemBeans.add(new BottomSelectItemBean(2, 5, reminds[2], false));
        mRemindItemBeans.add(new BottomSelectItemBean(3, 15, reminds[3], false));
        mRemindItemBeans.add(new BottomSelectItemBean(4, 30, reminds[4], false));
        mRemindItemBeans.add(new BottomSelectItemBean(5, 60, reminds[5], false));
        mRemindItemBeans.add(new BottomSelectItemBean(6, 1440, reminds[6], false));
        String alert = ProcessHelper.getUserPreferences().getString(Constants.KEY_SCHEDULE_ALERT_MINS, "");
        if (TextUtils.isEmpty(alert)) {
            mRemindValue.set(mRemindItemBeans.get(3).getValue());
        } else {
            setRemindData(alert);
            mCurSchedule.setAlertMins(alert);
            mSchedule.setAlertMins(alert);
        }
    }

    @NotNull
    private ScheduleMeeting getDefaultScheduleMeeting() {
        ScheduleMeeting scheduleMeeting = new ScheduleMeeting();
        scheduleMeeting.setRoomName("");
        scheduleMeeting.setFloorName("");
        scheduleMeeting.setRoomId("");
        scheduleMeeting.setBookingId("");
        scheduleMeeting.setMeetingType(0);
        scheduleMeeting.setMeetingDesc("");
        return scheduleMeeting;
    }

    public PoiItem getAddressLocation() {
        return addressLocation;
    }

    public Schedule getSchedule() {
        return mSchedule;
    }

    public Schedule getCurSchedule() {
        return mCurSchedule;
    }

    public MutableLiveData<BestRoomBean> getBestRoomBeanTypeLiveData() {
        return bestRoomBeanTypeLiveData;
    }

    public ObservableField<String> getRepeatValue() {
        return mRepeatValue;
    }

    public ObservableField<String> getRepeatTimeValue() {
        return mRepeatTimeValue;
    }

    public void setRepeatTimeValue(String repeatTimeValue) {
        mRepeatTimeValue.set(repeatTimeValue);
    }

    public List<BottomSelectItemBean> getRepeatItemBeans() {
        return mRepeatItemBeans;
    }

    public ObservableField<String> getRemindValue() {
        return mRemindValue;
    }

    public ObservableField<String> getScheduleCalendarGroupValue() {
        return mScheduleCalendarGroupName;
    }

    public boolean[] getRemindIndex() {
        return mRemindIndex;
    }

    public List<BottomSelectItemBean> getRemindItemBeans() {
        return mRemindItemBeans;
    }

    public String getRemindString(boolean[] indexList) {
        mRemindIndex = indexList;
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder alertMins = new StringBuilder();
        boolean isMore = false;
        for (int i = 0; i < indexList.length; i++) {
            if (indexList[i]) {
                if (!isMore) {
                    isMore = true;
                } else {
                    stringBuilder.append("、");
                    alertMins.append(",");
                }
                stringBuilder.append(getRemindItemBeans().get(i).getValue());
                alertMins.append(getRemindItemBeans().get(i).getKey());
            }
        }
        mRemindValue.set(stringBuilder.toString());
        return alertMins.toString();
    }

    public void setRemindData(String alertMins) {
        String[] split = alertMins.split(",");
        Set<String> set = new HashSet<>();
        for (String s : split) {
            set.add(s.trim());
        }
        boolean isMore = false;
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < mRemindIndex.length; i++) {
            BottomSelectItemBean itemBean = mRemindItemBeans.get(i);
            mRemindIndex[i] = set.contains(String.valueOf(itemBean.getKey()));
            if (mRemindIndex[i]) {
                if (!isMore) {
                    isMore = true;
                } else {
                    stringBuilder.append("、");
                }
                stringBuilder.append(getRemindItemBeans().get(i).getValue());
            }
        }
        mRemindValue.set(stringBuilder.toString());
    }

    /**
     * 根据日程数据回填重复弹框数据
     *
     * @param schedule
     */
    public void setRepeatData(Schedule schedule) {
        if (schedule.getRepeatType() > 0) {
            //重复日程
            mRepeatItemBeans.get(0).setChecked(false);
            int checkIndex = 0;
            switch (schedule.getRepeatType()) {
                case 1:
                    checkIndex = 1;
                    break;
                case 2:
                    if (schedule.getFrequent() == 1) {
                        if (TextUtils.isEmpty(schedule.getRepeatDays())) {
                            checkIndex = 3;
                        } else {
                            checkIndex = 2;
                        }
                    } else {
                        checkIndex = 4;
                    }

                    break;
                case 3:
                    checkIndex = 5;
                    break;
                case 4:
                    checkIndex = 6;
                    break;
                default:
                    break;
            }
            mRepeatItemBeans.get(checkIndex).setChecked(true);
            mRepeatValue.set(mRepeatItemBeans.get(checkIndex).getValue());
            mRepeatIndex = checkIndex;
        }
    }

    public int getRepeatIndex() {
        return mRepeatIndex;
    }

    public void setRepeat(int position) {
        mRepeatIndex = position;
        BottomSelectItemBean itemBean = mRepeatItemBeans.get(position);
        mRepeatValue.set(itemBean.getValue());
        mSchedule.setRepeatType(itemBean.getKey());
        mSchedule.setFrequent(itemBean.getKey() > 0 ? 1 : 0);
        mSchedule.setRepeatDays("");
        if (position == 4) {
            //每两周
            mSchedule.setFrequent(2);
        } else if (position == 2) {
            //周一到周五
            mSchedule.setRepeatDays("1,2,3,4,5");
        }
    }

    public MutableLiveData<String> getScheduleNotifyPeopleLiveData() {
        return scheduleNotifyPeopleLiveData;
    }

    public MutableLiveData<List<Avatar>> getParticipantAvatarsLiveData() {
        return participantAvatarsLiveData;
    }

    public MutableLiveData<ScheduleOrInventoryAddResponse> getAddLiveData() {
        return addLiveData;
    }

    public MutableLiveData<ScheduleOrInventoryUpdateResponse> getUpdateLiveData() {
        return updateLiveData;
    }

    public void setNotifyPeople() {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                ContactService service = ServiceManager.getInstance().getContactService();
                List<Contact> cache = service.getContactsByIdsForCache(friendIds);
                GroupService groupService = ServiceManager.getInstance().getGroupService();
                List<Group> group = groupService.getOnlyGroupByIdWithExtendCache(toArray(groupIds));
                List<Avatar> avatars = new ArrayList<>(groupIds.size() + friendIds.size());
                for (Group item : group) {
                    avatars.add(new Avatar(
                            TextUtils.isEmpty(item.getAvatar()) ? item.getTinyAvatar() : item.getAvatar(),
                            TextUtils.isEmpty(item.getGroupRemark()) ? item.getGroupName() : item.getGroupRemark()
                    ));
                }
                for (Contact item : cache) {
                    avatars.add(new Avatar(
                            TextUtils.isEmpty(item.getTinyAvatar()) ? item.getAvatar() : item.getTinyAvatar(),
                            item.getStrAvatar()
                    ));
                }
                loadBestMeetingInfo();
                participantAvatarsLiveData.postValue(avatars);
            }
        });
    }

    public ObservableBoolean getShowParticipantPerm() {
        return showParticipantPerm;
    }

    public void setShowParticipantPerm(boolean showParticipantPerm) {
        this.showParticipantPerm.set(showParticipantPerm);
    }

    public boolean isScheduleCreatedByMe() {
        return mIsScheduleCreatedByMe;
    }

    public void setScheduleCreatedByMe(boolean createdByMe) {
        mIsScheduleCreatedByMe = createdByMe;
    }

    public LiveData<Schedule> getScheduleLiveData() {
        ExecutorFactory.execLocalTask(() -> {
            ScheduleService service = ServiceManager.getInstance().getScheduleService();
            Schedule schedule;
            if (TextUtils.isEmpty(mCalendarId)) {
                schedule = service.getScheduleWithFriend(mSchedule.getScheduleId());
            } else {
                schedule = service.getScheduleInCalendar(mSchedule.getScheduleId(), mCalendarId);
            }
            if (schedule != null) {
                if (schedule.getLinkMeetingType() == ScheduleConstant.MEETING_TYPE_BOSS_HI) {
                    schedule.setLinkMeetingUri("");
                }
                ScheduleUtils.clone(mCurSchedule, schedule);
                ScheduleUtils.clone(mSchedule, schedule);
                updateSelectionBySchedule(schedule);
                resetParticipants();
                mScheduleLiveData.postValue(schedule);
            } else {
                GetScheduleWithCalendarRequest request = new GetScheduleWithCalendarRequest(new GetScheduleWithCalendarCallback() {

                    @Override
                    public void onSuccess(ApiData<GetScheduleWithCalendarResponse> data) {
                        if (data.resp != null && data.resp.data != null) {
                            Schedule schedule = data.resp.data; //因为此处只查一个id，所以只返回一个数据
                            if (schedule.getLinkMeetingType() == ScheduleConstant.MEETING_TYPE_BOSS_HI) {
                                schedule.setLinkMeetingUri("");
                            }
                            ScheduleUtils.clone(mCurSchedule, schedule);
                            ScheduleUtils.clone(mSchedule, schedule);
                            updateSelectionBySchedule(schedule);
                            resetParticipants();
                            mScheduleLiveData.postValue(schedule);
                        }
                    }

                    @Override
                    public void onComplete() {
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                    }
                });
                request.scheduleId = mSchedule.getScheduleId();
                if (!TextUtils.isEmpty(mCalendarId)) {
                    request.calendarId = mCalendarId;
                }
                HttpExecutor.executeSync(request);
            }
        });
        return mScheduleLiveData;
    }

    /**
     * 根据已有的日历的日程分组设置界面展示的选中分组效果
     *
     * @param schedule 待编辑的日程
     */
    private void updateSelectionBySchedule(Schedule schedule) {
        if (mCalendarGroupOptions == null) {
            return;
        }

        String calenderGroupId = mCalendarId == null ? schedule.getCalenderGroupId() : mCalendarId;
        for (int i = 0; i < mCalendarGroupOptions.size(); i++) {
            CalendarGroupOptions.Entry entry = mCalendarGroupOptions.get(i);
            if (entry.id.equals(calenderGroupId)) {
                mLastSelectedCalendarIndex = i;
                mScheduleCalendarGroupName.set(entry.name);
                mCalendarStyle.postValue(entry.style);
                break;
            }
        }
    }

    private void resetParticipants() {
        boolean isManager = mSchedule.isManager() == 1 || TextUtils.equals(mSchedule.getCreatorId(), MY_USER_ID);
        canChangeCalendar.set(isManager);
        boolean canModify = mSchedule.getPartnerModify() == 1;
        boolean canShare = mSchedule.getPartnerShare() == 1;
        canAddUser.set(isManager || canModify || canShare);

        List<String> friendIds = new ArrayList<>();
        List<String> groupIds = new ArrayList<>();
        for (ScheduleParticipant partner : mSchedule.getPartners()) {
            if (TextUtils.equals(partner.getPartnerId(), mSchedule.getCreatorId())) {
                friendIds.add(0, partner.getPartnerId());
            } else if (partner.getPartnerType() == 1) {
                friendIds.add(partner.getPartnerId());
            } else if (partner.getPartnerType() == 2) {
                groupIds.add(partner.getPartnerId());
            }
        }

        startFriendIds.clear();
        startFriendIds.addAll(friendIds);
        startGroupIds.clear();
        startGroupIds.addAll(groupIds);

        setParticipantIds(friendIds, groupIds);
        setNotifyPeople();
    }

    public void addScheduleRequest(Schedule schedule) {
        ExecutorFactory.execWorkTask(() -> addScheduleRequest(schedule, groupIds));
    }

    public void addScheduleRequest(Schedule schedule, List<String> groupEncIds) {

        ScheduleOrInventoryAddRequest req = new ScheduleOrInventoryAddRequest(
                new BaseApiRequestCallback<ScheduleOrInventoryAddResponse>() {

                    @Override
                    protected boolean isFocusOnNetworkError() {
                        return true;
                    }

                    @Override
                    public void onSuccess(ApiData<ScheduleOrInventoryAddResponse> data) {
                        addLiveData.setValue(data.resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        addLiveData.setValue(null);
                    }
                });

        req.content = schedule.getContent();
        req.targetDate = schedule.getTargetDate();

        // 1:单/群聊点击对话框的「新建日程」;
        // 2:单/群聊点击右键消息的「日程」(移动端为长按行为);
        // 3:单/群聊点击消息蓝色时间「新建日程」;
        // 4:从「个人卡片-查看日程」中点击「新建日程」;
        // 5:日历中选取时间段新建;
        // 6:日历中点击「+」号
        // 7:从会议室选择时间区间「新建日程」
        switch (mFrom) {
            case Constants.CREATE_FROM_SINGLE_CHAT_MORE:
            case Constants.CREATE_FROM_GROUP_CHAT_MORE:
                req.source = 1;
                break;
            case Constants.CREATE_FROM_SINGLE_CHAT:
            case Constants.CREATE_FROM_GROUP_CHAT:
                req.source = 2;
                break;
            case Constants.CREATE_FROM_SINGLE_CHAT_TIME:
            case Constants.CREATE_FROM_GROUP_CHAT_TIME:
                req.source = 3;
                break;
            case Constants.CREATE_FROM_USER_INFO:
                req.source = 4;
                break;
            case Constants.CREATE_FROM_TIME_SELECTION:
                req.source = 5;
                break;
            case Constants.CREATE_FROM_MEETING_ROOM:
                req.source = 7;
                break;
            case Constants.CREATE_FROM_REVERSE_MEETING_ROOM_TAB:
                req.source = 9;
                break;
            case Constants.CREATE_FROM_CHAT_SETTING_SINGLE:
                req.source = 10;
                break;
            case Constants.CREATE_FROM_CHAT_SETTING_GROUP:
            case Constants.CREATE_FROM_CHAT_SETTING_GROUP_ONLY_PARTICI:
                req.source = 11;
                break;
            default:
                req.source = 6;
                break;
        }

        req.groupIds = TextUtils.join(",", groupEncIds);
        req.userIds = ScheduleUtils.convert(friendIds);

        if (schedule.getMeetingInfo() != null && !TextUtils.isEmpty(schedule.getMeetingInfo().getRoomId())) {
            req.roomId = schedule.getMeetingInfo().getRoomId();
        }
        if (schedule.getType() == 1) {
            req.beginTime = schedule.getBeginTime();
            req.endTime = schedule.getEndTime();
        }
        if (!TextUtils.isEmpty(schedule.getAlertMins())) {
            req.alertMins = schedule.getAlertMins();
        }
        req.repeatType = schedule.getRepeatType();
        if (!TextUtils.isEmpty(schedule.getRepeatDays())) {
            req.repeatDays = schedule.getRepeatDays();
        }
        if (schedule.getFrequent() > 0) {
            req.frequent = String.valueOf(schedule.getFrequent());
        }
        if (!TextUtils.isEmpty(schedule.getRepeatEnd())) {
            req.repeatEnd = schedule.getRepeatEnd();
        }
        if (!TextUtils.isEmpty(getDesc().get())) {
            req.desc = getDesc().get();
        }
        List<ScheduleFile> files = getFiles().getValue();
        if (!LList.isEmpty(files)) {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < files.size(); i++) {
                stringBuilder.append(files.get(i).url);
                if (i != files.size() - 1) {
                    stringBuilder.append(",");
                }
            }
            req.fileUrls = stringBuilder.toString();
        }
        req.partnerModify = schedule.getPartnerModify();
        req.partnerShare = schedule.getPartnerShare();
        ScheduleLocation location = schedule.getLocation();
        req.locationName = location == null ? "" : location.getName();
        req.locationAddr = location == null ? "" : location.getAddr();
        req.lng = location == null ? "" : String.valueOf(location.getLng());
        req.lat = location == null ? "" : String.valueOf(location.getLat());
        req.calenderGroupId = schedule.getCalenderGroupId();
        req.push = mIsNotifyParticipator ? "1" : "0";

        if (mIsShareBackToConversation && StringUtils.isNotEmpty(mChatId)) {
            req.chatId = mChatId;
            switch (mFrom) {
                case Constants.CREATE_FROM_SINGLE_CHAT:
                case Constants.CREATE_FROM_SINGLE_CHAT_TIME:
                case Constants.CREATE_FROM_SINGLE_CHAT_MORE:
                case Constants.CREATE_FROM_CHAT_SETTING_SINGLE:
                    req.chatType = "1";
                    break;
                case Constants.CREATE_FROM_GROUP_CHAT:
                case Constants.CREATE_FROM_GROUP_CALENDAR:
                case Constants.CREATE_FROM_GROUP_CHAT_TIME:
                case Constants.CREATE_FROM_GROUP_CHAT_MORE:
                case Constants.CREATE_FROM_CHAT_SETTING_GROUP:
                case Constants.CREATE_FROM_CHAT_SETTING_GROUP_ONLY_PARTICI:
                    req.chatType = "2";
                    break;
            }
        }
        req.linkMeetingType = schedule.getLinkMeetingType();
        req.linkMeetingUri = schedule.getLinkMeetingType() == ScheduleConstant.MEETING_TYPE_THIRD_PARTY ? schedule.getLinkMeetingUri() : "";

        HttpExecutor.execute(req);

        /*
         * {¶
         * "content":"内容"¶
         * "targetDate":"日期"¶
         * "beginTime":"开始时间"¶
         * "endTime":"结束时间"¶
         * "userIds":"日程参与者用户ids列表"¶
         * "groupIds":"项目组加密id"¶
         * "calGroupId":"公共日历id"¶
         * "source":"请求来源，1. 日程tab点击+; 2. 聊天页长按添加日程; 3. 聊天页+号添加; 4 预定会议室添加"¶
         * "roomId":"会议室ID"¶
         * "meetingType":"会议室用途；1-员工访谈, 2-面试, 3-答辩述职,4-培训,5-新人入职,6-访客洽谈,7-例会,8-评审,9-其他"¶
         * "repeatType":"重复类型，0-不重复，1-天，2-周，3-月，4-年"¶
         * "frequent":"重复频率，如repeatType为2时，frequent为2表示每两周"¶
         * "repeatDays":"指定重复时间，英文逗号分隔。每周时，表示星期几；每月时表示几号；"¶
         * "repeatEnd":"日程重复截止日期，无截止日期不传"¶
         * "alertMins":"提醒时间，-1: 不提醒，0: 实时提醒，5: 五分钟前，1440: 一天前，以分钟计数，默认为15"¶
         * "alertPoints":"全天提醒时间，-1:不提醒，1:当天08:00，2:当天09:00，3:当天10:00, 4:提前1天08:00，5:提前1天09:00，6:提前1天10:00 "¶
         * "desc":"日程描述"¶
         * "fileUrls":"附件url列表，英文逗号分隔"¶
         * "partnerModify":"是否允许参与人修改日程，1-允许 0-不允许"¶
         * "locationName":"地图上的地点建筑名称"¶
         * "locationAddr":"地图上的地点具体地址"¶
         * "linkMeetingType":"1- bosshi 视频会议 ，2- 三方视频会议"¶
         * "linkMeetingUri":"2- 三方视频会议 填写会议链接地址"¶
         * "calenderGroupId":"日程分组Id"¶
         * "chatId":"会话Id"¶
         * "push":"0-不通知 1-通知 （默认通知）"¶
         * "chatType":"消息来源用户聊天类型：1-单聊，2-群聊"¶
         * "partnerShare":"是否允许参与者分享日程 1-是 0-否"¶
         * }
         */
        Map<String, String> data = new HashMap<>();
        data.put("content", hidden(req.content));
        data.put("targetDate", req.targetDate);
        data.put("beginTime", req.beginTime);
        data.put("endTime", req.endTime);
        data.put("userIds", req.userIds);
        data.put("groupIds", req.groupIds);
        data.put("source", String.valueOf(req.source));
        data.put("roomId", req.roomId);
        data.put("meetingType", String.valueOf(req.meetingType));
        data.put("frequent", req.frequent);
        data.put("repeatDays", req.repeatDays);
        data.put("repeatEnd", req.repeatEnd);
        data.put("alertMins", req.alertMins);
        data.put("desc", hidden(req.desc));
        data.put("fileUrls", hidden(req.fileUrls));
        data.put("partnerModify", String.valueOf(req.partnerModify));
        data.put("locationName", hidden(req.locationName));
        data.put("locationAddr", hidden(req.locationAddr));
        data.put("linkMeetingType", String.valueOf(req.linkMeetingType));
        data.put("linkMeetingUri", hidden(req.linkMeetingUri));
        data.put("calenderGroupId", req.calenderGroupId);
        data.put("chatId", req.chatId);
        data.put("chatType", req.chatType);
        data.put("push", req.push);
        data.put("duration", String.valueOf(System.currentTimeMillis() - mStartTimestamp));
        data.put("partnerShare", req.partnerShare + "");
        new PointUtils.BuilderV4()
                .name("calendar-new-submit")
                .params("content", new Gson().toJson(data))
                .point();
    }

    private String hidden(String rawData) {
        return TextUtils.isEmpty(rawData) ? "N" : "Y";
    }

    public void updateScheduleRequest(Schedule schedule) {
        ExecutorFactory.execWorkTask(() ->
                updateScheduleRequest(
                        schedule,
                        groupIds)
        );
    }

    /**
     * 更新日程
     */
    public void updateScheduleRequest(Schedule schedule, List<String> groupEncIds) {

        ScheduleOrInventoryUpdateRequest req = new ScheduleOrInventoryUpdateRequest(new BaseApiRequestCallback<ScheduleOrInventoryUpdateResponse>() {

            @Override
            protected boolean isFocusOnNetworkError() {
                return true;
            }

            @Override
            public void onSuccess(ApiData<ScheduleOrInventoryUpdateResponse> data) {
                updateLiveData.setValue(data.resp);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                updateLiveData.setValue(null);
            }
        });
        req.scheduleId = schedule.getScheduleId();
        req.content = schedule.getContent();
        req.targetDate = schedule.getTargetDate();

        req.userIds = ScheduleUtils.convert(friendIds);
        req.groupIds = TextUtils.join(",", groupEncIds);

        if (schedule.getMeetingInfo() != null && !TextUtils.isEmpty(schedule.getMeetingInfo().getRoomId())) {
            req.roomId = schedule.getMeetingInfo().getRoomId();
        }
        if (schedule.getType() == 1) {
            req.beginTime = schedule.getBeginTime();
        }
        req.endTime = schedule.getEndTime();
        if (!TextUtils.isEmpty(schedule.getAlertMins())) {
            req.alertMins = schedule.getAlertMins();
        }
        req.repeatType = schedule.getRepeatType();
        if (!TextUtils.isEmpty(schedule.getRepeatDays())) {
            req.repeatDays = schedule.getRepeatDays();
        }
        if (schedule.getFrequent() > 0) {
            req.frequent = String.valueOf(schedule.getFrequent());
        }
        if (!TextUtils.isEmpty(schedule.getRepeatEnd())) {
            req.repeatEnd = schedule.getRepeatEnd();
        }
        req.afterAll = afterAll;
        if (!TextUtils.isEmpty(currDate)) {
            req.updateDate = currDate;
        }
        req.desc = getDesc().get();
        StringBuilder stringBuilder = new StringBuilder("");
        List<ScheduleFile> files = getFiles().getValue();
        if (!LList.isEmpty(files)) {
            for (int i = 0; i < files.size(); i++) {
                stringBuilder.append(files.get(i).url);
                if (i != files.size() - 1) {
                    stringBuilder.append(",");
                }
            }
        }
        req.fileUrls = stringBuilder.toString();
        req.partnerModify = schedule.getPartnerModify();
        req.partnerShare = schedule.getPartnerShare();

        ScheduleLocation location = schedule.getLocation();
        req.locationName = location == null ? "" : location.getName();
        req.locationAddr = location == null ? "" : location.getAddr();
        req.lng = location == null ? "" : String.valueOf(location.getLng());
        req.lat = location == null ? "" : String.valueOf(location.getLat());
        req.calenderGroupId = schedule.getCalenderGroupId();
        req.fromSelfCalendar = mCalendarId == null || Objects.equals(mCalendarId, mDefaultCalendarId) ? 1 : 0;
        req.linkMeetingType = schedule.getLinkMeetingType();
        req.linkMeetingUri = schedule.getLinkMeetingType() == ScheduleConstant.MEETING_TYPE_THIRD_PARTY ? schedule.getLinkMeetingUri() : "";
        req.updatePush = mIsNotifyParticipator ? "1" : "0";

        HttpExecutor.execute(req);
    }

    public void setFrom(int from) {
        mFrom = from;
    }

    public int getFrom() {
        return mFrom;
    }

    public boolean shouldShowShareBackToConversationOption() {
        return mFrom == Constants.CREATE_FROM_SINGLE_CHAT_MORE
                || mFrom == Constants.CREATE_FROM_GROUP_CHAT_MORE
                || mFrom == Constants.CREATE_FROM_GROUP_CHAT
                || mFrom == Constants.CREATE_FROM_SINGLE_CHAT
                || mFrom == Constants.CREATE_FROM_SINGLE_CHAT_TIME
                || mFrom == Constants.CREATE_FROM_CHAT_SETTING_GROUP
                || mFrom == Constants.CREATE_FROM_CHAT_SETTING_SINGLE
                || mFrom == Constants.CREATE_FROM_CHAT_SETTING_GROUP_ONLY_PARTICI
                || mFrom == Constants.CREATE_FROM_GROUP_CHAT_TIME;
    }

    public int getAfterAll() {
        return afterAll;
    }

    public void setAfterAll(int afterAll) {
        this.afterAll = afterAll;
    }

    public String getCurrDate() {
        return currDate;
    }

    public void setCurrDate(String currDate) {
        this.currDate = currDate;
    }

    public void setCalendarId(String calendarId) {
        mCalendarId = calendarId;
    }

    public ObservableField<String> getDesc() {
        return mDesc;
    }

    public void setDesc(String desc) {
        this.mDesc.set(desc);
    }

    public ObservableField<String> getRoomName() {
        return mRoomName;
    }

    public void setRoomName(String roomName) {
        this.mRoomName.set(roomName);
    }

    public LiveData<List<ScheduleFile>> getFiles() {
        return mFiles;
    }

    public int getFileCount() {
        if (!LList.isEmpty(mFiles.getValue())) {
            return mFiles.getValue().size();
        }
        return 0;
    }

    public void setFiles(List<ScheduleFile> files) {
        this.mFiles.postValue(files);
    }

    public void addFile(ScheduleFile file) {
        List<ScheduleFile> list;
        if (LList.isEmpty(mFiles.getValue())) {
            list = new ArrayList<>();
        } else {
            list = new ArrayList<>(mFiles.getValue());
        }
        mFileChanged = true;
        list.add(file);
        setFiles(list);
    }

    public void removeFile(ScheduleFile file) {
        List<ScheduleFile> list;
        if (LList.isEmpty(mFiles.getValue())) {
            list = new ArrayList<>();
        } else {
            list = new ArrayList<>(mFiles.getValue());
        }
        mFileChanged = true;
        list.remove(file);
        setFiles(list);
    }


    public ObservableField<Boolean> getAddressEmptyShow() {
        return mAddressEmptyShow;
    }

    public void setAddressEmptyShow(boolean addressEmptyShow) {
        this.mAddressEmptyShow.set(addressEmptyShow);
    }

    public ObservableField<Boolean> getRepeatEmptyShow() {
        return mRepeatEmptyShow;
    }

    public void setRepeatEmptyShow(boolean repeatEmptyShow) {
        this.mRepeatEmptyShow.set(repeatEmptyShow);
    }

    public String checkVideoOrPic(String endName) {
        if (mPicSuffix.contains(endName)) {
            return PhotoSelectManager.IMAGE;
        } else if (mVideoSuffix.contains(endName)) {
            return PhotoSelectManager.VIDEO;
        }
        return "";
    }

    private void updateUploadStatus(int status) {
        if (status == FILE_UPLOADING_OVER || status == FILE_UPLOADING_DEFAULT) {
            setIsUploading(false);
        } else {
            setIsUploading(true);
        }
        mUploadStatus.setValue(status);
    }

    public void uploadFiles(final List<File> files) {
        if (files.size() > 0) {
            List<ScheduleFile> scheduleFiles = getFiles().getValue();
            if (!LList.isEmpty(scheduleFiles) && scheduleFiles.size() >= ScheduleCreateEditActivity.MAX_FILE_COUNT) {
                updateUploadStatus(FILE_UPLOADING_OVER);
                return;
            }
            updateUploadStatus(FILE_UPLOADING);
            File file = files.remove(0);
            String mimeType = checkVideoOrPic(FileUtils.getFileSuffix(file));
            if (TextUtils.equals(mimeType, PhotoSelectManager.VIDEO)) {
                uploadVideo(file, files);
            } else {
                uploadPic(file, files);
            }
        } else {
            updateUploadStatus(FILE_UPLOADING_OVER);
        }
    }

    private void uploadVideo(File file, final List<File> files) {
        int[] info = new int[2];
        String thumbnail = FileUtils.getVideoThumbnail(getApplication(), file.getAbsolutePath(), info);
        if (TextUtils.isEmpty(thumbnail)) {
            return;
        }
        int duration = FileUtils.getVideoDuration("file://" + file.getAbsolutePath());
        ScheduleFile scheduleFile = new ScheduleFile();
        final String[] ext = new String[1];
        ImageUploadRequestV2 thumbRequest = new ImageUploadRequestV2(new BaseUpdateRequestCallback<ImageUploadResponse>() {
            @Override
            public void handleInChildThread(ApiData<ImageUploadResponse> data) {
                super.handleInChildThread(data);
                scheduleFile.category = Constants.CATEGORY_VIDEO;
                scheduleFile.name = file.getName();
                scheduleFile.size = (int) file.length();
                scheduleFile.width = data.resp.originImage.width;
                scheduleFile.height = data.resp.originImage.height;
                scheduleFile.url = data.resp.originImage.url;
                scheduleFile.tinyWidth = data.resp.tinyImage.width;
                scheduleFile.tinyHeight = data.resp.tinyImage.height;
                scheduleFile.tinyUrl = data.resp.tinyImage.url;
                scheduleFile.localPath = file.getAbsolutePath();
                scheduleFile.ext = ext[0];
                VideoUploadRequest request = new VideoUploadRequest(new BaseUpdateRequestCallback<VideoUploadResponse>() {
                    @Override
                    public void handleInChildThread(ApiData<VideoUploadResponse> data) {
                        super.handleInChildThread(data);
                        VideoUploadResponse response = data.resp;
                        if (response != null) {
                            scheduleFile.url = response.url;
                            addFile(scheduleFile);
                        }
                    }

                    @Override
                    public void handleErrorInChildThread(ErrorReason reason) {
                        super.handleErrorInChildThread(reason);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        uploadFiles(files);
                    }
                });
                request.file = file;
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.putOpt(Constants.DURATION, duration);
                    jsonObject.putOpt(Constants.COVER_WIDTH, info[0]);
                    jsonObject.putOpt(Constants.COVER_HEIGHT, info[1]);
                    jsonObject.putOpt(Constants.COVER_URL, data.resp.tinyImage.url);
                } catch (Exception e) {
                    TLog.error(TAG, e.getMessage());
                }
                if (!TextUtils.isEmpty(jsonObject.toString())) {
                    ext[0] = jsonObject.toString();
                    request.ext = ext[0];
                }
                HttpExecutor.execute(request);
            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                uploadFiles(files);
            }
        });
        thumbRequest.file = new File(thumbnail.replace("file://", ""));
        HttpExecutor.execute(thumbRequest);
    }

    private void uploadPic(File file, final List<File> files) {
        ImageUploadRequestV2 thumbRequest = new ImageUploadRequestV2(new BaseUpdateRequestCallback<ImageUploadResponse>() {
            @Override
            public void handleInChildThread(ApiData<ImageUploadResponse> data) {
                super.handleInChildThread(data);
                ScheduleFile scheduleFile = new ScheduleFile();
                scheduleFile.category = Constants.CATEGORY_PIC;
                scheduleFile.name = file.getName();
                scheduleFile.size = (int) file.length();
                scheduleFile.width = data.resp.originImage.width;
                scheduleFile.height = data.resp.originImage.height;
                scheduleFile.url = data.resp.originImage.url;
                scheduleFile.tinyWidth = data.resp.tinyImage.width;
                scheduleFile.tinyHeight = data.resp.tinyImage.height;
                scheduleFile.tinyUrl = data.resp.tinyImage.url;
                scheduleFile.localPath = file.getAbsolutePath();
                addFile(scheduleFile);
            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                uploadFiles(files);
            }
        });
        thumbRequest.file = file;
        HttpExecutor.execute(thumbRequest);
    }

    public ObservableBoolean getIsUploading() {
        return isUploading;
    }

    public void setIsUploading(boolean isUploading) {
        this.isUploading.set(isUploading);
    }

    public MutableLiveData<Integer> getUploadStatus() {
        return mUploadStatus;
    }

    public List<String> getAllIds() {
        return allIds;
    }

    public List<String> getFriendIds() {
        return friendIds;
    }

    public boolean isNoParticipant() {
        return getFriendIds().size() <= 0 && getGroupIds().size() <= 0;
    }

    public boolean isParticipantNotOnlyMe() {
        return friendIds.contains(MY_USER_ID)
                ? friendIds.size() > 1
                : friendIds.size() > 0;
    }

    public boolean isParticipantOnlyMe() {
        List<String> currentFriends = new ArrayList<>(friendIds);
        return currentFriends.size() == 1 && TextUtils.equals(currentFriends.get(0), MY_USER_ID);
    }

    public List<String> getGroupIds() {
        return groupIds;
    }

    public void setParticipantIds(List<String> friendIds, List<String> groupIds) {
        if (this.friendIds.equals(friendIds) && this.groupIds.equals(groupIds)) {
            return;
        }

        this.friendIds = friendIds;
        this.groupIds = groupIds;
        participantsChanged = !startFriendIds.equals(friendIds) || !startGroupIds.equals(groupIds);

        ExecutorFactory.execWorkTask(() -> {
            List<GroupMember> members = ServiceManager.getInstance().getGroupService().getMultiGroupMembersById(toArray(groupIds), toArray(friendIds));
            List<String> ids = new ArrayList<>(friendIds.size() + members.size());
            ids.addAll(friendIds);
            for (GroupMember member : members) {
                ids.add(member.getUserId());
            }
            allIds.clear();
            allIds.addAll(ids);
            inviteNum = allIds.size();

            if (ScheduleUtils.isAllDaySchedule(getSchedule())) {
                setLookBusy(false);
            } else {
                if (getSchedule().getRepeatType() == 0) {
                    if (allIds.size() > 0) {
                        setLookBusy(true);
                        requestQueryBusyTime();
                    } else {
                        setLookBusy(false);
                    }
                }
            }
        });
    }

    public float getStartTime() {
        return startTime;
    }

    public float getEndTime() {
        return endTime;
    }

    public void setSelectTime(Schedule schedule) {
        startTime = ScheduleUtils.serverTime2TimeIdx(schedule.getBeginTime());
        endTime = ScheduleUtils.serverTime2TimeIdx(schedule.getEndTime());
        resetBusyTimeDesc();
    }

    public void setSelectTime(float startTime, float endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
        resetBusyTimeDesc();
    }

    /**
     * 除了附件上传时，均可点击
     * @return
     */
    public boolean saveEnable() {
        if (isUploading.get()) { //附件上传中不可点击
            return false;
        }
        return true;
    }

    public void loadBestMeetingInfo() {
        setBestRoomMsg("");
        enableMeetingRoom.set(true);
        String targetDate = mSchedule.getTargetDate();
        if (TextUtils.isEmpty(targetDate)) { //没有预定日期，不用查询会议室匹配
            posBestMeetingRoom(null);
            return;
        }
        if (mSchedule.getRepeatType() > 0) {
            posBestMeetingRoom(null);
            return;
        }
        if (SettingBuilder.getInstance().getJurisdictionVisible(JurisdictionUtils.CREATE_MEETING_IDENTITY) != JurisdictionUtils.IDENTITY_VISIBLE) {
            posBestMeetingRoom(null);
            return;
        }
        Calendar calendar = ScheduleUtils.getCalendarByTargetDate(targetDate);
        int year = calendar.get(java.util.Calendar.YEAR);
        int month = calendar.get(java.util.Calendar.MONTH) + 1;
        int day = calendar.get(java.util.Calendar.DAY_OF_MONTH);
        int status = CalendarUtil.compareToToday(year, month, day); //0 同一天,正数 calendar大于今天,负数 calendar小于今天
        if (status < 0) { //小于今天，不用查询会议室匹配
            //如果已经预定过会议室，就弹取消弹窗
            handleExpiredTime();
        } else if (status == 0) {
            String beginTime = mSchedule.getBeginTime();
            if (TextUtils.isEmpty(beginTime)) {
                posBestMeetingRoom(null);
            } else {
                Calendar endCalendar = ScheduleUtils.getCalendarByTime(beginTime);
                int beginHour = endCalendar.get(Calendar.HOUR_OF_DAY);
                int beginMinute = endCalendar.get(Calendar.MINUTE);
                float beginIndex = beginHour + beginMinute / 60f ;
                Calendar currentCalendar = Calendar.getInstance();
                int currentHour = currentCalendar.get(Calendar.HOUR_OF_DAY);
                int currentMinute = currentCalendar.get(Calendar.MINUTE); //有可能大于半小时
                float currentIndex = currentHour + currentMinute / 60f;
                if (beginIndex >= currentIndex) {
                    requestBestMeetingRoom();
                } else {
                    //如果已经预定过会议室，就弹取消弹窗
                    handleExpiredTime();
                }
            }
        } else {
            requestBestMeetingRoom();
        }
    }

    private void handleExpiredTime() {
        if (!TextUtils.isEmpty(mSchedule.getRoomId())) {
            mCancelMeetingRoomEvent.postValue(true);
        } else {
            posBestMeetingRoom(null);
            enableMeetingRoom.set(false);
            bestRoomMsg.set(getResources().getString(R.string.schedule_meeting_room_time_past));
        }
    }

    public void requestBestMeetingRoom() {
        BestMeetingRoomRequest request = new BestMeetingRoomRequest(new BaseApiRequestCallback<BestMeetingRoomResponse>() {

            @Override
            public void onSuccess(ApiData<BestMeetingRoomResponse> data) {
                BestMeetingRoomResponse response = data.resp;
                if (response == null) {
                    return;
                }
                List<BestRoomBean> result = response.result;
                //设置推荐会议室展示的逻辑
                if (LList.isEmpty(result)) {
                    //没有推荐会议室
                    posBestMeetingRoom(null);
                    bestRoomMsg.set(response.tips);
                    enableMeetingRoom.set(TextUtils.isEmpty(response.tips));
                } else if (!TextUtils.isEmpty(mSchedule.getRoomId())) {
                    //当前已有选中的会议室
                    posBestMeetingRoom(null);
                    enableMeetingRoom.set(true);
                } else {
                    posBestMeetingRoom(result.get(0));
                    enableMeetingRoom.set(true);
                }

                if (!TextUtils.isEmpty(mSchedule.getRoomId())) {
                    /*如果当前的选中的会议室不再可用的逻辑
                      需要弹出取消会议弹窗
                     */
                    if (isMeetingRoomContained(result, mSchedule.getRoomId()) < 0) {
                        mCancelMeetingRoomEvent.postValue(true);
                    } else { //说明当前时间符合会议室的要求
                        mValidMeetingRoomBeginTime = mSchedule.getBeginTime();
                        mValidMeetingRoomEndTime = mSchedule.getEndTime();
                        mValidMeetingRoomStartTargetDate = mSchedule.getTargetDate();
                    }
                }

            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                posBestMeetingRoom(null);
            }
        });
        request.targetDate = mSchedule.getTargetDate();
        //没有参与人按照一个人推荐会议室
        request.num = getInviteMeetingNum();
        if (!TextUtils.isEmpty(mSchedule.getBeginTime())) {
            request.startTime = mSchedule.getBeginTime();
        }
        if (!TextUtils.isEmpty(mSchedule.getEndTime())) {
            request.endTime = mSchedule.getEndTime();
        }
        if (!TextUtils.isEmpty(mCurSchedule.getBookingId())) {
            request.excludeBookingId = mCurSchedule.getBookingId();
        }
        HttpExecutor.execute(request);
    }

    /**
     * 是否包含该会议室
     */
    private int isMeetingRoomContained(List<BestRoomBean> rooms, String roomId) {
        for (int i = 0; i < rooms.size(); i++) {
            if (TextUtils.equals(rooms.get(i).roomId, roomId)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 没有参与人按照一个人推荐会议室
     *
     * @return 获取推荐会议室参与人数
     */
    public int getInviteMeetingNum() {
        return inviteNum == 0 ? 1 : inviteNum;
    }

    public void cleanBestMeetingRoom() {
        posBestMeetingRoom(null);
    }

    private void posBestMeetingRoom(BestRoomBean o) {
        bestRoomBeanTypeLiveData.postValue(o);
    }

    /**
     * 查询参与人闲忙
     */
    public void requestQueryBusyTime() {
        if (!lookBusy.get() || allIds.isEmpty()) {
            return;
        }
        setBusyTimeDesc(getResources().getString(R.string.schedule_look_actor_busying));
        setLookBusyIsUploading(true);
        ScheduleUserBusyRequest request = new ScheduleUserBusyRequest(new BaseApiRequestCallback<ScheduleUserBusyResponse>() {
            @Override
            public void handleInChildThread(ApiData<ScheduleUserBusyResponse> data) {
                List<ScheduleBusyTime> totalResult = data.resp.totalResult;
                List<ScheduleUserBusyResponse.ScheduleUserBusy> userResult = data.resp.userResult;
                busyTimes.clear();
                if (LList.isEmpty(totalResult)) {
                    //全员空闲
                    allNoBusy = true;
                } else {
                    allNoBusy = false;
                    for (ScheduleUserBusyResponse.ScheduleUserBusy busy : userResult) {
                        if (busy.visible != ScheduleUserBusyResponse.ScheduleUserBusy.SCHEDULE_USER_BUSY_HIDE) {
                            List<ScheduleDetailBusyTime> detailBusyTimes = new ArrayList<>();
                            for (ScheduleBusyTime busyTime : busy.times) {
                                ScheduleDetailBusyTime detailBusyTime = new ScheduleDetailBusyTime();
                                detailBusyTime.startTime = ScheduleUtils.serverTime2TimeIdx(busyTime.beginTime);
                                detailBusyTime.endTime = ScheduleUtils.serverTime2TimeIdx(busyTime.endTime);
                                detailBusyTimes.add(detailBusyTime);
                            }
                            busyTimes.add(detailBusyTimes);
                        }
                    }
                }
                resetBusyTimeDesc();
            }

            @Override
            public void onSuccess(ApiData<ScheduleUserBusyResponse> data) {

            }

            @Override
            public void onComplete() {
                setLookBusyIsUploading(false);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                setBusyTimeDesc(getResources().getString(R.string.schedule_look_actor_busy));
            }
        });
        if (TextUtils.isEmpty(mSchedule.getTargetDate())) {
            request.targetDate = ScheduleUtils.getServerDate(new Date());
        } else {
            request.targetDate = mSchedule.getTargetDate();
        }
        request.userIds = ScheduleUtils.convert(allIds);
        request.excludeScheduleId = mSchedule.getScheduleId();
        HttpExecutor.execute(request);
    }

    private String[] toArray(List<String> longList) {
        String[] result = new String[longList.size()];
        for (int i = 0; i < result.length; i++) {
            result[i] = longList.get(i);
        }
        return result;
    }

    /**
     * 重新计算日程冲突人数
     */
    private void resetBusyTimeDesc() {
        if (allNoBusy) {
            setBusyTimeDesc(getResources().getString(R.string.schedule_actor_no_busy));
        } else {
            int busyNum = 0;
            for (List<ScheduleDetailBusyTime> busyTime : busyTimes) {
                for (ScheduleDetailBusyTime time : busyTime) {
                    if (Math.max(startTime, time.startTime) < Math.min(endTime, time.endTime)) {
                        busyNum++;
                        break;
                    }
                }
            }
            if (busyNum > 0) {
                setBusyTimeDesc(getResources().getString(R.string.schedule_actor_busy_num, ScheduleUtils.getLimitCount(busyNum)), true);
            } else {
                setBusyTimeDesc(getResources().getString(R.string.schedule_actor_no_busy));
            }
        }
    }

    public ObservableField<String> getBusyTimeDesc() {
        return busyTimeDesc;
    }

    public void setBusyTimeDesc(String busyTimeDesc) {
        setBusyTimeDesc(busyTimeDesc, false);
    }

    public void setBusyTimeDesc(String busyTimeDesc, boolean highlight) {
        this.busyTimeDesc.set(getResources().getString(R.string.schedule_participant_count, ScheduleUtils.getLimitCount(allIds.size())) + "，" + busyTimeDesc);
        busyTimeHighlight.set(highlight);
    }

    public ObservableBoolean getBusyTimeHighlight() {
        return busyTimeHighlight;
    }

    public ObservableBoolean getLookBusy() {
        return lookBusy;
    }

    public void setLookBusy(boolean lookBusy) {
        this.lookBusy.set(lookBusy);
    }

    public ObservableBoolean getLookBusyIsUploading() {
        return lookBusyIsUploading;
    }

    public void setLookBusyIsUploading(boolean lookBusyIsUploading) {
        this.lookBusyIsUploading.set(lookBusyIsUploading);
    }

    public void setAddressLocation(PoiItem poiItem) {
        addressLocation = poiItem;
        if (poiItem == null) {
            mSchedule.setLocation(null);
        } else {
            ScheduleLocation location = new ScheduleLocation();
            location.setName(poiItem.getTitle());
            location.setAddr(String.format("%s%s%s", poiItem.getCityName(), poiItem.getAdName(), poiItem.getSnippet()));
            location.setLng(poiItem.getLatLonPoint().getLongitude());
            location.setLat(poiItem.getLatLonPoint().getLatitude());
            mSchedule.setLocation(location);
        }
    }

    public boolean isAllDaySchedule() {
        return ScheduleUtils.isAllDaySchedule(mSchedule);
    }

    public ObservableField<String> getBestRoomMsg() {
        return bestRoomMsg;
    }

    public void setBestRoomMsg(String bestRoomMsg) {
        this.bestRoomMsg.set(bestRoomMsg);
    }

    public ObservableBoolean getHasAudioAndLED() {
        return hasAudioAndLED;
    }

    public void setHasAudioAndLED(boolean hasAudioAndLED) {
        this.hasAudioAndLED.set(hasAudioAndLED);
    }

    public ObservableBoolean getShowMeetingRoomAccessControl() {
        return showMeetingRoomAccessControl;
    }

    public void setShowMeetingRoomAccessControl(boolean showMeetingRoomAccessControl) {
        this.showMeetingRoomAccessControl.set(showMeetingRoomAccessControl);
    }

    public ObservableBoolean isVideoMeetingVisible() {
        return videoMeetingVisible;
    }

    public List<BottomSelectItemBean> getCalendarGroup() {
        return mSelectableCalendars;
    }

    public int getLastChoseCalendar() {
        return mLastSelectedCalendarIndex;
    }

    public void setSelectedCalendarIndex(int index) {
        if (mCalendarGroupOptions == null) {
            return;
        }

        CalendarGroupOptions.Entry entry = mCalendarGroupOptions.get(index);
        mLastSelectedCalendarIndex = index;
        mScheduleCalendarGroupName.set(entry.name);
        mSchedule.setCalenderGroupId(entry.id);
        mCalendarStyle.postValue(entry.style);
    }

    public boolean isCustomizedCalendar() {
        return ServiceManager.getInstance().getScheduleService()
                .isCustomizedCalendarGroup(mSchedule.getCalenderGroupId());
    }

    public void setIsNotifyParticipator(boolean mIsNotifyParticipator) {
        this.mIsNotifyParticipator = mIsNotifyParticipator;
    }

    public void setChatId(String chatId) {
        mChatId = chatId;
    }

    /**
     * 如果日程参与人不仅仅只有自己就通知
     */
    public void notifyIfNecessary() {
        mIsNotifyParticipator = (isParticipantNotOnlyMe() || groupIds.size() > 0);
    }

    public ObservableBoolean getCanJustInvite() {
        return canJustInvite;
    }

    public void setCanJustInvite(Schedule schedule) {
        boolean isManager = schedule.isManager() == 1 || TextUtils.equals(schedule.getCreatorId(), MY_USER_ID);
        boolean canModify = schedule.getPartnerModify() == 1;
        boolean canShare = schedule.getPartnerShare() == 1;
        //不能编辑 但是 可以邀请
        this.canJustInvite.set(!isManager && !canModify && canShare);
    }

    public ObservableBoolean getCanAddUser() {
        return canAddUser;
    }

    public ObservableBoolean getCanChangeCalendar() {
        return canChangeCalendar;
    }

    /**
     * 传入两个Long类型的list来取出不能删除的人员和群组Id
     */
    public void obtainParticipantIdCannotBeDeleted(List<String> participantsCannotBeDeleted,
                                                 List<String> groupsCannotBeDeleted) {
        //权限为只可以邀请参与者的时候 才需要考虑不可删除人员
        if (!canJustInvite.get()
                || participantsCannotBeDeleted == null
                || groupsCannotBeDeleted == null) {
            return;
        }
        //只可以邀请参与者的成员，不能删除不是自己邀请的成员
        for (ScheduleParticipant participant :
                mSchedule.getPartners()) {
            if (!TextUtils.equals(participant.getInviteUserId(), MY_USER_ID)) {
                if (participant.getPartnerType() == 1) {
                    participantsCannotBeDeleted.add(participant.getPartnerId());
                } else {
                    groupsCannotBeDeleted.add(participant.getPartnerId());
                }
            }
        }
    }

    /**
     * 判断日程参与人是否变更
     *
     * @return  0无变化  1新增参与者  2删除参与者  3新增和删除均有
     */
    public int getParticipatorChangeState() {
        Map<String, String> groupIdMap = new HashMap<>();
        Map<String, String> friendIdMap = new HashMap<>();
        for (String id : groupIds) {
            groupIdMap.put(id, id);
        }
        for (String id : friendIds) {
            friendIdMap.put(id, id);
        }

        boolean add = false;
        boolean del = false;
        for (String id : startGroupIds) {
            if (groupIdMap.containsKey(id)) {
                groupIdMap.remove(id);
            } else {
                del = true;
            }
        }
        if (groupIdMap.size() > 0) {
            add = true;
        }

        for (String id : startFriendIds) {
            if (friendIdMap.containsKey(id)) {
                friendIdMap.remove(id);
            } else {
                del = true;
            }
        }
        if (friendIdMap.size() > 0) {
            add = true;
        }

        if (add && del) {
            return CHANGE_STATE_ADD_AND_DEL;
        }
        if (del) {
            return CHANGE_STATE_DEL;
        }
        if (add) {
            return CHANGE_STATE_ADD;
        }
        return CHANGE_STATE_NO_CHANGE;
    }

    /**
     * 获取日程被修改后更新时，弹框显示的对应提示标题
     * 需要判断参与人，时间地点等信息
     *
     * @param participatorChangeState           参与人变化
     * @param timeAndAddressInfoChangeState     时间地点等变化
     * @return                                  对应类型的标题资源id
     */
    public int getScheduleUpdateChangeNotifyTitle(int participatorChangeState, int timeAndAddressInfoChangeState) {
        if (participatorChangeState > 0 && timeAndAddressInfoChangeState > 0) {
            switch (participatorChangeState) {
                case CHANGE_STATE_ADD:
                    return R.string.schedule_update_notification_tip_to_add_part_change;
                case CHANGE_STATE_DEL:
                    return R.string.schedule_update_notification_tip_to_del_part_change;
                case CHANGE_STATE_ADD_AND_DEL:
                    return R.string.schedule_update_notification_tip_to_change_part_change;
                default:
                    break;
            }
        }

        if (participatorChangeState > 0) {
            return getParticipatorChangeNotifyTitle(participatorChangeState);
        }
        if (timeAndAddressInfoChangeState > 0) {
            return getTimeAndAddressInfoChangeNotifyTitle();
        }
        return 0;
    }

    /**
     * 获取仅时间、重复频率、地点、会议室、会议室用途是否发生改变后的标题
     * @return
     */
    private int getTimeAndAddressInfoChangeNotifyTitle() {
        return R.string.schedule_update_notification_tip;
    }

    /**
     * 获取参与人变化后更新时，弹框显示的对应提示标题
     * @param   participatorChangeState
     * @return
     */
    public int getParticipatorChangeNotifyTitle (int participatorChangeState) {
        switch (participatorChangeState) {
            case CHANGE_STATE_ADD:
                return R.string.schedule_update_notification_tip_to_add_part;
            case CHANGE_STATE_DEL:
                return R.string.schedule_update_notification_tip_to_del_part;
            case CHANGE_STATE_ADD_AND_DEL:
                return R.string.schedule_update_notification_tip_to_change_part;
            default:
                break;
        }
        return R.string.schedule_update_notification_tip;
    }

    /**
     * 时间、重复频率、地点、会议室、会议室用途是否发生改变
     *
     * @return
     */
    public int getTimeAndAddressInfoChangeState() {
        boolean timeAndAddressInfoSame = ScheduleUtils.isContentSame(mCurSchedule, mSchedule)
                && TextUtils.equals(mCurSchedule.getBeginTime(), mSchedule.getBeginTime())
                && TextUtils.equals(mCurSchedule.getEndTime(), mSchedule.getEndTime())
                && TextUtils.equals(mCurSchedule.getTargetDate(), mSchedule.getTargetDate())
                && mCurSchedule.getRepeatType() == mSchedule.getRepeatType()
                && ScheduleUtils.isSameScheduleLocation(mCurSchedule.getLocation(), mSchedule.getLocation())
                && ScheduleUtils.isSameScheduleMeeting(mCurSchedule.getMeetingInfo(), mSchedule.getMeetingInfo());
        return timeAndAddressInfoSame ? CHANGE_STATE_TIME_LOCATION_NO_CHANGE : CHANGE_STATE_TIME_LOCATION_CHANGED;
    }

    /**
     * 创建日程时，初始化默认参数
     * 退出时比较参数，根据是否修改来判断是否显示弹框
     * @param friendIds
     */
    public void initCreateScheduleDefaultParams(ArrayList<String> friendIds, Schedule defaultParam) {
        Date dateTime = new Date();
        startFriendIds.addAll(friendIds);
        mCurSchedule.setPartnerShare(1); // 创建时默认可分享
        mCurSchedule.setContentEmpty(1); // 默认无标题
        mCurSchedule.setContent("");
        if (TextUtils.isEmpty(defaultParam.getTargetDate())) {
            mCurSchedule.setTargetDate(ScheduleUtils.yMdFormat.format(dateTime));
        } else {
            mCurSchedule.setTargetDate(defaultParam.getTargetDate());
        }
        if (TextUtils.isEmpty(defaultParam.getBeginTime()) || TextUtils.isEmpty(defaultParam.getEndTime())) {
            String[] time = ScheduleUtils.getDurationServerTime(dateTime, mDynamicDefaultDuration);
            mCurSchedule.setBeginTime(time[0]);
            mCurSchedule.setEndTime(time[1]);
        } else {
            mCurSchedule.setBeginTime(defaultParam.getBeginTime());
            mCurSchedule.setEndTime(defaultParam.getEndTime());
        }
        if (defaultParam.getMeetingInfo() != null) {
            mCurSchedule.setMeetingInfo(defaultParam.getMeetingInfo());
        }
        mCurSchedule.setDesc("");
        mCurSchedule.setRepeatDays("");
    }

    /**
     * 判断修改前后，数据是否一致
     * @return
     */
    public boolean isScheduleSame() {
        return saveEnable() && !mFileChanged && (getParticipatorChangeState() == CHANGE_STATE_NO_CHANGE)
                && ScheduleUtils.areSameContent(mCurSchedule, mSchedule);
    }

    public void setShareBackToConversation(boolean shareBackToConversation) {
        mIsShareBackToConversation = shareBackToConversation;
    }

    public void setValidMeetingRoomBeginTime(String validMeetingRoomBeginTime) {
        this.mValidMeetingRoomBeginTime = validMeetingRoomBeginTime;
    }

    public void setValidMeetingRoomEndTime(String validMeetingRoomEndTime) {
        this.mValidMeetingRoomEndTime = validMeetingRoomEndTime;
    }

    public LiveData<Boolean> getCancelMeetingRoomEvent() {
        return mCancelMeetingRoomEvent;
    }

    public String getValidMeetingRoomBeginTime() {
        return mValidMeetingRoomBeginTime;
    }

    public String getValidMeetingRoomEndTime() {
        return mValidMeetingRoomEndTime;
    }

    public String getValidMeetingRoomStartTargetDate() {
        return mValidMeetingRoomStartTargetDate;
    }

    public void setValidMeetingRoomStartTargetDate(String validMeetingRoomStartTargetDate) {
        mValidMeetingRoomStartTargetDate = validMeetingRoomStartTargetDate;
    }

    public MutableLiveData<CalendarStyleEnum> getCalendarStyle() {
        return mCalendarStyle;
    }

    public long getDynamicDefaultDuration() {
        return mDynamicDefaultDuration;
    }
}