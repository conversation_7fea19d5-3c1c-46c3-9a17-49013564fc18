package com.twl.hi.schedule.freetime.view.callback;

import com.twl.hi.basic.callback.TitleBarCallback;
import com.twl.hi.basic.dialog.CalendarSelectDialog;

/**
 * Created by ChaiJiangpeng on 2020/10/15
 * Describe:
 */
public interface ScheduleLookBusyCallback extends TitleBarCallback, CalendarSelectDialog.OnItemClickListener {
    void chooseDate();

    void selectTime();

    void clickSortBusy();
}
