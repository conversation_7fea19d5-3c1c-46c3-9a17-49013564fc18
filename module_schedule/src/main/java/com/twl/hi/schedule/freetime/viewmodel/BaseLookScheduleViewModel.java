package com.twl.hi.schedule.freetime.viewmodel;


import static com.twl.hi.foundation.api.response.bean.CalendarItemBean.getCalendarCheckedKV;

import android.app.Application;
import android.text.TextUtils;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.request.schedule.ScheduleSubscribeRequest;
import com.twl.hi.foundation.api.response.ScheduleSubscribeResponse;
import com.twl.hi.foundation.api.response.bean.ScheduleSubscribeBean;
import com.twl.hi.schedule.SchedulePointUtil;
import com.twl.hi.schedule.preference.model.remote.ScheduleSubscribeUpdateRequest;
import com.twl.hi.schedule.preference.model.remote.ScheduleSubscribeUpdateResponse;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import java.util.List;

import kotlin.collections.CollectionsKt;

/**
 * Created by ChaiJiangpeng on 2020/10/15
 * Describe:
 */
public class BaseLookScheduleViewModel extends BaseScheduleViewModel {

    private static final String TAG = "BaseLookScheduleViewModel";

    private final ObservableField<Boolean> isBusy = new ObservableField<>(false);
    private final ObservableField<Boolean> hasObserved = new ObservableField<>(false);

    public BaseLookScheduleViewModel(Application application) {
        super(application);
    }

    public MutableLiveData<List<String>> getParticipantIds() {
        return participantIds;
    }

    public void setParticipantIds(List<String> participantIds) {
        this.participantIds.postValue(participantIds);
    }

    public ObservableField<Boolean> isBusy() {
        return isBusy;
    }

    public void setIsBusy(boolean isBusy) {
        this.isBusy.set(isBusy);
    }

    public ObservableField<Boolean> hasObserved() {
        return hasObserved;
    }

    public void setHasObserved(boolean hasObserved) {
        this.hasObserved.set(hasObserved);
    }

    public void requestScheduleSubscribe(boolean subscribe) {
        if (participantIds.getValue().size() != 2) {
            TLog.error(TAG, "error sizeof participantIds, %d", participantIds.getValue().size());
            return;
        }
        String subscribeId = participantIds.getValue().get(1);
        ScheduleSubscribeUpdateRequest request = new ScheduleSubscribeUpdateRequest(new BaseApiRequestCallback<ScheduleSubscribeUpdateResponse>() {
            @Override
            public void onSuccess(ApiData<ScheduleSubscribeUpdateResponse> data) {
                if (!TextUtils.isEmpty(data.resp.getCalendarId())) {
                    getCalendarCheckedKV().putBoolean(data.resp.getCalendarId(), subscribe);
                }
                hasObserved.set(subscribe);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.subscribeId = subscribeId;
        request.type = 1;
        request.subscribe = subscribe ? 1 : 0;
        HttpExecutor.execute(request);

        SchedulePointUtil.pointCalendarSubscribeClick(request.subscribe == 1 ? "add" : "cancel", "1", request.subscribeId);
    }

    public void requestScheduleSubscribeStatus() {
        if (participantIds.getValue().size() != 2) {
            TLog.error(TAG, "error sizeof participantIds, %d", participantIds.getValue().size());
            return;
        }
        ScheduleSubscribeRequest request = new ScheduleSubscribeRequest(new BaseApiRequestCallback<ScheduleSubscribeResponse>() {

            @Override
            public void onSuccess(ApiData<ScheduleSubscribeResponse> data) {
                ScheduleSubscribeBean subscribeBean = CollectionsKt.firstOrNull(
                        data.resp.subscribeList,
                        bean -> TextUtils.equals(bean.subscribeId, participantIds.getValue().get(1))
                );
                hasObserved.set(subscribeBean != null);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }
}
