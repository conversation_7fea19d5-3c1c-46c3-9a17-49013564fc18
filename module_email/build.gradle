apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-parcelize'

apply from: rootProject.file('bzl-push.gradle')
apply from: rootProject.file('ksp_config.gradle')

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion build_versions.build_tools

    defaultConfig {
        minSdkVersion build_versions.min_sdk
        targetSdkVersion build_versions.target_sdk
        versionCode rootProject.ext.appVersionCode
        versionName rootProject.ext.appShowVersionName
        resourcePrefix 'email_'
    }
    dataBinding {
        enabled = true
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
}

dependenciesForImplementation(project, ":lib_webeditor", deps.commonLibs.lib_webeditor)
dependenciesForImplementation(project, ":module_ai_summary",deps.commonLibs.module_ai_summary)
dependenciesForImplementation(project, ":module_foundation_business", deps.commonLibs.module_foundation_business)
dependenciesForImplementation(project, ":module_viewer", deps.commonLibs.module_viewer)

dependenciesForImplementation(project, ":export_module_email", deps.commonLibs.export_module_email)
dependenciesForImplementation(project, ":export_module_chat", deps.commonLibs.export_module_chat)
dependenciesForImplementation(project, ":export_module_webview", deps.commonLibs.export_module_webview)
dependenciesForImplementation(project, ":export_module_main", deps.commonLibs.export_module_main)
dependenciesForImplementation(project, ":export_module_select", deps.commonLibs.export_module_select)
dependenciesForImplementation(project, ":export_module_organization", deps.commonLibs.export_module_organization)
dependenciesForImplementation(project, ":export_module_schedule", deps.commonLibs.export_module_schedule)

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation deps.mails_sdk
    implementation "androidx.core:core-ktx:1.5.0"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.0"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.0"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:1.6.0"
    implementation project(path: ':export_module_webview')
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
}