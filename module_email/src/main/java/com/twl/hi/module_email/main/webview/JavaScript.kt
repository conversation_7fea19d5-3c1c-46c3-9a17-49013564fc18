package com.twl.hi.module_email.main.webview

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.webkit.JavascriptInterface
import android.webkit.ValueCallback
import android.webkit.WebView
import com.google.gson.Gson
import com.google.gson.JsonElement
import com.techwolf.lib.tlog.TLog
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.viewer.MediaViewerActivity
import com.twl.hi.module_email.view_edit.UploadImgBean
import com.twl.hi.webeditor.RichEditor
import hi.kernel.BundleConstants
import hi.kernel.Constants
import hi.kernel.utils.MD5Utils
import lib.twl.common.util.AppUtil
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.ProcessHelper
import lib.twl.common.util.ToastUtils
import lib.twl.common.views.imagesview.Image
import lib.twl.common.views.imagesview.MultiViewerBean
import org.json.JSONObject
import java.net.URLEncoder
import java.util.Vector

/**
 * <AUTHOR>
 * webview js相关操作
 * 包含cid和本地图片路径互相替换逻辑
 */
object JavaScript {

    val TAG = "JavaScript"

    private const val WEB_URL =
        "((https?|ftp|file|bosshi)://)([0-9a-zA-Z+%#@/&$~();:?!,=_-])+(\\.[\\.0-9a-zA-Z+%#@/&$~();:?!,=_-]+)*|(?:www|(?!www)[0-9a-zA-Z_-]+)(\\.[0-9a-zA-Z_-]+)*\\.(com|cn|net|edu|org|site|io|info|me|im|cloud|dev|buzz|ai|red|mil|gov|tech)(:[0-9]+)?(/((((?!https?://|ftp://|@)[0-9a-zA-Z+%#@/&$~().;!:,=_-]+))?(\\?[0-9a-zA-Z+%#@/&$~().;!?:,=_-]+)?))?"
    private const val IP_ADDRESS =
        "((https?|ftp|file|bosshi)://)?((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}(:[0-9]+)?((/((?!https?://|ftp://|@)[0-9a-zA-Z~\\#@\\+\\%\\.\\/_,-]+)?)?(\\?[0-9a-zA-Z\\+@\\%\\/&\\.~;?:=_,-]+)?)"
    private const val EMAIL_ADDRESS = "([\\w!#$%&'*+=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+(com|cn|net|edu|org|site|io))"

    /**
     * 最中传递给JavaScript的识别规则Json
     */
    private var detectRuleJson = ""

    val urlPattern = ProcessHelper.getUserPreferences().getString(Constants.KEY_URL_PATTERNS, WEB_URL)
    val ipPattern = ProcessHelper.getUserPreferences().getString(Constants.KEY_IP_ADDRESS_PATTERNS, IP_ADDRESS)
    val emailPattern = ProcessHelper.getUserPreferences().getString(Constants.KEY_EMAIL_PATTERNS, EMAIL_ADDRESS)

    var delCallback: ((String) -> Unit?)? = null

    var imageRemoveCallback: ((String) -> Unit?)? = null

    var uploadImgList = Vector<UploadImgBean>()

    var onLoadCallback: (() -> Unit) ?= null

    /**
     * 获取列表数量
     */
    @JavascriptInterface
    fun getUploadImageListSize(): Int {
        return uploadImgList.size
    }

    @JavascriptInterface
    fun onLoadReady() {
        onLoadCallback?.invoke()
    }

    @JavascriptInterface
    fun onImgRemoved(cid: String) {
        TLog.info(TAG, "onImgRemoved, cid = $cid")
        imageRemoveCallback?.invoke(cid)
    }

    @JavascriptInterface
    fun onImgClick(imgSrc: String) {
        TLog.info(TAG, imgSrc)
        if (imgSrc.startsWith("cid:")) {
            ExecutorFactory.execMainTask {
                ToastUtils.ss("图片下载中...")
            }
            return
        }
        if (imgSrc.startsWith("data:")) {
            TLog.info(TAG, "不支持打开的图片...");
            return
        }

        val multiViewerBeans: MutableList<MultiViewerBean> = ArrayList<MultiViewerBean>(1)
        val image = Image(imgSrc)
        val multiViewerBean = MultiViewerBean(MultiViewerBean.TYPE_IMAGE)
        multiViewerBean.image = image
        multiViewerBeans.add(multiViewerBean)
        ServiceManager.getInstance().messageService.previewImages =
            multiViewerBeans

        val bundle = Bundle()
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT, 0)
        bundle.putString(BundleConstants.BUNDLE_DATA_STRING, "")
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT_1, MessageConstants.MSG_SINGLE_CHAT)
        bundle.putBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN, false)
        bundle.putBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN_1, false)
        val context = ProcessHelper.getContext()
        AppUtil.startActivity(
            context,
            Intent(context, MediaViewerActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                putExtras(bundle)
            })
    }

    /**
     * 获取指定bean的本地路径
     */
    @JavascriptInterface
    fun getUploadImageBeanPathAt(index: Int): String {
        return uploadImgList[index].uri.toString()
    }

    /**
     * 获取指定bean的cid
     */
    @JavascriptInterface
    fun getUploadImageCidPathAt(index: Int): String {
        return uploadImgList[index].cid
    }

    /**
     * 删除不需要的cid图片
     */
    @JavascriptInterface
    fun delNoAdapterCid(fileId: String) {
        delCallback?.invoke(fileId)
    }

    /**
     * 清空缓存
     */
    @JavascriptInterface
    fun clearUploadList() {
        uploadImgList.clear()
    }

    /**
     * 替换cid的值为图片本地路径
     * 用于支持三方邮箱发送到本地的展示
     */
    fun replaceCidToLocalImgPath(webView: WebView?, cid: String?, uriPath: String?, callback: ((String) -> Unit) ?= null) {
        if (webView == null || cid.isNullOrEmpty() || uriPath.isNullOrEmpty()) {
            TLog.info(TAG, "param is null $webView $cid $uriPath")
            return
        }
        Log.i(TAG, "$cid  $uriPath")
        webView.evaluateJavascript(
            "javascript:replaceCidToLocalImgPath('cid:$cid','$uriPath');"
        ) {
            callback?.run {
                callback(it)
            }
        }
    }

    /**
     * 删除src值为cid的图片标签
     * 用于支持大图转大附件
     */
    fun deleteBigInlineImgTag(webView: WebView?, cid: String?, uriPath: String?, callback: ((String) -> Unit) ?= null) {
        if (webView == null || cid.isNullOrEmpty() || uriPath.isNullOrEmpty()) {
            TLog.info(TAG, "param is null $webView $cid $uriPath")
            return
        }
        Log.i(TAG, "$cid  $uriPath")
        webView.evaluateJavascript(
            "javascript:deleteBigInlineImgTag('cid:$cid','$uriPath');"
        ) {
            callback?.run {
                callback(it)
            }
        }
    }


    /**
     * 获取网页中当前存在的所有cid
     */
    fun fetchAllCidImgFromHtml(mailContent: WebView, fetchCidList: (cidList: String) -> Unit) {
        mailContent.evaluateJavascript("javascript:fetchAllCidImgFromHtml();", ValueCallback {
            it?.let {
                fetchCidList(it)
            }
        })
    }

    /**
     * 设置图片点击
     */
    fun setImgClickEvent(mailWebPreview: WebView) {
        mailWebPreview.evaluateJavascript("javascript:setImgClickEvent();", ValueCallback {
        })
    }

    /**
     * html内容适配展示
     */
    fun initWebViewEditor(mailWebPreview: WebView, messageId: String, block: (() -> Unit)?= null) {
        TLog.info(TAG, "initWebViewEditor -> $messageId ")
        val mailIdMd5 = if (messageId.isEmpty()) { "" } else MD5Utils.getMD5(messageId)?:""
        mailWebPreview.evaluateJavascript(
            "javascript:initWebViewEditor('${mailIdMd5}')") {
            ExecutorFactory.execMainTask { block?.invoke() }
        }
    }

    fun replacePictureShow(webview: WebView, status: Int, block: (() -> Unit)? = null) {
        webview.evaluateJavascript("javascript:replacePictureShow($status)") {
            ExecutorFactory.execMainTask { block?.invoke() }
        }
    }

    fun exportHtmlFromEditor(
        mailContent: RichEditor,
        imgBeanList: JsonElement?,
        hasDownload: Boolean,
        attachFileList: JsonElement?,
        function: (String, String) -> Unit
    ) {
        mailContent.evaluateJavascript("javascript:exportHtmlFromEditor('$imgBeanList', '$hasDownload', '$attachFileList')") {
            var json = it
            val jsonObject = JSONObject(json.replace("\\\"", "\""))
            try {
                val html = String(android.util.Base64.decode(jsonObject.getString("inputHtml"), android.util.Base64.NO_WRAP))
                val text = String(android.util.Base64.decode(jsonObject.getString("inputText"), android.util.Base64.NO_WRAP))
                function(html, text)
            } catch (e: Exception) {
                TLog.error(TAG, "error decode %s", e.message)
                function("", "")
            }
        }
    }

    fun registerCallback(callback: (String) -> Unit) {
        delCallback = callback
    }

    fun registerImageRemoveCallback(callback: (String) -> Unit) {
        imageRemoveCallback = callback
    }

    fun unRegisterCallback() {
        delCallback = null
        imageRemoveCallback = null
    }

    /**
     * 隐藏大附件内联静态样式
     */
    fun hideAttachDivByFileIds(webview: WebView, json: JsonElement?) {
        webview.evaluateJavascript("javascript:hideAttachDivByFileIds('$json')") {
        }
    }

    fun hideAttachBodyOnLoad(webview: WebView) {
        webview.evaluateJavascript("javascript:hideAttachBodyOnLoad()"){ }
    }

    fun showAttachBodyWhenNoBigAttach(webview: WebView) {
        webview.evaluateJavascript("javascript:showAttachBodyWhenNoBigAttach()"){ }
    }

    fun setupLinkDetect(webview: WebView) {
        webview.evaluateJavascript("javascript:setupLinkDetect('${urlPattern.replace("\\", "\\\\")}', '${ipPattern.replace("\\", "\\\\")}')"){ }
    }

    fun showBody(webview: WebView) {
        webview.evaluateJavascript("javascript:showBody()") { }
    }

    fun triggerLinkDetect(webview: WebView) {
        val patterns = detectRuleJson.ifEmpty { generateLinkDetectJson() }
        webview.evaluateJavascript("""javascript:triggerLinkDetect('$patterns')"""){ }
    }

    fun adaptNoahHtml(webview: WebView) {
        webview.evaluateJavascript("javascript:adaptNoahHtml()") {}
    }

    private fun generateLinkDetectJson() =
        //如果改变了list的顺序，需要考虑影响，先入的先匹配
        listOf(
            LinkDetectRule(emailPattern.replace("\\", "\\\\"), "^mailto://", "mailto:"),
            LinkDetectRule(ipPattern.replace("\\", "\\\\"), "^(https?|ftp|file|bosshi)://", "http://"),
            LinkDetectRule(urlPattern.replace("\\", "\\\\"), "^(https?|ftp|file|bosshi)://", "http://")
        ).let {
            detectRuleJson = Gson().toJson(it)
            detectRuleJson
        }

    fun replaceSignature(webView: WebView, signatureInfo: String, function: () -> Unit) {
        TLog.info(TAG, "replaceSignature -> $signatureInfo ")
        webView.evaluateJavascript("javascript:replaceSignature('${URLEncoder.encode(signatureInfo, "UTF-8")}')") {
            function.invoke()
        }
    }

    fun removeSignature(webView: WebView, function: () -> Unit) {
        webView.evaluateJavascript("javascript:removeSignature()") {
            function.invoke()
        }
    }

    fun bindMessageIdForHtml(webView: WebView, id: String, function: () -> Unit) {
        TLog.info(TAG, "bindMessageIdForHtml -> $id ")
        webView.evaluateJavascript("javascript:bindMessageIdForHtml('${MD5Utils.getMD5(id)}')") {
            function.invoke()
        }
    }

    /**
     * 连接匹配规则以及补充前缀
     * @param reg：用来匹配的正则
     * @param prefixReg ：[reg]匹配上之后，进一步 判断是否有前缀
     * @param defaultPrefix: 如果没有前缀则补充该参数
     */
    data class LinkDetectRule(val reg: String, val prefixReg: String, val defaultPrefix: String)
}