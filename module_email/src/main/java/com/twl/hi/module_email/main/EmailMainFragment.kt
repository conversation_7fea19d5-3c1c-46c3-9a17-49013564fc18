package com.twl.hi.module_email.main

import android.animation.ObjectAnimator
import android.animation.ValueAnimator.INFINITE
import android.graphics.Color
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.widget.TextView
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.adapter.MyMultiTypeAdapter
import com.twl.hi.basic.dialog.DialogUtils
import com.twl.hi.basic.helpers.RemindViewUpdateHelper
import com.twl.hi.email.pb.constants.CommonType.BSH_MAIL_FOLDER_TYPE.BSH_MAIL_FOLDER_TYPE_LABEL_VALUE
import com.twl.hi.export.chat.router.ChatPageRouter
import com.twl.hi.export.email.router.EmailPageRouter
import com.twl.hi.foundation.base.fragment.FoundationVMFragment
import com.twl.hi.foundation.helper.GlobalRemindHelper
import com.twl.hi.foundation.logic.EmailService.LOGIN_STATUS_INVALID_ACCOUNT
import com.twl.hi.foundation.logic.EmailService.LOGIN_STATUS_LOGIN
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.logic.SystemGrayConfigService
import com.twl.hi.foundation.model.email.bean.receive.MailFolder
import com.twl.hi.foundation.model.email.constants.MailConstants
import com.twl.hi.foundation.utils.PointUtils.BuilderV4
import com.twl.hi.foundation.utils.point.MailPoint
import com.twl.hi.module_email.BR
import com.twl.hi.module_email.R
import com.twl.hi.module_email.callback.IEmailAccountSelectorCallback
import com.twl.hi.module_email.callback.IEmailBoxSelectorCallback
import com.twl.hi.module_email.common.sharedMailViewModels
import com.twl.hi.module_email.common_receive.EmailCommonReceiveFragment
import com.twl.hi.module_email.custome.EmailCustomFragment
import com.twl.hi.module_email.databinding.EmailFragmentMainBinding
import com.twl.hi.module_email.databinding.EmailMainItemMailAccountItemBinding
import com.twl.hi.module_email.databinding.EmailMainItemMailboxFolderGroupTitleBinding
import com.twl.hi.module_email.databinding.EmailMainItemMailboxFolderSplitLineBinding
import com.twl.hi.module_email.databinding.EmailMainItemMailboxSelectionBinding
import com.twl.hi.module_email.domain.AccountInfoBean
import com.twl.hi.module_email.domain.EmailBusinessCentre
import com.twl.hi.module_email.domain.EmailBusinessCentre.toMailFolderWrapper
import com.twl.hi.module_email.domain.MailFolderGroupTitle
import com.twl.hi.module_email.domain.MailFolderWrapper
import com.twl.hi.module_email.domain.toAccountBean
import com.twl.hi.module_email.folder.manage.showManageFolderFragment
import com.twl.hi.module_email.folder.select.SplitLine
import com.twl.hi.module_email.inbox.EmailInboxFragment
import com.twl.hi.module_email.main.base.MenuClickEvent
import com.twl.hi.module_email.main.ui.MenuOption
import com.twl.hi.module_email.main.ui.dialog.MailMenuOptionSelectDialog
import com.twl.hi.module_email.util.observe
import com.twl.hi.module_email.view_edit.edit.EmailEditFragment
import lib.twl.common.ext.rotateDown
import lib.twl.common.ext.rotateUp
import lib.twl.common.util.AppUtil
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.ExecutorFactory.RunnableWrapper
import lib.twl.common.util.QMUIDisplayHelper
import lib.twl.common.util.TimeDifferenceUtil
import lib.twl.common.util.TimeTag
import lib.twl.common.util.ToastUtils
import lib.twl.common.util.widget.HiPopupWindow
import lib.twl.common.views.loading.SpriteFactory
import lib.twl.common.views.loading.Style
import java.util.Locale

typealias OnItemClickListener = (MailFolder) -> Unit

/**
 * 邮件主页
 */
class EmailMainFragment : FoundationVMFragment<EmailFragmentMainBinding, EmailMainViewModel>() {

    private val sharedViewModel by lazy { sharedMailViewModels(activity as FragmentActivity) }
    private var currentFolder: MailFolder? = null
    private var folderList = mutableListOf<MailFolderWrapper>()
    private var emptyFolderDialog: DialogUtils? = null
    private var closeFullNoticeDialog: DialogUtils? = null
    private var hasSwitchMailAccount = false
    private var switchMailAccountStartTime: Long = 0L
    private var loadingFullMailLoadingAnimator: ObjectAnimator? = null
    private var userCancelFullMailLoad: Boolean = false
    private var targetFolderType = MailConstants.FolderType.UNKNOW

    override fun getContentLayoutId() = R.layout.email_fragment_main

    override fun getCallbackVariable() = -1

    override fun getCallback() = null

    override fun getBindingVariable() = -1

    override fun initFragment() {
        super.initFragment()
        updateContentTopPadding(dataBinding.content)
        showLaunchProgressView()
        bindViewActions()
        observeViewState()
    }

    private fun showLaunchProgressView() {
        dataBinding.ivIcon.setIndeterminateDrawable(SpriteFactory.create(Style.DOUBLE_BOUNCE))
        playFullUpdateNoticeAnimation()
    }

    private fun hideLaunchProgressView() {
        dataBinding.ivIcon.destroyAnimate()
        dataBinding.ivIcon.visibility = View.GONE
    }

    private fun bindViewActions() {
        dataBinding.groupMailboxTitle.setOnClickListener { showMailboxSelector() }
        dataBinding.actionCompose.setOnClickListener {
            composeEmail()
        }
        dataBinding.cancelLongPress.setOnClickListener {
            sharedViewModel.changeMailLongPressCancelLiveData(true)
        }
        dataBinding.switchMailRoot.setOnClickListener {
            if (dataBinding.switchMailAccount.visibility == View.GONE) {
                return@setOnClickListener
            }
            showMailAccountSelector()
        }
        dataBinding.iconMore.setOnClickListener {
            showMoreMailBoxMenu()
        }
        dataBinding.iconMailSetting.setOnClickListener {
            EmailPageRouter.jumpToEmailSetting(activity)
        }
        dataBinding.closeLoadingIcon.setOnClickListener {
            userCancelFullMailLoad = true
            showOrRemoveFullNoticeUi(false)
            showCloseFullNoticeUpdateDialog()
        }
        dataBinding.imgSmallSearch.setOnClickListener {
            AppUtil.startUri(activity, ChatPageRouter.CHAT_RECORD_ALL_SEARCH_ACTIVITY);
            BuilderV4()
                    .name("search-enter-click")
                    .params("source", "email")
                    .point()
        }

        sharedViewModel.getMailLongPressCancelLiveData().observe(this) {
            it?.run {
                if (it) {
                    showMailBoxTitle()
                }
            }
        }
    }

    private fun showCloseFullNoticeUpdateDialog() {
        if (closeFullNoticeDialog == null) {
            closeFullNoticeDialog = DialogUtils.Builder(context)
                .setCancelable(false)
                .setContent("关闭提醒不会影响数据同步")
                .setPositive("我知道了")
                .setPositiveListener {
                    closeFullNoticeDialog?.dismiss()
                }.build()
        }
        closeFullNoticeDialog?.show()
    }

    private fun showMoreMailBoxMenu() {
        fragmentManager?.run {
            val list = mutableListOf<MenuOption>()
            list.add(MenuOption(0, R.drawable.email_icon_setting, "邮箱设置"))
            list.add(MenuOption(1, R.drawable.email_ic_press_delete, "全部清空"))
            MailMenuOptionSelectDialog.instance(list) {
                when (it.indexKey) {
                    0 -> EmailPageRouter.jumpToEmailSetting(activity)
                    1 -> showEmptyFolderDialog()
                }
            }.show(this, "fm")
        }
    }

    private fun showEmptyFolderDialog() {
        val content = "将彻底删除所有${currentFolder?.folderName}列表的邮件，此操作不可恢复，是否继续"
        emptyFolderDialog =
            DialogUtils.Builder(context)
                .setTitle("全部清空")
                .setContent(content)
                .setNegative(R.string.cancel)
                .setPositive("清空")
                .setPositiveColor(activity.resources.getColor(R.color.email_color_F23544))
                .setPositiveCountdown(5000L)
                .setCancelable(true)
                .setPositiveListener(View.OnClickListener {
                    emptyFolderDialog?.dismiss()
                    currentFolder?.run {
                        sharedViewModel.emptyFolderMails(folderId) { code: Int ->
                            if (code == MailConstants.Code.SUCCESS) {
                                ToastUtils.success("文件夹\"${folderName}\"已清空")
                                MailPoint.pointEmptyFolder(if (isTrashFolder) "trash" else "removed")
                            }
                        }
                    }
                }).setNegativeListener(View.OnClickListener {
                    emptyFolderDialog?.dismiss()
                }).build()
        emptyFolderDialog?.show()
    }

    private fun showMailAccountSelector() {
        viewModel.getMailAccountListData().value?.let {
            MailAccountSelector().show(it)
        }
    }

    private fun observeViewState() {
        viewModel.initLiveData()
        viewModel.getFolderData().observe(this, Observer { it ->
            TLog.info(TAG, "folder livedata -> ${it.isNullOrEmpty()} ")
            hideLaunchProgressView()
            dismissProgressDialog()
            if (it.isNullOrEmpty()) {
                TLog.info(TAG, "folderList is empty showFailed")
                delayShowLoadFailed()
                return@Observer
            }
            hideLoadFailed()

            folderList.clear()
            folderList.addAll(it)
            dataBinding.actionCompose.visibility = View.VISIBLE
            TLog.info(TAG, "refresh folderList size ${folderList.size} $currentFolder")
            val current = currentFolder
            if (current == null) {
                selectDefaultMailBox()
            } else {
                folderList.firstOrNull { folderWrapper ->
                    TextUtils.equals(
                        current.folderId ?: "", folderWrapper.mailFolder.folderId
                    )
                }.also { folder ->
                    TLog.info(TAG, "folder null - > ${folder == null} ->  ")
                    folder?.run {
                        updateCurrentSelectFolderShowInfo(folder.mailFolder)
                    } ?: run {
                        selectDefaultMailBox()
                    }
                }
            }
        })

        viewModel.getMailAccountListData()
            .observe(this, object : Observer<MutableList<AccountInfoBean>> {
                override fun onChanged(accountList: MutableList<AccountInfoBean>?) {
                    TLog.info(TAG, "account live data refresh size ${accountList?.size}")
                    showDefaultMailAccount(accountList)
                }
            })

        sharedViewModel.getMailLongPressMenuData()
            .observe(this, object : Observer<MutableList<MenuOption>> {
                override fun onChanged(list: MutableList<MenuOption>?) {
                    list?.run {
                        if (isNotEmpty()) {
                            dataBinding.groupMailboxTitle.visibility = View.GONE
                            dataBinding.mailMenuRoot.visibility = View.VISIBLE
                            dataBinding.mailLongPressMenu.showMenu(list, fragmentManager)
                            dataBinding.switchMailRoot.isEnabled = false
                            dataBinding.iconMore.isEnabled = false
                        }
                    }
                }
            })

        sharedViewModel.getMailLongPressEventData()
            .observe(this, object : Observer<MenuClickEvent> {
                override fun onChanged(event: MenuClickEvent?) {
                    event?.run {
                        dataBinding.mailLongPressMenu.clickEvent {
                            showMailBoxTitle()
                            event(it)
                        }
                    }
                }
            })

        sharedViewModel.selectMailNumLiveData.observe(this, object : Observer<Int> {
            override fun onChanged(number: Int?) {
                number?.run {
                    dataBinding.cancelLongPress.text = String.format(
                        Locale.CHINA, getString(R.string.email_selected_mails), number
                    )
                }
            }
        })

        sharedViewModel.mEditFolderFragmentBackLiveData.observe(this, Observer {
            it?.run {
                EmailBusinessCentre.updateFolderCache()
                sharedViewModel.mEditFolderFragmentBackLiveData.postValue(null)
            }
        })

        sharedViewModel.mailListLiveData.observe(this) {
            if (hasSwitchMailAccount) {
                dismissProgressDialog()
                hasSwitchMailAccount = false
                MailPoint.pointSwitchMailAccount(
                    !(ServiceManager.getInstance().emailService.currentCacheAccountInfo?.toAccountBean()?.isPersonAccount
                        ?: true), System.currentTimeMillis() - switchMailAccountStartTime
                )
            }
        }

        ServiceManager.getInstance().emailService.mailBoxLoginStatus.observe(this) {
            TLog.info(TAG, "mailbox login status = %s", it)
            when (it) {
                LOGIN_STATUS_LOGIN -> {
                    hideLoginFailed()
                    dataBinding.spinkViewContainer.visibility = View.GONE
                }

                LOGIN_STATUS_INVALID_ACCOUNT -> {
                    hideLoginFailed()
                    showEmailInvalid()
                    TimeDifferenceUtil.getInstance().finish(TimeTag.MAIL_TAB_FIRST_CLICK)
                }

                else -> {
                    showLoginFailed()
                    TimeDifferenceUtil.getInstance().finish(TimeTag.MAIL_TAB_FIRST_CLICK)
                }
            }
        }

        ServiceManager.getInstance().emailService.observeToMailInBoxEvent()
            .observe(viewLifecycleOwner) {
                TLog.info(TAG, "choose mail in box")
                selectDefaultMailBox()
                ServiceManager.getInstance().emailService.clearToMailEvent()
            }

        GlobalRemindHelper.remindLiveData().observe(this, Observer {
            it?.run {
                val key = if (GlobalRemindHelper.isGlobalRemindShow(it)) {
                    SystemGrayConfigService.GraySettings.PageRemind.WHOLE_HOMEPAGE_REMIND
                } else if (GlobalRemindHelper.isEmailRemindShow(it)) {
                    SystemGrayConfigService.GraySettings.PageRemind.EMAIL_HOMEPAGE_REMIND
                } else {
                    ""
                }
                RemindViewUpdateHelper.updateRemindView(activity, dataBinding.remindStub, key, it)
            }
        })

        observe(viewModel.showMailUnReadMark()) {
            dataBinding.otherUnreadMark.isGone = it.not()
        }

        observe(ServiceManager.getInstance().emailService.selectMailBoxEvent()) {
            it?.run {
                if (it == MailConstants.FolderType.DRAFTS || it == MailConstants.FolderType.SENDED) {
                    ServiceManager.getInstance().emailService.selectMailBoxAt(MailConstants.FolderType.UNKNOW)
                    targetFolderType = it
                    currentFolder = null
                    TLog.info(TAG, "targetFolderType event -> $targetFolderType ")
                    viewModel.refresh()
                }
            }
        }
    }

    private fun showDefaultMailAccount(accountList: MutableList<AccountInfoBean>?) {
        ExecutorFactory.execMainTask {
            accountList?.firstOrNull { it.currentShowAccount }?.also {
                dataBinding.switchMailRoot.visibility = View.VISIBLE
                dataBinding.mailAccountAddress.text = it.mailAddress
                showOrRemoveFullNoticeUi(it.fullSyncFinish)
                hideLoadFailed()
                TLog.info(TAG, "update current account $it")
            } ?: run {
                TLog.info(TAG, "no default account found")
                dataBinding.switchMailRoot.visibility = View.GONE
                if (!accountList.isNullOrEmpty()) {
                    accountList.first().let {
                        TLog.info(TAG, "peek first as default account")
                        viewModel.switchMailAccount(it) { _, _ ->
                            viewModel.refreshAccount()
                            viewModel.refresh()
                        }
                    }
                } else {
                    hideLaunchProgressView()
                    delayShowLoadFailed()
                    TLog.info(TAG, "account list empty showFailed")
                }
            }
            dataBinding.unreadMark.visibility =
                if (viewModel.hasUnReadMailsOtherAccount(accountList)) View.VISIBLE else View.GONE
            dataBinding.switchMailAccount.visibility =
                if ((accountList?.size ?: 0) <= 1) View.GONE else View.VISIBLE
        }
    }

    private fun showOrRemoveFullNoticeUi(fullSyncFinish: Boolean) {
        if (userCancelFullMailLoad) {
            dataBinding.fullUpdateUi.isGone = true
            loadingFullMailLoadingAnimator?.cancel()
            return
        }
        if (fullSyncFinish) {
            dataBinding.fullUpdateUi.isGone = true
            loadingFullMailLoadingAnimator?.cancel()
            loadingFullMailLoadingAnimator = null
            closeFullNoticeDialog?.dismiss()
        } else {
            playFullUpdateNoticeAnimation()
            dataBinding.fullUpdateUi.isGone = false
        }
    }

    private fun playFullUpdateNoticeAnimation() {
        if (loadingFullMailLoadingAnimator == null) {
            loadingFullMailLoadingAnimator =
                ObjectAnimator.ofFloat(dataBinding.mailLoadingIcon, "rotation", 0f, 180f, 360f)
                    .apply {
                        setAutoCancel(false)
                        interpolator = LinearInterpolator()
                        duration = 2000
                        repeatCount = INFINITE
                        start()
                    }
        }
    }

    private fun selectDefaultMailBox() {
        if (folderList.isEmpty()) return
        TLog.info(TAG, " selectDefaultMailBox -> $targetFolderType ")
        val currentFolderLocal= if (targetFolderType != MailConstants.FolderType.UNKNOW) {
            folderList.filter { it.mailFolder.folderType == targetFolderType }.run {
                targetFolderType = MailConstants.FolderType.UNKNOW
                if (isNotEmpty()) this[0].mailFolder else folderList[0].mailFolder
            }
        } else {
            folderList[0].mailFolder
        }
        val count = currentFolderLocal.showCountInMenu
        if (count > 0) {
            dataBinding.mailboxSubtitle.visibility = View.VISIBLE
            dataBinding.mailboxSubtitle.text = if (count > 99) {
                "(99+)"
            } else {
                "($count)"
            }
        } else {
            dataBinding.mailboxSubtitle.visibility = View.GONE
        }
        selectMailboxAt(currentFolderLocal)
    }

    private fun showMailBoxTitle() {
        dataBinding.mailMenuRoot.visibility = View.GONE
        dataBinding.mailLongPressMenu.clear()
        dataBinding.groupMailboxTitle.visibility = View.VISIBLE
        dataBinding.switchMailRoot.isEnabled = true
        dataBinding.iconMore.isEnabled = true
        sharedViewModel.clearMenuData()
    }

    /**
     * 展示邮箱文件夹加载失败提示
     */
    private fun showLoadFailed() {
        dismissProgressDialog()

        if (dataBinding.loginFailedStub.isInflated && dataBinding.loginFailedStub.root.isVisible) {
            return
        }
        if (dataBinding.accountLackStub.isInflated && dataBinding.accountLackStub.root.isVisible) {
            return
        }
        if (dataBinding.loadFailedStub.isInflated) {
            dataBinding.loadFailedStub.root.visibility = View.VISIBLE
        } else {
            val failedView = dataBinding.loadFailedStub.viewStub?.inflate() ?: return
            val description = getString(R.string.email_mailbox_load_failed)
            failedView.findViewById<TextView>(R.id.description).run {
                setOnClickListener {
                    hideLoadFailed()
                    showProgressDialog("")
                    viewModel.refreshAccount()
                    viewModel.refresh()
                }
                text = SpannableStringBuilder(description).apply {
                    setSpan(
                        ForegroundColorSpan(Color.parseColor("#5D68E8")),
                        description.length - 4,
                        description.length,
                        Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                    )
                }
            }
        }
        TimeDifferenceUtil.getInstance().finish(TimeTag.MAIL_TAB_FIRST_CLICK)
    }

    private val failedLoadRunnable = RunnableWrapper {
        showLoadFailed()
    }

    private fun delayShowLoadFailed() {
        ExecutorFactory.execMainTaskDelay(failedLoadRunnable, 1000)
    }


    /**
     * 展示邮箱 sdk 登陆失败提示
     */
    private fun showLoginFailed() {
        dataBinding.otherUnreadMark.isGone = true
        dataBinding.spinkViewContainer.visibility = View.GONE
        if (dataBinding.loginFailedStub.isInflated) {
            dataBinding.loginFailedStub.root.visibility = View.VISIBLE
        } else {
            val failedView = dataBinding.loginFailedStub.viewStub?.inflate() ?: return
            failedView.findViewById<TextView>(R.id.tv_retry).run {
                setOnClickListener {
                    hideLoginFailed()
                    dataBinding.spinkViewContainer.visibility = View.VISIBLE
                    ServiceManager.getInstance().emailService.loginMailBox()
                }
            }
        }
    }

    /**
     * 展示没有邮箱权限提示
     */
    private fun showEmailInvalid() {
        dataBinding.otherUnreadMark.isGone = true
        if (dataBinding.accountLackStub.isInflated) {
            dataBinding.accountLackStub.root.visibility = View.VISIBLE
        } else {
            dataBinding.accountLackStub.viewStub?.inflate()
        }
    }

    private fun hideEmailInvalid() {
        if (dataBinding.accountLackStub.isInflated) {
            dataBinding.accountLackStub.root.visibility = View.GONE
        }
    }

    private fun hideLoadFailed() {
        ExecutorFactory.removeMainHandlerCallbacks(failedLoadRunnable)
        if (dataBinding.loadFailedStub.isInflated) {
            dataBinding.loadFailedStub.root.visibility = View.GONE
        }
    }

    private fun hideLoginFailed() {
        if (dataBinding.loginFailedStub.isInflated) {
            dataBinding.loginFailedStub.root.visibility = View.GONE
        }
    }

    private fun selectMailboxAt(mailFolder: MailFolder) {
        TLog.info(TAG, "切换邮箱：$mailFolder")
        currentFolder = mailFolder
        updateCurrentSelectFolderShowInfo(mailFolder)
        EmailBusinessCentre.updateCurrentShowMailFolder(mailFolder)
        viewModel.refreshMailUnReadMark()
        EmailBusinessCentre.getMailBoxFromMailId(mailFolder.folderId)?.run {
            sharedViewModel.setQueryParam(this, mailFolder.folderId)
        }
        if (mailFolder.isFlaggedFolder) {
            dataBinding.iconMore.visibility = View.GONE
            dataBinding.iconMailSetting.visibility = View.VISIBLE
        } else {
            dataBinding.iconMore.visibility = View.VISIBLE
            dataBinding.iconMailSetting.visibility = View.GONE
        }
        childFragmentManager.beginTransaction()
            .replace(R.id.mailbox_container, fragmentProvider(mailFolder)).commit()
    }

    private fun updateCurrentSelectFolderShowInfo(mailFolder: MailFolder) {
        dataBinding.mailboxTitle.text = mailFolder.folderName
        dataBinding.mailboxSelector.isGone = false
        val count = mailFolder.showCountInMenu
        if (count > 0) {
            dataBinding.mailboxSubtitle.visibility = View.VISIBLE
            dataBinding.mailboxSubtitle.text = if (count > 99) {
                "(99+)"
            } else {
                "($count)"
            }
        } else {
            dataBinding.mailboxSubtitle.visibility = View.GONE
            dataBinding.mailboxSubtitle.text = ""
        }
    }

    private fun fragmentProvider(mailFolder: MailFolder): Fragment {
        return when (mailFolder.folderType) {

            MailConstants.FolderType.DRAFTS -> {
                EmailCommonReceiveFragment.instance(mailFolder.folderType)
            }

            MailConstants.FolderType.FLAGGED -> {
                EmailCommonReceiveFragment.instance(mailFolder.folderType)
            }

            MailConstants.FolderType.LABEL -> {
                EmailCommonReceiveFragment.instance(mailFolder.folderType)
            }

            MailConstants.FolderType.INBOX -> {
                EmailInboxFragment()
            }

            MailConstants.FolderType.OUTBOX -> {
                EmailCustomFragment.instance(mailFolder.folderId, mailFolder.folderName)
            }

            MailConstants.FolderType.TRASH -> {
                EmailCustomFragment.instance(mailFolder.folderId, mailFolder.folderName)
            }

            MailConstants.FolderType.SENDED -> {
                EmailCustomFragment.instance(mailFolder.folderId, mailFolder.folderName)
            }

            MailConstants.FolderType.REMOVED -> {
                EmailCustomFragment.instance(mailFolder.folderId, mailFolder.folderName)
            }

            MailConstants.FolderType.ARCHIVE -> {
                EmailCustomFragment.instance(mailFolder.folderId, mailFolder.folderName)
            }

            else -> {
                EmailCustomFragment.instance(mailFolder.folderId, mailFolder.folderName)
            }
        }
    }

    private fun showMailboxSelector() {
        viewModel.getFavoriteList { mailFolderList ->
            val mergedList = mutableListOf<MailFolderWrapper>()
            val favoriteList = mailFolderList.map {
                it.toMailFolderWrapper(0).also { it.isFavorite = true }
            }
            mergedList.addAll(favoriteList)
            val folderData = viewModel.getFolderData().value
            if (!folderData.isNullOrEmpty()) {
                mergedList.addAll(folderData)
            }
            MailboxSelector { folder ->
                TimeDifferenceUtil.getInstance().start(TimeTag.MAIL_LOAD_FOLDER)
                selectMailboxAt(folder)
                MailPoint.pointFolderSwitchOperation(folder.folderType)
            }.show(mergedList)
        }
        dataBinding.mailboxSelector.rotateUp()
    }

    private fun composeEmail() {
        MailPoint.pointAddNewMail()
        fragmentManager?.let {
            EmailEditFragment.instance(EmailEditFragment.TYPE_CREATE, "", "")
                .show(it, "EmailEditFragment")
        }
    }

    inner class MailboxSelector(val onItemClick: OnItemClickListener) : IEmailBoxSelectorCallback {

        /**
         * 邮箱选择的适配器
         */
        private val folderAdapter = MyMultiTypeAdapter()

        init {
            initView()
        }

        private fun initView() {
            folderAdapter.register(MailFolderWrapper::class.java,
                R.layout.email_main_item_mailbox_selection,
                object :
                    MyMultiTypeAdapter.ItemViewBinder<EmailMainItemMailboxSelectionBinding, MailFolderWrapper> {
                    override fun bind(
                        vdb: EmailMainItemMailboxSelectionBinding?,
                        item: MailFolderWrapper?,
                        linkIndex: Int
                    ) {
                        vdb?.setVariable(BR.callback, this@MailboxSelector)
                        vdb?.setVariable(BR.mailFolderWrapper, item)
                        vdb?.setVariable(BR.viewModel, viewModel)
                    }

                })
            folderAdapter.register(MailFolderGroupTitle::class.java,
                R.layout.email_main_item_mailbox_folder_group_title,
                object :
                    MyMultiTypeAdapter.ItemViewBinder<EmailMainItemMailboxFolderGroupTitleBinding, MailFolderGroupTitle> {
                    override fun bind(
                        vdb: EmailMainItemMailboxFolderGroupTitleBinding?,
                        item: MailFolderGroupTitle?,
                        linkIndex: Int
                    ) {
                        vdb?.setVariable(BR.customFolder, item)
                        vdb?.setVariable(BR.viewModel, viewModel)
                    }
                })
            folderAdapter.register(SplitLine::class.java,
                R.layout.email_main_item_mailbox_folder_split_line,
                object :
                    MyMultiTypeAdapter.ItemViewBinder<EmailMainItemMailboxFolderSplitLineBinding, SplitLine> {
                    override fun bind(
                        vdb: EmailMainItemMailboxFolderSplitLineBinding?,
                        item: SplitLine?,
                        linkIndex: Int
                    ) {
                    }
                })
        }

        /**
         * 邮箱文件夹的选择对话框
         */
        private val _mailboxSelector: HiPopupWindow by lazy {
            val contentView = layoutInflater.inflate(
                R.layout.email_main_window_mailbox_selection, view as? ViewGroup, false
            ).also {
                it.findViewById<View>(R.id.mask).setOnClickListener { _mailboxSelector.dismiss() }
                it.findViewById<RecyclerView>(R.id.mailbox_selection).run {
                    layoutManager = LinearLayoutManager(context)
                    adapter = folderAdapter
                    itemAnimator = null
                }
                it.findViewById<TextView>(R.id.manager_folder).setOnClickListener {
                    fragmentManager?.run {
                        showManageFolderFragment()
                        _mailboxSelector.dismiss()
                    }
                }
            }
            HiPopupWindow.Builder(activity).setContentView(contentView)
                .setWidth(QMUIDisplayHelper.getScreenWidth(context))
                .setAnimationStyle(R.style.pop_anim_style_alpha).build().also {
                    it.setCallBack {
                        dataBinding.mailboxSelector.rotateDown()
                    }
                }
        }

        fun show(mailFolderList: List<MailFolderWrapper>?) {
            if (mailFolderList.isNullOrEmpty()) {
                return
            }
            val dataList = mutableListOf<Any>()
            dataList.addAll(mailFolderList)
            folderList.clear()
            folderList.addAll(mailFolderList)

            val lastIdx = dataList.indexOfLast { it is MailFolderWrapper && it.isFavorite }
            if (lastIdx > -1) {
                dataList.add(0, MailFolderGroupTitle("收藏", 0))
                dataList.add(lastIdx + 2, MailFolderGroupTitle("所有文件夹", 0))
            }
            val firstLabelIdx = dataList.indexOfFirst { it is MailFolderWrapper && it.mailFolder.folderType == BSH_MAIL_FOLDER_TYPE_LABEL_VALUE }
            if (firstLabelIdx > -1) {
                dataList.add(firstLabelIdx, MailFolderGroupTitle("标签", 0))
            }
            folderAdapter.data = dataList
            _mailboxSelector.showAsDropDown(dataBinding.groupMailboxTitle)
        }

        override fun onSelectMailFolder(folder: MailFolder) {
            onItemClick(folder)
            _mailboxSelector.dismiss()
        }
    }

    inner class MailAccountSelector : IEmailAccountSelectorCallback {

        private val accountAdapter = MyMultiTypeAdapter()

        init {
            initView()
        }

        private fun initView() {
            accountAdapter.register(AccountInfoBean::class.java,
                R.layout.email_main_item_mail_account_item,
                object :
                    MyMultiTypeAdapter.ItemViewBinder<EmailMainItemMailAccountItemBinding, AccountInfoBean> {
                    override fun bind(
                        vdb: EmailMainItemMailAccountItemBinding?,
                        item: AccountInfoBean?,
                        linkIndex: Int
                    ) {
                        vdb?.setVariable(BR.item, item)
                        vdb?.setVariable(BR.callback, this@MailAccountSelector)
                        vdb?.setVariable(BR.viewModel, viewModel)
                    }
                })
            accountAdapter.register(SplitLine::class.java,
                R.layout.email_main_item_mailbox_folder_split_line,
                object :
                    MyMultiTypeAdapter.ItemViewBinder<EmailMainItemMailboxFolderSplitLineBinding, SplitLine> {
                    override fun bind(
                        vdb: EmailMainItemMailboxFolderSplitLineBinding?,
                        item: SplitLine?,
                        linkIndex: Int
                    ) {
                    }
                })
        }

        private val _mailAccountSelector: HiPopupWindow by lazy {
            val contentView = layoutInflater.inflate(
                R.layout.email_main_window_mail_account_selection, view as? ViewGroup, false
            ).also {
                it.findViewById<View>(R.id.mask)
                    .setOnClickListener { _mailAccountSelector.dismiss() }
                it.findViewById<RecyclerView>(R.id.mail_account_list).run {
                    layoutManager = LinearLayoutManager(context)
                    adapter = accountAdapter
                    itemAnimator = null
                }
            }
            HiPopupWindow.Builder(activity).setContentView(contentView)
                .setWidth(QMUIDisplayHelper.getScreenWidth(context))
                .setAnimationStyle(R.style.pop_anim_style_alpha).build()
        }

        fun show(list: MutableList<AccountInfoBean>) {
            accountAdapter.data = insertSplitLine(list)
            _mailAccountSelector.showAsDropDown(dataBinding.switchMailRoot)
        }

        private fun insertSplitLine(list: MutableList<AccountInfoBean>?): MutableList<Any> {
            val update = mutableListOf<Any>()
            list?.run {
                this.forEachIndexed { index, accountInfoBean ->
                    update.add(accountInfoBean)
                    if (index < list.size - 1) {
                        update.add(SplitLine())
                    }
                }
            }
            return update
        }

        override fun onSwitchMailAccount(item: AccountInfoBean) {
            if (item.currentShowAccount) {
                _mailAccountSelector.dismiss()
                return
            }
            showProgressDialog("", false)
            switchMailAccountStartTime = System.currentTimeMillis()
            hasSwitchMailAccount = true
            viewModel.switchMailAccount(item) { code: Int, list: MutableList<AccountInfoBean> ->
                TLog.info(TAG, "EmailMainFragment -> on click switch account to $item")
                if (code == MailConstants.Code.SUCCESS) {
                    currentFolder = null

                    showDefaultMailAccount(list)
                    EmailBusinessCentre.resetCache()
                    viewModel.refresh()
                } else {
                    ToastUtils.failure("账号切换失败")
                }
                _mailAccountSelector.dismiss()
            }
        }
    }

    companion object {
        private const val TAG = "EmailMainFragment"
    }
}