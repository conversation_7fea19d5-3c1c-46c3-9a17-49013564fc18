package com.twl.hi.module_email.callback

import com.twl.hi.module_email.domain.AccountInfoBean
import com.twl.hi.module_email.view_edit.UploadFileBean

interface IEmailEditorCallback : IEmailTitleCallback{

    /**
     * 取消上传
     */
    fun cancelUpload(uploadBean: UploadFileBean)

    /**
     * 预览文件
     */
    fun previewAttachFile(uploadBean: UploadFileBean)

    fun onSelectFileUpload()

    fun onClickExpandSenderList(accountInfoBean: AccountInfoBean)

    fun detachSchedule()

    fun editDraftSchedule()
}