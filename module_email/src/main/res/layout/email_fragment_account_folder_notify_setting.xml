<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.twl.hi.basic.util.ThemeUtils" />

        <variable
                name="viewModel"
                type="com.twl.hi.module_email.setting.folder.EmailAccountFolderNotifySettingViewModel" />
        <variable
                name="callback"
                type="com.twl.hi.module_email.callback.IEmailAccountFolderNotifySetting" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/color_F4F4F6">
        <RelativeLayout
                android:id="@+id/title_bar"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                app:layout_constraintTop_toTopOf="parent"
                android:paddingStart="6dp"
                android:background="@color/colorWhite"
                android:paddingEnd="16dp">

            <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:padding="10dp"
                    android:onClick="@{(v) -> callback.onClickBack()}"
                    android:layout_centerVertical="true"
                    android:src="@drawable/ic_icon_black_back" />

            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="设置需要通知的文件夹"
                    android:textColor="@color/app_black"
                    android:textSize="16sp"
                    android:layout_centerInParent="true" />
        </RelativeLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/check_all_ll"
                android:layout_width="match_parent"
                android:background="@color/colorWhite"
                app:layout_constraintTop_toBottomOf="@id/title_bar"
                android:layout_height="48dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:layout_marginTop="10dp"
                android:onClick="@{() -> callback.onSelectAll()}"
                >

            <CheckBox
                    android:id="@+id/check_all_item"
                    android:layout_width="24dp"
                    android:background="@color/transparent"
                    android:layout_height="24dp"
                    android:clickable="false"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/check_all_title"
                    android:button="@drawable/email_ic_checkbox"
                    />

            <TextView
                    android:id="@+id/check_all_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginStart="4dp"
                    android:textSize="15sp"
                    android:onClick="@{() -> callback.onSelectAll()}"
                    android:textColor="@color/color_15181D"
                    app:layout_constraintStart_toEndOf="@id/check_all_item"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:includeFontPadding="false"
                    android:singleLine="true"
                    android:text="全选"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/check_all_ll"
                android:background="@color/colorWhite"
                />

        <View
                android:id="@+id/split_line"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/check_all_ll"
                android:layout_marginStart="20dp"
                android:background="@color/color_F4F4F6"
                android:layout_marginEnd="20dp"
                />

        <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/folder_list"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintTop_toBottomOf="@id/split_line"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginBottom="66dp"
                app:layout_constraintVertical_bias="0"
                tools:listitem="@layout/email_item_folder_notify_setting"
                />

        <Button
                android:id="@+id/save"
                android:layout_width="match_parent"
                android:layout_height="46dp"
                android:text="保存"
                android:textColor="@color/app_white"
                android:background="@{ThemeUtils.useNewTheme ? @drawable/bg_selector_common_button_primary : @drawable/bg_selector_blue_button}"
                android:onClick="@{()->callback.onClickSave()}"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginBottom="10dp"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>