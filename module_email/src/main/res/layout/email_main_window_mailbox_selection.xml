<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#7F000000" />

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            android:orientation="vertical"
            android:background="@drawable/email_bg_main_mailbox_selection"
            >
        <com.twl.hi.basic.views.MaxHeightLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:max_height="600dp"
                >
            <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/mailbox_selection"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="15dp"
                    tools:itemCount="4"
                    tools:listitem="@layout/email_main_item_mailbox_selection" />
        </com.twl.hi.basic.views.MaxHeightLayout>

        <View
                android:id="@+id/split_line"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@color/email_split_line_bg"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                app:layout_constraintTop_toBottomOf="@id/title_bar"
                app:layout_constraintBottom_toTopOf="@id/receive"
                />

        <TextView
                android:id="@+id/manager_folder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawablePadding="10dp"
                android:singleLine="true"
                android:paddingTop="20dp"
                android:paddingEnd="20dp"
                android:paddingBottom="20dp"
                android:paddingStart="20dp"
                android:gravity="center_vertical"
                android:textColor="@color/color_0D0D1A"
                android:text="管理文件夹/标签"
                android:textSize="14sp"
                android:drawableStart="@drawable/hd_icon_settings"
                />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>