<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
                name="callback"
                type="com.twl.hi.module_email.callback.IMailMenuOptionSelectCallback" />
        <variable
                name="menuData"
                type="com.twl.hi.module_email.main.ui.MenuOption" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:onClick="@{(v) -> callback.onSelectOption(menuData)}">

        <ImageView
            android:id="@+id/ic_menu_"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/menuTitle"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:src="@{menuData.getDrawable()}"
            android:layout_width="20dp"
            android:layout_height="20dp"
            app:layout_constraintHorizontal_chainStyle="packed"
            tools:src="@drawable/email_ic_press_achive"
            />
        <TextView
                android:id="@+id/menuTitle"
                android:layout_marginLeft="5dp"
                app:layout_constraintLeft_toRightOf="@id/ic_menu_"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textSize="14sp"
                android:textColor="@color/sel_color_0d0d1a_b1b1b8"
                android:drawablePadding="6dp"
                android:text="@{menuData.menuTitle}"
                tools:text="存档"
                />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>