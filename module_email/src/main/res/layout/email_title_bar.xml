<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>

        <variable
                name="callback"
                type="com.twl.hi.module_email.callback.IEmailTitleCallback" />

        <variable
                name="bean"
                type="com.twl.hi.module_email.domain.TitleBarBean" />
    </data>

    <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@android:color/transparent"
            >

        <ImageView
                android:id="@+id/leftIcon"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginTop="17dp"
                android:onClick="@{(v) -> callback.onClickIcon(v)}"
                android:visibility="@{bean.leftIconVisible ? View.VISIBLE : View.GONE}"
                android:layout_marginStart="16dp"
                android:src="@drawable/email_icon_black_back"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true" />

        <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:singleLine="true"
                android:textColor="@color/color_0D0D1A"
                android:layout_marginStart="15dp"
                android:layout_toRightOf="@id/leftIcon"
                android:layout_toLeftOf="@id/icon_container"
                android:textStyle="bold"
                android:text="@{bean.title}"
                android:onClick="@{() -> callback.onClickTitle()}"
                android:visibility="@{bean.titleVisible ? View.VISIBLE : View.GONE}"
                tools:text="欢迎使用Microsoft邮箱欢迎使用Microsoft邮箱欢迎使用Microsoft邮箱"
                android:layout_centerInParent="true" />

        <LinearLayout
                android:id="@+id/icon_container"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:orientation="horizontal"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:layout_alignParentEnd="true"
                android:gravity="center"
                >

            <ImageView
                android:id="@+id/rightFourIcon"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginEnd="15dp"
                android:onClick="@{(v) -> callback.onClickIcon(v)}"
                android:src="@drawable/email_icon_edit"
                android:visibility="@{bean.rightIconFourVisible ? View.VISIBLE : View.GONE}"
                tools:src="@drawable/email_icon_edit"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/rightThreeIcon"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginEnd="15dp"
                android:onClick="@{(v) -> callback.onClickIcon(v)}"
                android:src="@{bean.rightIconThreeVisible == 1 ? @drawable/email_ic_default_red_flag : @drawable/email_ic_marked_red_flag}"
                android:visibility="@{bean.rightIconThreeVisible != 0 ? View.VISIBLE : View.GONE}"
                tools:src="@drawable/email_ic_default_red_flag"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/rightTwoIcon"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginEnd="15dp"
                android:onClick="@{(v) -> callback.onClickIcon(v)}"
                android:src="@drawable/email_icon_ai"
                android:visibility="@{bean.rightIconTwoVisible ? View.VISIBLE : View.GONE}" />

            <ImageView
                android:id="@+id/rightIcon"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginEnd="15dp"
                android:onClick="@{(v) -> callback.onClickIcon(v)}"
                android:src="@drawable/email_icon_delete"
                android:visibility="@{bean.rightIconVisible ? View.VISIBLE : View.GONE}" />

            <ImageView
                android:id="@+id/rightFiveIcon"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:onClick="@{(v) -> callback.onClickIcon(v)}"
                android:src="@drawable/email_icon_more_option"
                android:visibility="@{bean.rightIconFiveVisible ? View.VISIBLE : View.GONE}"  />

        </LinearLayout>

    </RelativeLayout>
</layout>