package com.twl.debug.sqlite

import android.content.ContentProvider
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import com.twl.debug.sqlite.config.Config
import com.twl.debug.sqlite.server.DebugSQLiteServer
import com.twl.debug.sqlite.util.LogUtil
import com.twl.debug.sqlite.util.NetUtil

/**
 * <AUTHOR>
 * 通过继承 ContentProvider 在 ActivityThread 中解析 ContentProvider 时自动完成初始化
 */
class DebugSQLiteProvider : ContentProvider() {

	override fun onCreate(): Boolean {
		context?.run {
			val port = Config.port
			DebugSQLiteServer(this, port).start()
			Config.dataBaseUrl = NetUtil.getDebugSQLiteAddress(this, port)
			LogUtil.print("sqlite debug address is: ${Config.dataBaseUrl}")
		}
		return true
	}

	override fun query(
		uri: Uri,
		projection: Array<out String>?,
		selection: String?,
		selectionArgs: Array<out String>?,
		sortOrder: String?
	): Cursor? = null

	override fun getType(uri: Uri): String? = null

	override fun insert(uri: Uri, values: ContentValues?): Uri? = null

	override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int = 0

	override fun update(
		uri: Uri,
		values: ContentValues?,
		selection: String?,
		selectionArgs: Array<out String>?
	): Int = 0
}