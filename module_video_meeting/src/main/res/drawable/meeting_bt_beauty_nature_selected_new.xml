<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:width="54dp"
        android:height="54dp"
        android:drawable="@mipmap/meeting_ic_natural">
    </item>

    <item
        android:width="54dp"
        android:height="54dp"
        android:drawable="@drawable/meeting_shape_primary_c12" />

    <!--白框是用来给相片裁圆角-->
    <item
        android:width="51dp"
        android:height="51dp"
        android:top="1.7dp"
        android:start="1.7dp">
        <shape android:shape="rectangle">
            <stroke android:color="@color/app_white" android:width="1.5dp"/>
            <corners android:radius="7.5dp" />
        </shape>
    </item>

</layer-list>