package com.twl.hi.videomeeting.status.impl

import android.text.TextUtils
import com.twl.hi.foundation.media.MediaConstants
import com.twl.hi.videomeeting.status.AbsMeetingStatus
import com.twl.hi.videomeeting.status.MeetingData

/**
 * <AUTHOR>
 * @date 2022/9/1
 */
class SingleWaitingMeetingStatus(meetingData: MeetingData) : AbsMeetingStatus(meetingData) {

    override fun singleWaiting() {
        meetingData.engine.initWaitingMediaPlayer()
        meetingData.engine.meetingRtcSyncInfo.setSelfVideoOpen(
            meetingData.engine.meetingNetSyncInfo.linkType == MediaConstants.MEETING_ROOM_LINK_TYPE_VIDEO
        )
        if (!TextUtils.isEmpty(meetingData.engine.meetingNetSyncInfo.roomId)) {
            meetingData.engine.getMeetingSdkInfo()
        }
    }

    override fun chatMeeting() {
        meetingData.setStatus(meetingData.chatMeetingStatus)
        meetingData.chatMeeting()
    }
}