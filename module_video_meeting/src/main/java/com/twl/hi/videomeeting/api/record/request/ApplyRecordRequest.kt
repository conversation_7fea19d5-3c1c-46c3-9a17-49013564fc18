package com.twl.hi.videomeeting.api.record.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.http.callback.AbsRequestCallback
import com.twl.http.client.BaseApiRequest
import com.twl.http.client.HttpResponse
import com.twl.http.config.RequestMethod

/**
 *@author: musa on 2023/3/29
 *@e-mail: <EMAIL>
 *@desc: 请求主持人开启录制
 */
class ApplyRecordRequest (mCallback: AbsRequestCallback<HttpResponse>?) :
    BaseApiRequest<HttpResponse>(mCallback) {

    @Expose
    @JvmField
    var roomId = ""

    override fun getUrl() = URLConfig.URL_APPLY_RECORD_MEETING

    override fun getMethod() = RequestMethod.POST

}