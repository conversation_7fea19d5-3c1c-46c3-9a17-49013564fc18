package com.twl.hi.videomeeting.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;

public class InviteMeetingPTPRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String calledIds;
    @Expose
    public String roomId;

    public InviteMeetingPTPRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_MEETING_INVITE;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}

