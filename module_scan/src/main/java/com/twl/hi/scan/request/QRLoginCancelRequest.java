package com.twl.hi.scan.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class QRLoginCancelRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String qrCode;

    public QRLoginCancelRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_QR_SCAN_LOGIN_CANCEL;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
