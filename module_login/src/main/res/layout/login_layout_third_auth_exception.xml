<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="20dp">

        <ImageView
            android:id="@+id/iv_inactivate"
            android:layout_width="121dp"
            android:layout_height="121dp"
            android:src="@drawable/login_ic_third_party_abnormal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="90dp"/>

        <TextView
            android:id="@+id/tv_inactive_title"
            style="@style/headline_small"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_inactivate"
            android:layout_margin="16dp"
            android:text="@string/login_request_abnormal"/>

        <TextView
            style="@style/body_medium"
            android:text="@string/login_request_abnormal_warning"
            android:textColor="@color/color_text_subtlest_858A99"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_inactive_title"
            android:layout_marginTop="8dp"/>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>