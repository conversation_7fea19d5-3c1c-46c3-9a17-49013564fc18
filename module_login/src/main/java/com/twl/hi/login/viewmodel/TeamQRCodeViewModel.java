package com.twl.hi.login.viewmodel;

import android.app.Application;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.view.View;

import androidx.databinding.ObservableField;

import com.twl.hi.basic.util.WxShareUtils;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Company;
import com.twl.hi.login.api.request.TeamQRCodeRequest;
import com.twl.hi.login.api.response.TeamQRCodeResponse;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.BitmapUtil;
import com.twl.utils.StringUtils;

import java.io.File;
import java.io.IOException;

import hi.kernel.Constants;
import hi.kernel.utils.MD5Utils;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.ToastUtils;

public class TeamQRCodeViewModel extends QRCodeViewModel {
    private ObservableField<String> teamName = new ObservableField<>();
    public String companyId;
    public String departmentId;

    public TeamQRCodeViewModel(Application application) {
        super(application);
    }

    public void loadData(Company companiesBean, String departmentId) {
        setTeamName(companiesBean.comName);
        this.companyId = companiesBean.comId;
        this.departmentId = departmentId;
        initInBackground();
    }

    @Override
    public String getFileName() {
        return MD5Utils.getMD5(mUrl.get() + teamName.get()) + ".jpg";
    }

    @Override
    protected void initData() {
        TeamQRCodeRequest request = new TeamQRCodeRequest(new BaseApiRequestCallback<TeamQRCodeResponse>() {
            @Override
            public void onSuccess(ApiData<TeamQRCodeResponse> data) {
                TeamQRCodeResponse response = data.resp;
                if (response == null || TextUtils.isEmpty(response.imgUrl)) {
                    return;
                }
                mUrl.set(response.imgUrl);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        if (StringUtils.isNotEmpty(departmentId)) {
            request.deptId = departmentId;
        }
        request.companyId = companyId;
        HttpExecutor.execute(request);
    }

    public ObservableField<String> getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName.set(teamName);
    }


    public void sharePic(View view, int type) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                Bitmap bitmap;
                switch (type) {
                    case Constants.SHARE_BOSS_HI:
                        String name = getFileName();
                        String path = ServiceManager.getInstance().getFileService().getFileRootPath();
                        File file = new File(path + name);
                        if (file.exists()) {
                            mFile.postValue(file);
                            return;
                        }
                        bitmap = getCacheBitmapFromView(view);
                        if (bitmap == null) {
                            return;
                        }
                        File bitmapFile = null;
                        try {
                            bitmapFile = ServiceManager.getInstance().getFileService().saveBitmap(bitmap, name);
                            if (bitmapFile != null) {
                                mFile.postValue(bitmapFile);
                            } else {
                                ToastUtils.failure("生成图片失败");
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                        break;
                    case Constants.SHARE_WECHAT:
                        bitmap = getCacheBitmapFromView(view);
                        if (bitmap == null) {
                            return;
                        }
                        WxShareUtils.shareImage(view.getContext(), Constants.SHARE_WECHAT, BitmapUtil.bitmapToByte(bitmap));
                        break;
                }
            }
        });
    }
}