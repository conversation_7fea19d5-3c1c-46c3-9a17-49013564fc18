package com.twl.hi.login;

import static lib.twl.common.ext.ViewExtKt.getStatusBarsHeight;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;

import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.export.select.bean.SelectBaseParams;
import com.twl.hi.export.select.bean.SelectConversationParams;
import com.twl.hi.export.select.router.SelectPageRouter;
import com.twl.hi.foundation.model.Company;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.login.callback.TeamQRCodeCallback;
import com.twl.hi.login.databinding.LoginActivityTeamQrCodeBinding;
import com.twl.hi.login.viewmodel.TeamQRCodeViewModel;

import java.io.File;
import java.util.ArrayList;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import lib.twl.common.permission.PermissionAvoidManager;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.HiPermissionUtil;
import lib.twl.common.util.ToastUtils;

public class TeamQRCodeActivity extends FoundationVMActivity<LoginActivityTeamQrCodeBinding, TeamQRCodeViewModel> implements TeamQRCodeCallback {
    PermissionAvoidManager manager;
    Company companiesBean;

    public static Intent createIntent(Context context, Company companiesBean, String departmentId) {
        Intent intent = new Intent(context, TeamQRCodeActivity.class);
        intent.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE, companiesBean);
        intent.putExtra(BundleConstants.BUNDLE_DATA_LONG, departmentId);
        return intent;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.login_activity_team_qr_code;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }


    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return false;
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getDataBinding().titleBar.tvBack.setVisibility(View.INVISIBLE);
        companiesBean = (Company) getIntent().getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
        if (companiesBean != null) {
            getViewModel().loadData(companiesBean, getIntent().getStringExtra(BundleConstants.BUNDLE_DATA_LONG));
        }
        getDataBinding().viewRoot.setPadding(0, getStatusBarsHeight(this), 0, 0);
        getViewModel().getFile().observe(this, new Observer<File>() {
            @Override
            public void onChanged(@Nullable File file) {
                SelectConversationParams params = new SelectConversationParams()
                        .setSendMessageType(MessageConstants.MSG_PIC)
                        .<SelectConversationParams>setTitle(getResources().getString(R.string.select_contact))
                        .<SelectConversationParams>setOrgVisible(SelectBaseParams.VISIBLE)
                        .<SelectConversationParams>setSearchVisible(SelectBaseParams.VISIBLE)
                        .<SelectConversationParams>setOtherVisible(SelectBaseParams.VISIBLE);
                ArrayList<File> files = new ArrayList<File>();
                files.add(file);
                params.sendMessageContent.content = files;
                params.sendMessageContent.msgShowContent = "[图片]";
                Bundle bundleSelect = new Bundle();
                bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
                AppUtil.startUri(TeamQRCodeActivity.this, SelectPageRouter.SELECT_CONVERSATION_ACTIVITY, bundleSelect, ActivityAnimType.UP_GLIDE);
            }
        });
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    public void saveQRCode() {
        manager = new PermissionAvoidManager(this);
        manager.requestPermission(HiPermissionUtil.getAccessSharedFilePermissions(), new PermissionAvoidManager.OnCommonPermissionCallBack() {
            @Override
            public void onRequestPermissionsResult(boolean hasPermission, boolean shouldShowAllRequestPermissionRationale) {
                if (hasPermission) {
                    getViewModel().savePicToLocal(getDataBinding().teamQrCode);
                } else {
                    ToastUtils.failure("您拒绝了文件读写权限，无法保存图片");
                }
            }
        });
    }

    @Override
    public void shareQRCode() {
        getViewModel().sharePic(getDataBinding().teamQrCode, Constants.SHARE_WECHAT);
    }
}
