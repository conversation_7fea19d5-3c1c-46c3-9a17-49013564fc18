package com.twl.mms.common;

import com.techwolf.lib.tlog.TLog;


import java.util.ArrayList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/3/23.
 */

public class CancelHelper {
    private static final String TAG = "CancelHelper";
    private ArrayList<Integer> mRemoves = new ArrayList(0);

    private Object mRemovesLock = new Object();
    public void addCancel(int id){
        synchronized (mRemovesLock) {
            TLog.info(TAG, "add cancel message id = [%d]", id);
            mRemoves.add(id);
        }
    }

    public boolean containsAndRemove(Integer obj){
        synchronized (mRemovesLock){
            boolean ret = mRemoves.remove(obj);
            if (ret) {
                TLog.info(TAG, "real cancel message id = [%d]", obj);
            }
            return ret;
        }
    }

    public void clear(){
        synchronized (mRemovesLock) {
            if (mRemoves.size() > 0) {
                TLog.info(TAG, "clear mRemoves!");
                mRemoves.clear();
                mRemoves.trimToSize();
            }
        }
    }
}
