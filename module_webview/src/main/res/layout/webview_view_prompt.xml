<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="274dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/webview_shape_rect_solid_fff_radius_8"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="20dp"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:textColor="#000000"
        android:textSize="18sp"
        android:textStyle="bold"
        tools:text="titletitletitle" />

    <EditText
        android:id="@+id/et_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="20dp"
        android:background="@drawable/webview_shape_rect_solid_f6f6f7_radius_8"
        android:gravity="center_vertical"
        android:lines="1"
        android:maxLines="1"
        android:padding="6dp"
        android:singleLine="true"
        android:textColor="#333"
        android:textColorHint="#999"
        android:textSize="12sp"
        tools:hint="请\n\n\输入\n内容" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="20dp"
        android:background="#999" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="1"
            android:maxEms="8"
            android:textColor="@color/image_color_blue"
            android:textSize="16sp"
            tools:text="Cancel" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="#999" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="1"
            android:maxEms="8"
            android:textColor="@color/image_color_blue"
            android:textSize="16sp"
            tools:text="一二三四五六七八九" />

    </LinearLayout>

</LinearLayout>