package com.twl.http;

import android.app.ActivityManager;
import android.content.Context;
import android.util.ArraySet;
import android.util.Log;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import com.facebook.imagepipeline.backends.okhttp3.OkHttpImagePipelineConfigFactory;
import com.facebook.imagepipeline.cache.CacheKeyFactory;
import com.facebook.imagepipeline.cache.MemoryCacheParams;
import com.facebook.imagepipeline.core.ImagePipelineConfig;
import com.facebook.imagepipeline.decoder.ImageDecoderConfig;
import com.facebook.imagepipeline.listener.BaseRequestListener;
import com.facebook.imagepipeline.listener.RequestListener;
import com.facebook.imagepipeline.request.ImageRequest;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.client.*;
import com.twl.http.config.HttpConfig;
import com.twl.http.config.HttpParams;
import com.twl.http.config.RequestMethod;
import com.twl.http.error.ErrorReason;
import com.twl.http.method.GetRequestBuilder;
import com.twl.http.method.PostRequestBuilder;
import com.twl.http.request.RequestCreator;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;

/**
 * 作者：ZhouYou
 * 日期：2017/2/21.
 */
public class HttpExecutor {

    private static final String TAG = "HttpExecutor";
    private static final String FRESCO_TAG = "Fresco";

    public static void initialize(Context context) {
        initialize(context, null);
    }

    public static void initialize(Context context, HttpConfig config) {
        if (config == null) {
            HttpConfig.getInstance().initDefault(context);
        } else {
            HttpConfig.getInstance().initCustom(config);
        }
    }

    public static ImagePipelineConfig initFrescoConfig(
            Context context,
            ImageDecoderConfig imageDecoderConfig,
            CacheKeyFactory cacheKeyFactory) {
        OkHttpClient client = OkHttpClientFactory.get().getClientFrescoImageLoader();

        BaseRequestListener frescoRequestListener = new BaseRequestListener() {
            @Override
            public void onRequestStart(ImageRequest request, Object callerContext, String requestId, boolean isPrefetch) {
                TLog.info(FRESCO_TAG, "onRequestStart >>> uri: " + request.getSourceUri() + " requestId: " + requestId);
            }

            @Override
            public void onRequestCancellation(String requestId) {
                TLog.info(FRESCO_TAG, "onRequestCancellation >>> requestId: " + requestId);
            }

            @Override
            public void onRequestFailure(ImageRequest request, String requestId, Throwable throwable, boolean isPrefetch) {
                TLog.info(FRESCO_TAG, "onRequestFailure >>> uri: " + request.getSourceUri() + ",isPrefetch: " + isPrefetch +
                        ",detail message:" + (throwable != null ? throwable.getMessage() : "empty"));
            }
        };
        ArraySet<RequestListener> baseRequestListeners = new ArraySet<>();
        baseRequestListeners.add(frescoRequestListener);

        // 配置内存缓存参数，避免OOM
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        int memoryClass = am.getMemoryClass();

        // 设置内存缓存大小为可用内存的1/8，但不超过64MB
        int maxCacheSize = Math.min(1024 * 1024 * memoryClass / 8, 64 * 1024 * 1024);
        int maxCacheEntries = 256;
        int maxEvictionQueueSize = maxCacheSize;
        int maxEvictionQueueEntries = maxCacheEntries;
        int maxCacheEntrySize = maxCacheSize / 8;

        MemoryCacheParams bitmapCacheParams = new MemoryCacheParams(
            maxCacheSize,
            maxCacheEntries,
            maxEvictionQueueSize,
            maxEvictionQueueEntries,
            maxCacheEntrySize
        );

        return OkHttpImagePipelineConfigFactory.newBuilder(context, client)
                .setImageDecoderConfig(imageDecoderConfig)
                .setCacheKeyFactory(cacheKeyFactory)
                .setRequestListeners(baseRequestListeners)
                .setBitmapMemoryCacheParamsSupplier(() -> bitmapCacheParams)
                .build();
    }

    /**
     * 执行Get/Post请求
     *
     * @param req
     * @param <T>
     */
    public static <T extends AbsApiResponse> Call execute(AbsApiRequest<T> req) {
        Call call = null;
        if (req instanceof AbsCommonApiRequest) {
            AbsCommonApiRequest request = (AbsCommonApiRequest) req;
            String url = request.getRequestUrl();
            String sk = request.getSK();
            HttpParams params = request.getParams();
            if (request.getMethod() == RequestMethod.GET) {
                call = new GetRequestBuilder()
                        .url(url)
                        .addParams(params)
                        .tag(request.getTag())
                        .headers(req.getHeaders())
                        .build()
                        .async(request.getRawResponseCallback(), request.isTimeoutSensitive());

            } else if (request.getMethod() == RequestMethod.POST) {
                call = new PostRequestBuilder()
                        .url(url)
                        .addParams(params)
                        .headers(req.getHeaders())
                        .sk(sk)
                        .mediaType(req.getMediaType())
                        .tag(request.getTag())
                        .build()
                        .async(request.getRawResponseCallback(), request.isTimeoutSensitive());
            }
        } else if (req instanceof AbsBatchApiRequest) {
            AbsBatchApiRequest request = (AbsBatchApiRequest) req;
            String url = request.getRequestUrl();
            HttpParams params = request.getBatchParams();
            call = new GetRequestBuilder()
                    .headers(req.getHeaders())
                    .url(url)
                    .addParams(params)
                    .mediaType(request.getMediaType())
                    .tag(request.getTag())
                    .build()
                    .async(request.getRawResponseCallback(), request.isTimeoutSensitive());
        } else if (req instanceof AbsUploadApiRequest) {
            AbsUploadApiRequest request = (AbsUploadApiRequest) req;
            String url = request.getRequestUrl();
            HttpParams params = request.getParams();
            call = new PostRequestBuilder()
                    .headers(req.getHeaders())
                    .url(url)
                    .addParams(params)
                    .mediaType(request.getMediaType())
                    .tag(request.getTag())
                    .build()
                    .xLoad(request.getRawResponseCallback());

        }
        req.start();
        return call;
    }

    /**
     * 执行Get/Post请求
     *
     * @param req
     * @param <T>
     */
    public static <T extends AbsApiResponse> Call attendanceExecute(AbsApiRequest<T> req) {
        Call call = null;
        AbsCommonApiRequest request = (AbsCommonApiRequest) req;
        String url = request.getRequestUrl();
        String sk = request.getSK();
        HttpParams params = request.getParams();
        if (request.getMethod() == RequestMethod.GET) {
            call = new GetRequestBuilder()
                    .url(url)
                    .addParams(params)
                    .tag(request.getTag())
                    .headers(req.getHeaders())
                    .build()
                    .attendanceCall(request.getRawResponseCallback());

        } else if (request.getMethod() == RequestMethod.POST) {
            call = new PostRequestBuilder()
                    .url(url)
                    .addParams(params)
                    .headers(req.getHeaders())
                    .sk(sk)
                    .mediaType(req.getMediaType())
                    .tag(request.getTag())
                    .build()
                    .attendanceCall(request.getRawResponseCallback());
        }
        req.start();
        return call;
    }

    /**
     * 同步执行Get/Post请求
     */
    @Nullable
    public static <T extends AbsApiResponse> ApiData<T> executeSync(AbsCommonApiRequest<T> request) {
        Call call = null;
        String url = request.getRequestUrl();
        HttpParams params = request.getParams();
        if (request.getMethod() == RequestMethod.GET) {
            call = new GetRequestBuilder()
                    .url(url)
                    .addParams(params)
                    .tag(request.getTag())
                    .headers(request.getHeaders())
                    .build()
                    .syncCall(request.getRawResponseCallback(), request.isTimeoutSensitive());
        } else if (request.getMethod() == RequestMethod.POST) {
            call = new PostRequestBuilder()
                    .url(url)
                    .addParams(params)
                    .mediaType(request.getMediaType())
                    .headers(request.getHeaders())
                    .tag(request.getTag())
                    .build()
                    .syncCall(request.getRawResponseCallback(), request.isTimeoutSensitive());
        } else {
            Log.e(TAG, "Only support get and post method!");
            return null;
        }
        return request.start(call);
    }

    public static Call upload(AbsUploadApiRequest uploadRequest) {
        return new PostRequestBuilder()
                .url(uploadRequest.getUrl())
                .addParams(uploadRequest.getParams())
                .headers(uploadRequest.getHeaders())
                .mediaType(uploadRequest.getMediaType())
                .build()
                .xLoad(uploadRequest.getRawResponseCallback());
    }

    /***
     *
     * 执行文件下载
     *
     * **/
    public static Call download(AbsFileDownloadRequest downloadRequest) {
        if (downloadRequest == null || !downloadRequest.check()) {
            return null;
        }
        try {
            return new GetRequestBuilder()
                    .headers(downloadRequest.getHeaders())
                    .url(downloadRequest.getUrl())
                    .tag(downloadRequest.getTag())
                    .mediaType(downloadRequest.getMediaType())
                    .build()
                    .xLoad(downloadRequest.getRawResponseCallback());
        } catch (Exception e) {
            LiveData<FileProgress> progressLiveData = OkHttpClientFactory.get().getProgress(downloadRequest.getUrl());
            if (progressLiveData != null) {
                FileProgress fileProgress = progressLiveData.getValue();
                if (fileProgress != null) {
                    fileProgress.setProgress(-1f);
                    OkHttpClientFactory.get().setProgress(downloadRequest.getUrl(), fileProgress);
                }
            }
            if (downloadRequest.getCallback() != null) {
                downloadRequest.getCallback().onFail(downloadRequest.getUrl(), new ErrorReason(ErrorReason.CODE_DEFAULT, ErrorReason.ERROR_DOWNLOAD, e));
            }
            TLog.error(TAG, "下载文件失败: " + downloadRequest.getUrl() + " " + e.getMessage());
        }
        return null;
    }

    public static void cancelAll() {
        OkHttpClientFactory.get().getClientDefault().dispatcher().cancelAll();
        OkHttpClientFactory.get().getClientFrescoImageLoader().dispatcher().cancelAll();
        OkHttpClientFactory.get().getClientUploadOrDownload().dispatcher().cancelAll();
    }


    /***
     *
     * 执行文件下载
     *
     * **/
    public static Call renewalDownload(AbsFileDownloadRequest downloadRequest) {
        if (downloadRequest == null || !downloadRequest.check()) {
            return null;
        }
        return new GetRequestBuilder()
                .headers(downloadRequest.getHeaders())
                .url(downloadRequest.getUrl())
                .tag(downloadRequest.getTag())
                .mediaType(downloadRequest.getMediaType())
                .build()
                .renewalLoad();

    }

    public static Call renewalUpload(AbsUploadApiRequest request) {
        if (request == null) {
            return null;
        }
        String url = request.getRequestUrl();
        HttpParams params = request.getParams();
        return new PostRequestBuilder()
                .headers(request.getHeaders())
                .url(url)
                .addParams(params)
                .tag(request.getTag())
                .build()
                .renewalUpload();
    }

    /**
     * 带进度监听的文件上传
     *
     * @param uploadRequest 上传请求
     * @param progressListener 进度监听器
     * @return Call对象，可用于取消请求
     */
    public static Call uploadWithProgress(AbsUploadApiRequest uploadRequest, ProgressRequestBody.UploadProgressListener progressListener) {
        if (uploadRequest == null) {
            return null;
        }

        String url = uploadRequest.getRequestUrl();
        HttpParams params = uploadRequest.getParams();

        // 构建带进度监听的 RequestBody
        RequestBody requestBody = HttpUtils.buildFileRequestParamsWithProgress(params, url, progressListener);

        // 创建带自定义 RequestBody 的 RequestCreator
        RequestCreator requestCreator = new RequestCreator(
                url,
                uploadRequest.getTag(),
                RequestMethod.POST.getValue(),
                params,
                uploadRequest.getHeaders(),
                uploadRequest.getMediaType(),
                null,
                requestBody
        );

        RequestCall requestCall = new RequestCall(requestCreator);

        // 使用 xLoad 方法来执行上传，这样可以使用正确的 OkHttpClient
        Call call = requestCall.xLoad(uploadRequest.getRawResponseCallback());

        uploadRequest.start();
        return call;
    }


}