package com.twl.hi.richtext.utils

object MDConstants {

    const val TABLE_LINE_PRE = "|-Table-|"
    const val TABLE_LINK_CHAR = "&&&"

    const val TABLE_TEXT_SIZE = 14.0f
    const val TABLE_TEXT_COLOR = "#0A1B33"
    const val TABLE_BORDER_WIDTH = 1.0f
    const val TABLE_BORDER_COLOR = "#E1E3E6"
    const val TABLE_BORDER_RADIUS = 8.0f
    const val TABLE_PADDING = 16.0f
    const val TABLE_RIGHT_MARGIN = 2.0f
    const val TABLE_CELL_MIN_WIDTH = 60.0f
    const val TABLE_CELL_MAX_WIDTH = 180.0f
    const val TABLE_CELL_PADDING = 10.0f
    const val TABLE_SPACING_MULTI = 1.2f

    const val MD_LINE_BOTTOM_DIVIDE = 8.0f
    const val MD_TEXT_VIEW_SPACING_MULTI = 1.4f

    // 代码块专用行间距，与TABLE_SPACING_MULTI保持一致
    const val CODE_LINE_SPACING_MULTI = 1.2f

    const val CODE_LINE_PRE = "&&-Code-&&"
    const val CODE_LINK_CHAR = "&-%%%-&"
    const val CODE_LINE_LINK_CHAR = "&-###-&"
    const val CODE_BORDER_COLOR = "#290A1B33"

    const val DIVIDE_LINE_PRE = "&&-Break-&&"
    const val DIVIDE_LINE_IDENTITY = "---"
}