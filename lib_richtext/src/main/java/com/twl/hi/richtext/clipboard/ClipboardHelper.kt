@file:JvmName("ClipboardHelper")

package com.twl.hi.richtext.clipboard

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.text.Annotation
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import com.techwolf.lib.tlog.TLog
import com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard
import com.twl.hi.foundation.model.message.extensioncard.data.modules.ModuleForMarkdown
import com.twl.hi.foundation.model.message.extensioncard.data.modules.ModuleForContent
import com.twl.hi.foundation.model.message.extensioncard.data.modules.ModuleForNote
import com.twl.hi.foundation.model.message.extensioncard.data.modules.ModuleForImage
import com.twl.hi.foundation.model.message.extensioncard.data.modules.ModuleForDivider
import com.twl.hi.foundation.utils.RichTextEscapes
import com.twl.hi.richtext.editor.helper.LogHelper
import com.twl.hi.richtext.parser.MarkdownGenerator

private const val IDENTITY_KEY_COPY_RICH_TEXT = "com.twl.hi.chat.RichTextCopying"

private const val CLIP_DATA_LABEL = "BossHi Copy"

private const val TAG = "rich--->ClipboardHelper"

private val Context.clipboardManager: ClipboardManager
    get() = applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager

fun Spanned?.toMarkdownText(): String {
    if (isNullOrEmpty()) {
        return ""
    }

    val markdownBuilder = StringBuilder()
    MarkdownGenerator(markdownBuilder).visit(this, imageNeedNewLine = false)
    return markdownBuilder.toString()
}

fun copyRichText(context: Context, styledText: Spanned?) {
    if (styledText.isNullOrEmpty()) {
        return
    }

    val markdownText = styledText.toMarkdownText()
    TLog.debug(TAG, "copyRichText Spanned : $markdownText")
    copyRichText(context, markdownText)
}

fun copyRichText(context: Context, markdownText: String?) {
    if (markdownText.isNullOrEmpty()) {
        return
    }

    try {
        val text = SpannableString(RichTextEscapes.toPlainText(markdownText))
        val annotation = Annotation(IDENTITY_KEY_COPY_RICH_TEXT, markdownText)
        text.setSpan(annotation, 0, text.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        TLog.debug(TAG, "copyRichText markdownText : $text")
        val clipData = ClipData.newPlainText(CLIP_DATA_LABEL, text)
        context.clipboardManager.setPrimaryClip(clipData)
    } catch (e: Exception) {
        TLog.error(TAG, e.message)
    }
}

fun copyPlainText(context: Context, markdownText: String?) {
    if (markdownText.isNullOrEmpty()) {
        return
    }

    try {
        val text = SpannableString(RichTextEscapes.toPlainText(markdownText))
        TLog.debug(TAG, "copyRichText markdownText : $text")
        val clipData = ClipData.newPlainText(CLIP_DATA_LABEL, text)
        context.clipboardManager.setPrimaryClip(clipData)
    } catch (e: Exception) {
        TLog.error(TAG, e.message)
    }
}

fun retrieveRichText(context: Context): Pair<CharSequence, String?> {
    val clipData = context.clipboardManager.primaryClip ?: return "" to null
    val plainText = SpannableStringBuilder()
    val richText = StringBuilder()
    for (i in (0 until clipData.itemCount)) {
        val item = clipData.getItemAt(i)
        plainText.append(item.coerceToText(context))
        (item.text as? Spanned)?.let {
            val annotations = it.getSpans(0, it.length, Annotation::class.java)
            annotations.filter { annotation ->
                annotation.key == IDENTITY_KEY_COPY_RICH_TEXT
            }.forEach { annotation ->
                richText.append(annotation.value)
            }
        }
    }
    TLog.debug(TAG, "retrieveRichText : ${LogHelper.translate(plainText.toString())}")
    TLog.debug(TAG, "retrieveRichText : ${LogHelper.translate(richText.toString())}")
    return plainText to richText.toString()
}

fun MessageForExtensionCard.toMarkDownText(): String {
    val builder = StringBuilder()

    // 获取卡片信息
    val cardInfo = this.cardInfo ?: return ""

    // 添加卡片标题（如果有）

    cardInfo.header?.text?.let { title ->
        if (title.isNotEmpty()) {
            builder.append("# ").append(title).append("\n")
        }
    }

    // 遍历所有模块，提取markdown内容
    cardInfo.modules?.forEach { module ->
        when (module) {
            is ModuleForMarkdown -> {
                // Markdown模块：直接获取markdown文本
                module.markdown?.content?.let { content ->
                    val text = content.text
                    if (!text.isNullOrEmpty()) {
                        builder.append(text)
                        // 确保模块之间有换行分隔
                        if (!text.endsWith("\n")) {
                            builder.append("\n")
                        }
                    }
                }
            }
            else->{

            }

            // 其他模块类型可以根据需要添加
        }
    }

    return builder.toString().trim()
}

fun copyOriginText(context: Context, markdownText: String?) {
    if (markdownText.isNullOrEmpty()) {
        return
    }

    try {
        // 直接复制原始的 Markdown 文本，不进行 toPlainText 转换
        val clipData = ClipData.newPlainText(CLIP_DATA_LABEL, markdownText)
        context.clipboardManager.setPrimaryClip(clipData)
    } catch (e: Exception) {
        TLog.error(TAG, e.message)
    }
}
