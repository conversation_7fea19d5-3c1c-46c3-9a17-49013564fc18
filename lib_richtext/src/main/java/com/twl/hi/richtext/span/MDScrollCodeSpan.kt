package com.twl.hi.richtext.span

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.text.StaticLayout
import android.text.TextPaint
import android.text.style.ReplacementSpan
import com.techwolf.lib.tlog.TLog
import com.twl.hi.richtext.utils.MDConstants
import com.twl.hi.richtext.utils.MDTableUtils
import com.twl.hi.richtext.utils.MDTouchDispatcher
import lib.twl.common.ext.dp
import kotlin.math.abs

class MDScrollCodeSpan(
    // 编程语言类型
    private val codeLanguage: String,
    // 代码内容
    private val codeContent: String,
    // 是否是最后一行
    private val isLast: Boolean,
    // 表格的宽度
    private var codeWidth: Int = 0,
    // 表格的高度
    private var codeHeight: Int = 0,
    // 滑动偏移量记录
    private var scrollX: Float = 0f,
    // 展示区域宽度
    private var canvasWidth: Int = -1
) : ReplacementSpan() {

    override fun getSize(
        paint: Paint,
        text: CharSequence?,
        start: Int,
        end: Int,
        fm: Paint.FontMetricsInt?
    ): Int {
        // 解析表格的实际宽高
        codeWidth = MDTableUtils.checkCodeWidth(codeContent, paint)
        codeHeight = MDTableUtils.checkCodeHeight(codeContent, codeWidth, paint)

        // 设置行高，确保与实际绘制高度一致
        fm?.let {
            // 使用实际计算的代码块高度，避免不必要的倍数调整
            val actualHeight = codeHeight
            val bottomMargin = if (isLast) 0 else (MDConstants.MD_LINE_BOTTOM_DIVIDE.dp.toInt())

            // 保存原始比例关系
            val originalHeight = abs(it.ascent) + abs(it.descent)
            val ascentRatio = if (originalHeight > 0) abs(it.ascent).toFloat() / originalHeight else 0.5f

            // 设置新的FontMetrics，确保总高度等于实际代码块高度
            it.top = -(actualHeight * 0.1f).toInt()
            it.ascent = -(actualHeight * ascentRatio).toInt()
            it.descent = (actualHeight * (1 - ascentRatio)).toInt() + bottomMargin
            it.bottom = actualHeight + bottomMargin
        }
        return codeWidth
    }

    override fun draw(
        canvas: Canvas,
        text: CharSequence?,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        if (canvasWidth == -1) {
            canvasWidth = canvas.width
        }

        // 保存画布状态
        canvas.save()

        // 绘制代码块背景
        drawCodeBackground(canvas, x, top.toFloat(), paint)

        // 设置裁剪区域
        canvas.clipRect(
            x,
            top.toFloat(),
            x + canvasWidth,
            top.toFloat() + codeHeight
        )

        // 应用滑动偏移
        if (MDTouchDispatcher.checkOnMove(this)) {
            canvas.translate(-scrollX, 0f)
        } else {
            scrollX = 0f
        }

        // 绘制代码内容
        drawCodeContent(canvas, x, top.toFloat(), paint)

        // 恢复画布状态
        canvas.restore()
    }

    private fun drawCodeBackground(canvas: Canvas, x: Float, y: Float, paint: Paint) {
        val originalColor = paint.color
        val originalStyle = paint.style

        // 绘制背景
        paint.color = Color.parseColor("#f7f8f9")
        paint.style = Paint.Style.FILL
        canvas.drawRoundRect(
            x,
            y,
            x + canvasWidth,
            y + codeHeight,
            MDConstants.TABLE_BORDER_RADIUS.dp,
            MDConstants.TABLE_BORDER_RADIUS.dp,
            paint
        )
//        canvas.drawRect(
//            x,
//            y,
//            x + canvasWidth,
//            y + codeHeight,
//            paint
//        )

        // 绘制边框
        paint.color = Color.parseColor(MDConstants.CODE_BORDER_COLOR)
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = MDConstants.TABLE_BORDER_WIDTH.dp
//        canvas.drawRect(
//            x,
//            y,
//            x + canvasWidth - MDConstants.TABLE_BORDER_WIDTH.dp * 2,
//            y + codeHeight - MDConstants.TABLE_BORDER_WIDTH.dp * 2,
//            paint
//        )
        canvas.drawRoundRect(
            x,
            y,
            x + canvasWidth - MDConstants.TABLE_BORDER_WIDTH.dp * 2,
            y + codeHeight,
            MDConstants.TABLE_BORDER_RADIUS.dp,
            MDConstants.TABLE_BORDER_RADIUS.dp,
            paint
        )

        // 恢复画笔状态
        paint.color = originalColor
        paint.style = originalStyle
    }

    private fun drawCodeContent(canvas: Canvas, x: Float, y: Float, paint: Paint) {
        // 创建代码文本画笔
        val codePaint = TextPaint(paint).apply {
            textSize = MDConstants.TABLE_TEXT_SIZE.dp
            color = Color.parseColor(MDConstants.TABLE_TEXT_COLOR)
            isAntiAlias = true
        }

        try {
            canvas.save()

            // 创建静态布局，使用统一的行间距常量
            val layout = StaticLayout.Builder
                .obtain(
                    codeContent,
                    0,
                    codeContent.length,
                    codePaint,
                    codeWidth
                )
                .setIncludePad(true)
                .setLineSpacing(0f, MDConstants.CODE_LINE_SPACING_MULTI)
                .build()

            val padding = MDConstants.TABLE_CELL_PADDING.dp
            // 移动到绘制位置
            canvas.translate(x + padding, y + padding)

            // 绘制代码
            layout.draw(canvas)
        } catch (e: Exception) {
            TLog.error("drawCode()", e.message)
        } finally {
            canvas.restore()
        }
    }

    // 处理滑动事件
    fun handleScroll(dx: Float): Boolean {
        scrollX += dx
        return if (scrollX <= 0) {
            false
        } else {
            scrollX + canvasWidth <= codeWidth
        }
    }
}