package com.twl.hi.richtext.utils

import android.graphics.Paint
import android.text.StaticLayout
import android.text.TextPaint
import android.text.TextUtils
import com.techwolf.lib.tlog.TLog
import com.twl.hi.richtext.parser.item.table.MDTableItem
import com.twl.hi.richtext.parser.item.table.MDTableAlign
import com.twl.hi.richtext.parser.item.table.MDTableCell
import com.twl.hi.richtext.parser.item.table.MDTableData
import com.twl.hi.richtext.parser.item.table.MDTableLineInfo
import com.twl.hi.richtext.parser.item.table.MDTableRow
import com.twl.hi.richtext.parser.item.table.MDTableRowType
import lib.twl.common.ext.dp

object MDTableUtils {

    @JvmStatic
    fun mockMDValue(preText: String?): String? {
        if (preText == null || TextUtils.isEmpty(preText)) {
            return preText
        }
        return StringBuilder().append(preText)
            .append("\n> 这是一段引用")
            .append("\n>> 嵌套引用  ")
            .append("\n>")
            .append("\n> - 引用内列表  ")
            .append("\n> `引用内代码`")
            .append("\n>")
            .append("\n> 这是一段引用这是一段引用这是一段引用这是一段引用这是一段引用")
            .append("\n1. 有序列表1")
            .append("\n  1. 有序列表1.1，间距2")
            .append("\n2. 有序列表2")
            .append("\n   2. 有序列表2.2，间距3")
            .append("\n      3. 有序列表2.3，间距6")
            .append("\n- 无序列表1")
            .append("\n  - 无序列表1.1，间距2")
            .append("\n    - 无序列表1.1，间距4")
            .append("\n      - 无序列表1.1，间距6")
            .append("\n        - 无序列表1.1，间距8")
            .append("\n- 无序列表2")
            .append("\n   - 无序列表2.2，间距3")
            .append("\n      - 无序列表2.2，间距6")
            .append("\n         - 无序列表2.2，间距9")
            .append("\n            - 无序列表2.2，间距12")
            .append("\n# 一级标题H1")
            .append("\n## 二级标题H2")
            .append("\n### 三级标题H3")
            .append("\n#### 四级标题H4")
            .append("\n##### 五级标题H5")
            .append("\n###### 六级标题H6")
            .append("\n- [ ] 未勾选")
            .append("\n- [x] 已勾选")
            .append("\n  - [x] 已勾选")
            .append("\n    - [ ] 未勾选")
            .append("\n```java\npublic\n```")
            .append("\n| 左对齐 | 居中对齐 | |")
            .append("\n|:-------|:--------:|-------:|")
            .append("\n| 单元格 |  内容    |   \$100 |")
            .append("\n| 跨列合并→ || 右对齐内容右对齐内容 |")
            .append("\n| 跨列合并→ || 右对齐内容右对齐内 |")
            .append("\n```python\ndef hello():\n    print(\"语法高亮代码块语法高亮代码块语法高亮代码块语法高亮代码块\")\n```")
            .append(
                "\n```java\n" +
                        "private static CardPriceBuilder buildPrice25(long priceId, int beanCount, boolean select) {\n" +
                        "        CardPriceBuilder builder = CardPriceBuilder.obj(priceId)\n" +
                        "                .buildDiscountShowType()\n" +
                        "                .buildBusiness(8)\n" +
                        "                .buildBeanCount(beanCount)\n" +
                        "                .buildDescList()\n" +
                        "                .buildDetailButton()\n" +
                        "                .buildPriceType(33);\n" +
                        "        if (select) {\n" +
                        "            builder.buildSelect();\n" +
                        "        }\n" +
                        "        return builder;\n" +
                        "    }\n" +
                        "```\n"
            )
            .append(
                "\n```java\n" +
                        "private static CardPriceBuilder buildPrice25(long priceId, int beanCount, boolean select) {\n" +
                        "        CardPriceBuilder builder = CardPriceBuilder.obj(priceId)\n" +
                        "                .buildDiscountShowType()\n" +
                        "                .buildBusiness(8)\n" +
                        "                .buildBeanCount(beanCount)\n" +
                        "                .buildDescList()\n" +
                        "                .buildDetailButton()\n" +
                        "                .buildPriceType(33);\n" +
                        "        if (select) {\n" +
                        "            builder.buildSelect();\n" +
                        "        }\n" +
                        "        return builder;\n" +
                        "    }\n" +
                        "```\n"
            )
            .append(
                "\n当然可以为你准备一个简单的周报模板。由于无法生成确切的6000字符内容，这里提供一个示例，包含一些通用的周报元素和简单的表格代码，你可以根据需要进行扩展和修改。\n" +
                        "\n" +
                        "---\n" +
                        "\n" +
                        "**周报**\n" +
                        "\n" +
                        "**日期：** 2023年10月30日\n" +
                        "\n" +
                        "**撰写人：** XXX\n" +
                        "\n" +
                        "---\n" +
                        "\n" +
                        "### 一、项目进展\n" +
                        "\n" +
                        "1. **项目A**\n" +
                        "   - 完成了模块1的开发，进行了初步测试。\n" +
                        "   - 解决了上周遗留的Bug，改善了系统稳定性。\n" +
                        "   - 下周计划：开始模块2的设计并进行需求分析。\n" +
                        "\n" +
                        "2. **项目B**\n" +
                        "   - 用户反馈收集完成，正在分析数据。\n" +
                        "   - 提升了用户界面的响应速度。\n" +
                        "   - 下周计划：准备用户调研报告，提升用户体验。\n" +
                        "\n" +
                        "### 二、工作计划\n" +
                        "\n" +
                        "| 时间       | 事项          | 负责人   | 状态       |\n" +
                        "|------------|---------------|---------|------------|\n" +
                        "| 周一       | 项目A模块1测试| 张三    | 已完成     |\n" +
                        "| 周二       | 用户反馈分析  | 李四    | 进行中     |\n" +
                        "| 周三       | 项目B界面优化 | 王五    | 已完成     |\n" +
                        "| 周四       | 项目A设计讨论 | 张三    | 待开始     |\n" +
                        "| 周五       | 调研报告撰写  | 李四    | 待开始     |\n" +
                        "\n" +
                        "### 三、问题与解决方案\n" +
                        "\n" +
                        "- **问题：** 项目A的模块1测试中发现数据处理速度较慢。\n" +
                        "  - **解决方案：** 优化算法，提高处理效率。\n" +
                        "\n" +
                        "- **问题：** 项目B界面存在兼容性问题。\n" +
                        "  - **解决方案：** 更新兼容性测试方案，确保在不同设备上的表现一致。\n" +
                        "\n" +
                        "### 四、下周计划\n" +
                        "\n" +
                        "- 完成项目A模块2的需求分析。\n" +
                        "- 提交项目B用户调研报告。\n" +
                        "- 开始新的项目C的可行性研究。\n" +
                        "\n" +
                        "### 五、其他事项\n" +
                        "\n" +
                        "- 本周团队会议安排在周三下午2点。\n" +
                        "- 请各部门提交下个月的预算计划。\n" +
                        "\n" +
                        "---\n" +
                        "\n" +
                        "**代码片段示例**\n" +
                        "\n" +
                        "```python\n" +
                        "# 示例代码：数据处理脚本\n" +
                        "def process_data(data):\n" +
                        "    # 优化算法，提高数据处理效率\n" +
                        "    processed_data = [d*2 for d in data if d > 0]\n" +
                        "    return processed_data\n" +
                        "\n" +
                        "# 测试数据\n" +
                        "data = [-1, 2, 3, 4]\n" +
                        "result = process_data(data)\n" +
                        "print(\"Processed Data:\", result)\n" +
                        "```\n" +
                        "\n" +
                        "---\n" +
                        "\n" +
                        "以上是周报的基本框架，你可以根据具体的工作内容和需求进行详细的补充和扩展。希望对你有帮助！\n"
            )
            .append(
                "\n当然，请查看下面的周报表格：\n" +
                        "\n" +
                        "| 周次 | 日期范围         | 完成任务                             | 进展情况      | 问题与挑战                        | 下周计划                         |\n" +
                        "|------|------------------|----------------------------------|------------|-------------------------------|-------------------------------|\n" +
                        "| 第1周 | 2023/10/02-2023/10/08 | - 完成项目需求分析<br>- 设计初步方案 | 进行中       | - 资源不足<br>- 时间紧迫           | - 完善方案<br>- 开始开发工作       |\n" +
                        "| 第2周 | 2023/10/09-2023/10/15 | - 完成模块开发<br>- 编写测试用例    | 按计划进行   | - 模块间协调问题                  | - 进行单元测试<br>- 修复缺陷       |\n" +
                        "| 第3周 | 2023/10/16-2023/10/22 | - 完成单元测试<br>- 缺陷修复       | 延误        | - 测试时间不足<br>- 缺陷数量多     | - 开始集成测试<br>- 优化性能       |\n" +
                        "| 第4周 | 2023/10/23-2023/10/29 | - 完成集成测试<br>- 性能优化       | 进行中       | - 兼容性问题                      | - 进行用户验收测试<br>- 完善文档   |\n" +
                        "\n" +
                        "此表格可以根据具体项目和需求进行调整。希望这能帮助您进行项目管理和跟踪。\n"
            )
            .append(
                "\n在 Jupyter Notebook 中，使用 Python 的可视化库（如 Matplotlib、Seaborn 等）保存图表是一个常见的需求。下面是如何使用这些库保存图表的基本步骤：\n" +
                        "\n" +
                        "### 使用 Matplotlib 保存图表\n" +
                        "\n" +
                        "1. **绘制图表**：\n" +
                        "   首先，你需要使用 Matplotlib 绘制一个图表。例如：\n" +
                        "   ```python\n" +
                        "   import matplotlib.pyplot as plt\n" +
                        "\n" +
                        "   # 创建一些示例数据\n" +
                        "   x = [1, 2, 3, 4, 5]\n" +
                        "   y = [10, 20, 25, 30, 40]\n" +
                        "\n" +
                        "   # 绘制图表\n" +
                        "   plt.plot(x, y)\n" +
                        "   ```\n" +
                        "\n" +
                        "2. **保存图表**：\n" +
                        "   使用 `plt.savefig()` 方法来保存图表。在保存图表之前，通常不需要调用 `plt.show()`，因为 `plt.show()` 会清除当前图表。\n" +
                        "   ```python\n" +
                        "   # 保存图表为 PNG 文件\n" +
                        "   plt.savefig('my_plot.png')\n" +
                        "\n" +
                        "   # 保存图表为 PDF 文件\n" +
                        "   plt.savefig('my_plot.pdf')\n" +
                        "\n" +
                        "   # 保存图表为 SVG 文件\n" +
                        "   plt.savefig('my_plot.svg')\n" +
                        "   ```\n" +
                        "\n" +
                        "3. **指定保存选项**：\n" +
                        "   你可以使用 `bbox_inches='tight'` 选项来减少图表周围的空白边距：\n" +
                        "   ```python\n" +
                        "   plt.savefig('my_plot.png', bbox_inches='tight')\n" +
                        "   ```\n" +
                        "\n" +
                        "### 使用 Seaborn 保存图表\n" +
                        "\n" +
                        "Seaborn 是基于 Matplotlib 的，所以保存图表的方式基本相同。你只需在绘制完图表后使用 Matplotlib 的 `savefig` 方法。\n" +
                        "\n" +
                        "```python\n" +
                        "import seaborn as sns\n" +
                        "import matplotlib.pyplot as plt\n" +
                        "\n" +
                        "# 创建一些示例数据\n" +
                        "tips = sns.load_dataset('tips')\n" +
                        "\n" +
                        "# 使用 Seaborn 绘制图表\n" +
                        "sns.scatterplot(data=tips, x='total_bill', y='tip')\n" +
                        "\n" +
                        "# 保存图表\n" +
                        "plt.savefig('seaborn_plot.png', bbox_inches='tight')\n" +
                        "```\n" +
                        "\n" +
                        "### 使用 Plotly 保存图表\n" +
                        "\n" +
                        "Plotly 提供了交互式的图表，保存方法与 Matplotlib 和 Seaborn 略有不同。\n" +
                        "\n" +
                        "```python\n" +
                        "import plotly.express as px\n" +
                        "\n" +
                        "# 创建一些示例数据\n" +
                        "df = px.data.iris()\n" +
                        "\n" +
                        "# 使用 Plotly 绘制图表\n" +
                        "fig = px.scatter(df, x='sepal_width', y='sepal_length')\n" +
                        "\n" +
                        "# 保存图表为 HTML 文件（包含交互功能）\n" +
                        "fig.write_html('plotly_plot.html')\n" +
                        "\n" +
                        "# 保存图表为静态图像（需要安装 kaleido）\n" +
                        "fig.write_image('plotly_plot.png')\n" +
                        "```\n" +
                        "\n" +
                        "> **注意**：要保存 Plotly 图表为静态图像，你需要安装 `kaleido`，可以通过以下命令进行安装：\n" +
                        "> ```bash\n" +
                        "> pip install kaleido\n" +
                        "> ```\n" +
                        "\n" +
                        "通过这些方法，你可以轻松地将图表保存为不同格式的文件，以便用于报告、展示或其他用途。\n"
            )
            .toString()
    }

    @JvmStatic
    fun safeSubString(str: String?, start: Int, end: Int): String? {
        if (str == null) {
            return null
        }
        if (start < 0) {
            return null
        }
        if (end > str.length) {
            return null
        }
        return try {
            str.substring(start, end)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 检查行是否是表格行
     */
    @JvmStatic
    fun checkTableLine(lineValue: String?): MDTableLineInfo? {
        if (lineValue?.startsWith("|") == false || lineValue?.endsWith("|") == false) {
            return null
        }
        val cellArray = lineValue?.split("|")
        if (cellArray.isNullOrEmpty()) {
            return null
        }
        try {
            cellArray.forEach { cellValue ->
                if (!TextUtils.isEmpty(cellValue) && !checkCharValueIsAlign(cellValue.trim())) {
                    return MDTableLineInfo(lineValue, cellArray.size, 1)
                }
            }
            return MDTableLineInfo(lineValue, cellArray.size, 0)
        } catch (e: Exception) {
            return null
        }
    }

    /**
     * 检查字符是否是表格对齐字符
     */
    @JvmStatic
    private fun checkCharValueIsAlign(charValue: String): Boolean {
        val midValue = when {
            charValue.startsWith(":") && charValue.endsWith(":") -> {
                safeSubString(charValue, 1, charValue.length - 1)
            }

            charValue.startsWith(":") -> {
                safeSubString(charValue, 1, charValue.length)
            }

            charValue.endsWith(":") -> {
                safeSubString(charValue, 0, charValue.length - 1)
            }

            else -> {
                charValue
            }
        }
        midValue?.toCharArray()?.forEach {
            if (it != ':' && it != '-') {
                return false
            }
        }
        return true
    }

    /**
     * 检查行对齐值
     */
    @JvmStatic
    fun checkRowAlignValue(lineInfo: MDTableLineInfo?): String {
        val alignBuilder = StringBuilder().append("|")
        if (lineInfo == null || lineInfo.isContent == 1) {
            return alignBuilder.toString()
        }
        val cellArray = lineInfo.lineValue.split("|")
        cellArray.forEach { cellValue ->
            if (!TextUtils.isEmpty(cellValue)) {
                val trimValue = cellValue.trim()
                when {
                    trimValue.startsWith(":") && trimValue.endsWith(":") -> {
                        alignBuilder.append(MDTableAlign.CENTER).append("|")
                    }

                    trimValue.startsWith(":") -> {
                        alignBuilder.append(MDTableAlign.LEFT).append("|")
                    }

                    trimValue.endsWith(":") -> {
                        alignBuilder.append(MDTableAlign.RIGHT).append("|")
                    }

                    else -> {
                        alignBuilder.append(MDTableAlign.CENTER).append("|")
                    }
                }
            }
        }
        return alignBuilder.toString()
    }

    /**
     * 根据行数据，构建表格数据
     */
    @JvmStatic
    fun buildTableLineItem(lineValue: String): MDTableItem? {
        val eArray = lineValue.split(MDConstants.TABLE_LINK_CHAR)
        if (eArray.size != 5 || eArray[0] != MDConstants.TABLE_LINE_PRE || eArray[3] != "1") {
            return null
        }
        val contentArray = eArray[1].split("|")
        val alignArray = eArray[2].split("|")
        val rowType = when (eArray[4]) {
            "0" -> MDTableRowType.HEADER
            "1" -> MDTableRowType.NORMAL
            "2" -> MDTableRowType.FOOTER
            else -> MDTableRowType.NORMAL
        }
        return try {
            MDTableItem(
                contentArray.subList(1, contentArray.size - 1).toTypedArray(),
                getAlignArray(alignArray.subList(1, alignArray.size - 1)),
                rowType
            )
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 获取表格格式列表
     */
    @JvmStatic
    private fun getAlignArray(alignList: List<String>): Array<Int> {
        val alignArray = arrayOfNulls<Int>(alignList.size)
        alignList.forEachIndexed { index, s ->
            alignArray[index] = when (s) {
                "0" -> MDTableAlign.LEFT
                "1" -> MDTableAlign.CENTER
                "2" -> MDTableAlign.RIGHT
                else -> MDTableAlign.LEFT
            }
        }
        return alignArray.requireNoNulls()
    }

    @JvmStatic
    fun checkMDTableSpanLineCount(textPaint: TextPaint, textArray: Array<String>): Int {
        if (textArray.isEmpty()) {
            return 1
        }
        return try {
            var maxCount = 1
            val holderWidth = checkHolderWidth(textPaint, textArray)
            textArray.forEach {
                val textWidth = textPaint.measureText(it).toInt()
                val lineCount = if (textWidth % holderWidth == 0) {
                    textWidth / holderWidth
                } else {
                    textWidth / holderWidth + 1
                }
                if (lineCount > maxCount) {
                    maxCount = lineCount
                }
            }
            maxCount
        } catch (e: Exception) {
            TLog.error("", e.message)
            1
        }
    }

    @JvmStatic
    private fun checkHolderWidth(textPaint: TextPaint, textArray: Array<String>): Int {
        val maxLineValue = "占位文案占位文案占位文案"
        val customStrWidth = MDConstants.TABLE_TEXT_SIZE.dp * maxLineValue.length

        val curStrWidth = textPaint.textSize * maxLineValue.length
        val curTextWidth = textPaint.measureText(maxLineValue).toInt()

        val customTextWidth = customStrWidth * 1.0f / curStrWidth * curTextWidth
        val realTextLength = customTextWidth / textPaint.textSize

        val textHolderValue = maxLineValue.substring(0, realTextLength.toInt() / textArray.size)
        return textPaint.measureText(textHolderValue).toInt()
    }

    @JvmStatic
    fun tableCellPadding(cellCount: Int): Int {
        val maxPaddingData = 10
        if (cellCount <= 1) {
            return maxPaddingData.dp.toInt()
        }
        var paddingData = maxPaddingData - cellCount * 1
        if (paddingData < 6) {
            paddingData = 6
        }
        return paddingData.dp.toInt()
    }

    @JvmStatic
    fun tableTopValue(top: Int, @MDTableRowType rowType: Int): Int {
        return if (rowType == MDTableRowType.HEADER) {
            top
        } else {
            top - MDConstants.MD_LINE_BOTTOM_DIVIDE.dp.toInt()
        }
    }

    @JvmStatic
    fun tableBottomValue(bottom: Int): Int {
        return bottom - MDConstants.MD_LINE_BOTTOM_DIVIDE.dp.toInt()
    }

    @JvmStatic
    fun buildMDTableData(content: String): MDTableData {
        val lineArray = content.trim().split('\n')
        val tableLineList = mutableListOf<String>()
        // 提取表格行
        for (line in lineArray) {
            val trimmedLine = line.trim()
            if (trimmedLine.startsWith('|') && trimmedLine.endsWith('|')) {
                tableLineList.add(trimmedLine)
            }
        }
        // 表格至少要有一个表格头和对齐方式
        if (tableLineList.size < 2) {
            return MDTableData(emptyList(), ArrayList(), 0)
        }
        // 解析对齐方式（第二行）
        val alignList = parseCellAlign(tableLineList[1])
        // 解析数据行
        val rowList = ArrayList<MDTableRow>()

        try {
            tableLineList.forEachIndexed { lineIndex, line ->
                // 跳过对齐行
                if (lineIndex != 1) {
                    val cellList = line.split('|')
                        .drop(1) // 移除第一个空元素
                        .dropLast(1) // 移除最后一个空元素
                        .mapIndexed { index, cellContent ->
                            val align = if (index < alignList.size) {
                                alignList[index]
                            } else {
                                MDTableAlign.CENTER
                            }
                            MDTableCell(cellContent.trim(), align)
                        }

                    rowList.add(MDTableRow(cellList))
                }
            }
            val columnCount = rowList.maxOfOrNull { it.cellList.size } ?: 0
            return MDTableData(rowList, ArrayList(), columnCount)
        } catch (e: Exception) {
            return MDTableData(rowList, ArrayList(), 0)
        }
    }

    @JvmStatic
    private fun parseCellAlign(alignLine: String): List<Int> {
        return alignLine.split('|')
            // 移除第一个空元素
            .drop(1)
            // 移除最后一个空元素
            .dropLast(1)
            .map { cell ->
                val trimmed = cell.trim()
                when {
                    trimmed.startsWith(':') && trimmed.endsWith(':') -> {
                        MDTableAlign.CENTER
                    }

                    trimmed.startsWith(':') -> {
                        MDTableAlign.LEFT
                    }

                    trimmed.endsWith(':') -> {
                        MDTableAlign.RIGHT
                    }

                    else -> {
                        MDTableAlign.CENTER
                    } // 默认居中
                }
            }
    }

    @JvmStatic
    fun checkTableWidth(tableData: MDTableData?, paint: Paint): Int {
        if (tableData == null || tableData.rowList.isEmpty()) {
            return 0
        }
        val cellPadding = MDConstants.TABLE_CELL_PADDING.dp.toInt() * 2
        val cellMinWidth = MDConstants.TABLE_CELL_MIN_WIDTH.dp.toInt() + cellPadding
        val cellMaxWidth = MDConstants.TABLE_CELL_MAX_WIDTH.dp.toInt() + cellPadding
        var tableWidth = 0.0f

        val textPaint = TextPaint(paint)
        textPaint.textSize = MDConstants.TABLE_TEXT_SIZE.dp


        for (columnIndex in 0 until tableData.columnCount) {
            var rowMaxWidth = 0
            for (rowIndex in 0 until tableData.rowList.size) {
                if (columnIndex >= tableData.rowList[rowIndex].cellList.size) {
                    continue
                }
                try {
                    val cell = tableData.rowList[rowIndex].cellList[columnIndex]
                    val cellWidth = textPaint.measureText(cell.content) + cellPadding
                    val validCellWidth = if (cellWidth < cellMinWidth) {
                        cellMinWidth
                    } else if (cellWidth > cellMaxWidth) {
                        cellMaxWidth
                    } else {
                        cellWidth.toInt()
                    }
                    if (validCellWidth > rowMaxWidth) {
                        rowMaxWidth = validCellWidth
                    }
                } catch (e: Exception) {
                    TLog.error("MDTableUtils", "checkTableWidth: $e")
                }
            }
            tableData.rowWidthList.add(rowMaxWidth)
            tableWidth += rowMaxWidth
        }
        return tableWidth.toInt()
    }

    @JvmStatic
    fun checkTableHeight(tableData: MDTableData?, paint: Paint): Int {
        if (tableData == null || tableData.rowList.isEmpty()) {
            return 0
        }
        try {
            val cellPadding = MDConstants.TABLE_CELL_PADDING.dp.toInt() * 2
            var tableHeight = 0.0f

            paint.textSize = MDConstants.TABLE_TEXT_SIZE.dp
            tableData.rowList.forEach { mdTableRow ->
                var rowMaxHeight = 0
                mdTableRow.cellList.forEachIndexed { index, it ->
                    val staticLayout = StaticLayout.Builder.obtain(
                        it.content,
                        0,
                        it.content.length,
                        TextPaint(paint),
                        tableData.rowWidthList[index]
                    )
                        .setIncludePad(true)
                        .setLineSpacing(0f, MDConstants.TABLE_SPACING_MULTI)
                        .build()
                    val cellHeight = staticLayout.height + cellPadding
                    if (rowMaxHeight < cellHeight) {
                        rowMaxHeight = cellHeight
                    }
                }
                mdTableRow.rowMaxHeight = rowMaxHeight
                tableHeight += rowMaxHeight
            }
            return tableHeight.toInt()
        } catch (e: Exception) {
            return MDConstants.TABLE_TEXT_SIZE.dp.toInt()
        }
    }

    @JvmStatic
    fun checkCodeWidth(codeContent: String, paint: Paint): Int {
        try {
            val paddingValue = MDConstants.TABLE_CELL_PADDING.dp.toInt()
            var codeWidth = 0.0f

            val textPaint = TextPaint(paint).apply {
                textSize = MDConstants.TABLE_TEXT_SIZE.dp
            }
            codeContent.split("\n").forEach {
                val lineWidth = textPaint.measureText(it) + paddingValue * 2
                if (codeWidth < lineWidth) {
                    codeWidth = lineWidth
                }
            }
            return codeWidth.toInt() + paddingValue
        } catch (e: Exception) {
            return MDConstants.TABLE_CELL_PADDING.dp.toInt()
        }
    }

    @JvmStatic
    fun checkCodeHeight(codeContent: String, maxWidth: Int, paint: Paint): Int {
        try {
            val textPaint = TextPaint(paint).apply {
                textSize = MDConstants.TABLE_TEXT_SIZE.dp
            }
            val staticLayout = StaticLayout.Builder.obtain(
                codeContent,
                0,
                codeContent.length,
                textPaint,
                maxWidth
            )
                .setIncludePad(true)
                .setLineSpacing(0f, MDConstants.CODE_LINE_SPACING_MULTI)
                .build()
            return staticLayout.height + MDConstants.TABLE_CELL_PADDING.dp.toInt() * 2
        } catch (e: Exception) {
            return MDConstants.TABLE_TEXT_SIZE.dp.toInt()
        }
    }

    @JvmStatic
    fun checkValidDivide(lineValue: String?): Boolean {
        if (lineValue.isNullOrEmpty()) {
            return false
        }
        if (!lineValue.startsWith(MDConstants.DIVIDE_LINE_IDENTITY)) {
            return false
        }
        return try {
            lineValue.substring(0, MDConstants.DIVIDE_LINE_IDENTITY.length).toCharArray().forEach {
                if (it != '-' && it != ' ') {
                    return false
                }
            }
            true
        } catch (e: Exception) {
            TLog.error("MDTableUtils", "checkValidDivide: $e")
            false
        }
    }
}