package com.twl.hi.richtext.render

import android.text.TextUtils
import com.techwolf.lib.tlog.TLog
import com.twl.hi.richtext.parser.item.code.MDCodeItem
import com.twl.hi.richtext.parser.item.code.MDCodeProps
import com.twl.hi.richtext.span.MDScrollCodeSpan
import com.twl.hi.richtext.utils.MDConstants
import io.noties.markwon.AbstractMarkwonPlugin
import io.noties.markwon.MarkwonSpansFactory
import io.noties.markwon.MarkwonVisitor
import org.commonmark.node.Node
import org.commonmark.node.Text
import org.commonmark.parser.Parser
import java.util.regex.Pattern

class MDScrollCodePlugin : AbstractMarkwonPlugin() {

    companion object {
        @JvmStatic
        fun create(): MDScrollCodePlugin {
            return MDScrollCodePlugin()
        }

        private val codeContentMap: HashMap<String, String> = HashMap()
        private var codeIndex = 0
    }

    // 匹配代码块的正则表达式
    private val codeBlockPattern =
        Pattern.compile("```(\\w+)?\\n([\\s\\S]*?)```", Pattern.MULTILINE)

    /**
     * 从整个 markdown 中，检索出表格信息，并替换为占位符
     */
    override fun processMarkdown(markdown: String): String {
        val result = StringBuilder()
        val matcher = codeBlockPattern.matcher(markdown)
        var lastEnd = 0

        while (matcher.find()) {
            // 添加代码块前的普通文本
            val beforeText = markdown.substring(lastEnd, matcher.start())
            result.append(beforeText)
            // 获取语言和代码内容
            val language = matcher.group(1) ?: "text"
            val codeContent = matcher.group(2) ?: ""

            lastEnd = matcher.end()
            // 创建代码占位符文本（用于span定位）
            result.append(linkCodeResult(language, codeContent, lastEnd >= markdown.length))
        }
        // 添加剩余的普通文本
        if (lastEnd < markdown.length) {
            result.append(markdown.substring(lastEnd))
        }
        return result.toString()
    }

    /**
     * 将代码内容组合成一行文案
     */
    private fun linkCodeResult(codeLanguage: String, codeContent: String, isLast: Boolean): String {
        val codeKey = StringBuilder().append(MDConstants.CODE_LINE_PRE)
            .append(System.currentTimeMillis())
            .append(codeIndex)
            .toString()

        // 处理特殊字符，确保代码块内容正常显示
        val processedContent = codeContent
            // 首先处理HTML实体，恢复为原始字符
            .replace("&amp;", "&")   // 和号（必须最先处理，避免影响其他实体）
            .replace("&quot;", "\"") // 双引号
            .replace("&#39;", "'")   // 单引号
            .replace("&nbsp;", " ")  // HTML非断行空格转换为普通空格
            .replace("\u00A0", " ")  // Unicode非断行空格转换为普通空格
            // 然后处理被错误转义的字符，恢复代码块中的原始字符
            .replace("\\\\", "\\")   // 反斜杠
            .replace("\\!", "!")     // 感叹号
            .replace("\\#", "#")     // 井号
            .replace("\\{", "{")     // 左大括号
            .replace("\\}", "}")     // 右大括号
            .replace("\\[", "[")     // 左方括号
            .replace("\\]", "]")     // 右方括号
            .replace("\\(", "(")     // 左圆括号
            .replace("\\)", ")")     // 右圆括号
            .replace("\\<", "<")     // 小于号
            .replace("\\>", ">")     // 大于号
            .replace("\\*", "*")     // 星号
            .replace("\\+", "+")     // 加号
            .replace("\\-", "-")     // 减号
            .replace("\\.", ".")     // 点号
            .replace("\\_", "_")     // 下划线
            .replace("\\`", "`")     // 反引号
            .replace("\\=", "=")     // 等号
            .replace("\\~", "~")     // 波浪号

        val result = StringBuilder().append(codeLanguage)
            .append(MDConstants.CODE_LINK_CHAR)
            .append(if (isLast) "1" else "0")
            .append(MDConstants.CODE_LINK_CHAR)
            .append(processedContent)
        codeContentMap[codeKey] = result.toString()
        codeIndex++
        return codeKey
    }

    override fun configureParser(builder: Parser.Builder) {
        // 注册表格节点处理器
        builder.postProcessor { document ->
            processMDTableItemList(document)
            document
        }
    }

    private fun processMDTableItemList(document: Node?) {
        var funNode = document
        while (funNode != null) {
            val next = funNode.next
            // 递归处理所有子节点
            if (funNode.firstChild != null) {
                processMDTableItemList(funNode.firstChild)
            }
            if (funNode is Text) {
                processMDTableItem(funNode)
            }
            funNode = next
        }
    }

    /**
     * 解析表格文案，并添加到节点树中
     */
    private fun processMDTableItem(textItem: Text) {
        try {
            val literal = textItem.literal
            if (literal.startsWith(MDConstants.CODE_LINE_PRE)) {
                val tableItem = dispatchCodeItem(literal)
                if (tableItem != null) {
                    while (textItem.firstChild != null) {
                        val child = textItem.firstChild
                        child.unlink()
                        tableItem.appendChild(child)
                    }
                    textItem.insertAfter(tableItem)
                    textItem.unlink()
                }
            }
        } catch (e: Exception) {
            TLog.error("processMDTableItem", e.message)
        }
    }

    private fun dispatchCodeItem(literal: String): MDCodeItem? {
        try {
            val codeContent = codeContentMap[literal]
            if (codeContent == null || TextUtils.isEmpty(codeContent)) {
                return null
            }
            val splitArray = codeContent.split(MDConstants.CODE_LINK_CHAR)
            if (splitArray.size != 3) {
                return null
            }
            val codeValue = if (splitArray[2].endsWith("\n")) {
                splitArray[2].substring(0, splitArray[2].length - 1)
            } else {
                splitArray[2]
            }
            return MDCodeItem(
                splitArray[0],
                codeValue,
                splitArray[1] == "1"
            )
        } catch (e: Exception) {
            return null
        }
    }

    override fun configureVisitor(builder: MarkwonVisitor.Builder) {
        builder.on(
            Text::class.java
        ) { p0, p1 ->
            p0.builder().append(p1.literal)
        }
        builder.on(MDCodeItem::class.java) { visitor, codeItem ->
            // 记录开始位置
            val length = visitor.length()
            // 添加占位符
            visitor.run {
                builder().append("Code")
                // 存储节点信息到RenderProps
                MDCodeProps.run { CODE_PROPS_ITEM.set(renderProps(), codeItem) }
                // 应用Span
                setSpansForNodeOptional(codeItem, length)
            }
        }
    }

    override fun configureSpansFactory(builder: MarkwonSpansFactory.Builder) {
        super.configureSpansFactory(builder)
        builder.setFactory(MDCodeItem::class.java) { _, p1 ->
            val codeItem: MDCodeItem? = MDCodeProps.CODE_PROPS_ITEM.get(p1)
            return@setFactory when {
                codeItem != null -> {
                    MDScrollCodeSpan(codeItem.codeLanguage, codeItem.codeValue, codeItem.isLast)
                }

                else -> {
                    // 返回一个默认的空 span 或者 null
                    MDScrollCodeSpan("", "", false)
                }
            }
        }
    }
}