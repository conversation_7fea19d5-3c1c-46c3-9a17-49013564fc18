package com.twl.hi.richtext.render

import com.techwolf.lib.tlog.TLog
import com.twl.hi.richtext.parser.item.table.MDTableItem
import com.twl.hi.richtext.parser.item.table.MDTableProps
import com.twl.hi.richtext.parser.item.table.MDTableLineInfo
import com.twl.hi.richtext.span.MDTableSpan
import com.twl.hi.richtext.utils.MDConstants
import com.twl.hi.richtext.utils.MDTableUtils
import io.noties.markwon.AbstractMarkwonPlugin
import io.noties.markwon.MarkwonSpansFactory
import io.noties.markwon.MarkwonVisitor
import org.commonmark.node.Node
import org.commonmark.node.Text
import org.commonmark.parser.Parser

/**
 * Markdown 表格解析插件
 */
class MDTablePlugin : AbstractMarkwonPlugin() {

    companion object {
        @JvmStatic
        fun create(): MDTablePlugin {
            return MDTablePlugin()
        }
    }

    /**
     * 从整个 markdown 中，检索出表格信息，并替换为占位符
     */
    override fun processMarkdown(markdown: String): String {
        val result = StringBuilder()
        val tableRowList = ArrayList<MDTableLineInfo>()

        markdown.split("\n").forEach { line ->
            val tableLineInfo = MDTableUtils.checkTableLine(line)
            when {
                tableLineInfo == null -> {
                    appendResult(result, tableRowList, line)
                }

                tableRowList.size == 0 -> {
                    if (tableLineInfo.isContent == 1) {
                        tableRowList.add(tableLineInfo)
                    } else {
                        appendResult(result, tableRowList, line)
                    }
                }

                tableRowList.size == 1 -> {
                    dispatchSingleList(result, tableLineInfo, tableRowList, line)
                }

                else -> {
                    dispatchMultiList(result, tableLineInfo, tableRowList, line)
                }
            }
        }
        if (tableRowList.size > 0) {
            appendResult(result, tableRowList, null)
        }
        return result.toString().substring(0, result.length - 1)
    }

    private fun appendResult(
        result: StringBuilder,
        tableRowList: ArrayList<MDTableLineInfo>,
        lineValue: String?
    ) {
        if (tableRowList.size > 1) {
            val alignValue = MDTableUtils.checkRowAlignValue(tableRowList[1])
            tableRowList.forEachIndexed { index, lineInfo ->
                if (lineInfo.isContent == 1) {
                    result.append(lineInfo.buildLineValue(index, tableRowList.size, alignValue))
                        .append("\n")
                }
            }
        } else if (tableRowList.size == 1) {
            result.append(tableRowList[0].lineValue).append("\n")
        }
        tableRowList.clear()
        if (lineValue != null) {
            result.append(lineValue).append("\n")
        }
    }

    private fun dispatchSingleList(
        result: StringBuilder,
        tableLineInfo: MDTableLineInfo,
        tableRowList: ArrayList<MDTableLineInfo>,
        line: String?
    ) {
        if (tableLineInfo.isContent == 1) {
            appendResult(result, tableRowList, null)
            tableRowList.add(tableLineInfo)
        } else if (tableLineInfo.cCount != tableRowList[0].cCount) {
            appendResult(result, tableRowList, line)
        } else {
            tableRowList.add(tableLineInfo)
        }
    }

    private fun dispatchMultiList(
        result: StringBuilder,
        tableLineInfo: MDTableLineInfo,
        tableRowList: ArrayList<MDTableLineInfo>,
        line: String?
    ) {
        if (tableLineInfo.cCount != tableRowList[1].cCount) {
            if (tableLineInfo.isContent == 1) {
                appendResult(result, tableRowList, null)
                tableRowList.add(tableLineInfo)
            } else {
                appendResult(result, tableRowList, line)
            }
        } else {
            tableLineInfo.isContent = 1
            tableRowList.add(tableLineInfo)
        }
    }

    override fun configureParser(builder: Parser.Builder) {
        // 注册表格节点处理器
        builder.postProcessor { document ->
            processMDTableItemList(document)
            document
        }
    }

    private fun processMDTableItemList(document: Node?) {
        var funNode = document
        while (funNode != null) {
            val next = funNode.next
            // 递归处理所有子节点
            if (funNode.firstChild != null) {
                processMDTableItemList(funNode.firstChild)
            }
            if (funNode is Text) {
                processMDTableItem(funNode)
            }
            funNode = next
        }
    }

    private fun processMDTableItem(textItem: Text) {
        try {
            val literal = textItem.literal
            if (literal.startsWith(MDConstants.TABLE_LINE_PRE)) {
                val childTableItem = MDTableUtils.buildTableLineItem(literal)
                if (childTableItem != null) {
                    while (textItem.firstChild != null) {
                        val child = textItem.firstChild
                        child.unlink()
                        childTableItem.appendChild(child)
                    }
                    textItem.insertAfter(childTableItem)
                    textItem.unlink()
                }
            }
        } catch (e: Exception) {
            TLog.error("processMDTableItem", e.message)
        }
    }

    override fun configureVisitor(builder: MarkwonVisitor.Builder) {
        builder.on(
            Text::class.java
        ) { p0, p1 ->
            p0.builder().append(p1.literal)
        }
        builder.on(MDTableItem::class.java) { visitor, tableItem ->
            // 记录开始位置
            val length = visitor.length()
            // 添加占位符
            visitor.run {
                builder().append(" ")
                // 存储节点信息到RenderProps
                MDTableProps.run { TABLE_ITEM.set(renderProps(), tableItem) }
                // 应用Span
                setSpansForNodeOptional(tableItem, length)
            }
        }
    }

    override fun configureSpansFactory(builder: MarkwonSpansFactory.Builder) {
        super.configureSpansFactory(builder)
        builder.setFactory(MDTableItem::class.java) { _, p1 ->
            val tableItem: MDTableItem = MDTableProps.TABLE_ITEM.require(p1)
            return@setFactory MDTableSpan(tableItem)
        }
    }
}