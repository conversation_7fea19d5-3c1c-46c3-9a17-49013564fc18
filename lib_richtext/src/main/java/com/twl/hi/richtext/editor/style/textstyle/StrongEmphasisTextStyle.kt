package com.twl.hi.richtext.editor.style.textstyle

import android.widget.EditText
import com.twl.hi.richtext.editor.RichEditView
import com.twl.hi.richtext.editor.style.textstyle.TextStyle
import io.noties.markwon.core.spans.StrongEmphasisSpan

/**
 * <AUTHOR>
 * @date 2023/2/27
 * description:
 */
class StrongEmphasisTextStyle(editView: RichEditView) : TextStyle<StrongEmphasisSpan>(editView = editView) {

    override fun newSpan(): StrongEmphasisSpan {
        return StrongEmphasisSpan()
    }

}