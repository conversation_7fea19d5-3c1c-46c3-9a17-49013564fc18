package com.twl.hi.richtext.editor.toolbar

import android.content.Context
import android.util.AttributeSet
import android.widget.EditText
import android.widget.ImageView
import android.widget.RelativeLayout
import com.twl.hi.richtext.editor.style.SelectableStyle

/**
 * <AUTHOR>
 * @date 2023/3/2
 * description:
 */
class ToolItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    private val mImageView = ImageView(context)
    private var mToolItemData: ToolItemData? = null
    private var mEditText: EditText? = null
    private var hasClick = false

    init {
        removeAllViews()
        addView(mImageView.apply {
            layoutParams =
                LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                    addRule(CENTER_IN_PARENT, TRUE)
                }
        })
    }

    fun getData() = mToolItemData

    fun setData(data: ToolItemData) {
        mToolItemData = data
        mImageView.setImageResource(data.normalImageResId)
        data.style?.run {
            onStyleSelectChangedListener =
                object : SelectableStyle.OnStyleSelectChangedListener {
                    override fun onStyleSelectChanged(selected: Boolean) {
                        isSelected = selected
                    }
                }
        }
    }

    override fun setOnClickListener(l: OnClickListener?) {
        super.setOnClickListener(l)
        hasClick = (l != null)
    }

    override fun setPressed(pressed: Boolean) {
        super.setPressed(pressed)
        if (isSelected) {
            return
        }
        mToolItemData?.run {
            mImageView.setImageResource(
                if (pressed) {
                    selectedImageResId
                } else {
                    normalImageResId
                }
            )
        }
    }


    override fun setSelected(selected: Boolean) {
        super.setSelected(selected)
        mToolItemData?.run {
            mImageView.setImageResource(
                if (selected) {
                    selectedImageResId
                } else {
                    normalImageResId
                }
            )
        }
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        mToolItemData?.run {
            mImageView.setImageResource(
                if (isEnabled) {
                    if (isPressed || isSelected) {
                        selectedImageResId
                    } else {
                        normalImageResId
                    }
                } else {
                    disabledImageResId ?: normalImageResId
                }
            )
        }
    }

    fun attachToEditText(editText: EditText) {
        mEditText = editText
    }

    data class ToolItemData(
        val id: String = "",
        val type: Int,
        val style: SelectableStyle? = null,
        val selectedImageResId: Int,
        val normalImageResId: Int,
        val disabledImageResId: Int? = null
    ) {
        companion object {
            /**
             * 切换到markdown文字属性菜单
             */
            const val TYPE_OPEN_TEXT = 1

            /**
             * emoji表情选择
             */
            const val TYPE_SELECT_EMOJI = 2

            /**
             * @联系人选择
             */
            const val TYPE_SELECT_AT_CONTACT = 3

            /**
             * 上传图片选择
             */
            const val TYPE_SELECT_IMAGE = 4

            /**
             * 加粗
             */
            const val TYPE_SELECT_STRONG_EMPHASIS = 5

            /**
             * 斜体
             */
            const val TYPE_SELECT_EMPHASIS = 6

            /**
             * 删除线
             */
            const val TYPE_SELECT_THROUGH_LINE = 7

            /**
             * 下划线
             */
            const val TYPE_SELECT_UNDER_LINE = 8

            /**
             * 有序列表
             */
            const val TYPE_SELECT_ORDERED_LIST = 9

            /**
             * 无序列表
             */
            const val TYPE_SELECT_NO_ORDERED_LIST = 10

            /**
             * 引用
             */
            const val TYPE_SELECT_BLOCK_QUOTE = 11
        }
    }


}