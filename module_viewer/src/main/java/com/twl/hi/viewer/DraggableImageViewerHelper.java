package com.twl.hi.viewer;

import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.message.MessageConstants;

import java.util.ArrayList;
import java.util.List;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.LList;
import lib.twl.common.views.imagesview.DraggableParamsInfo;
import lib.twl.common.views.imagesview.Image;
import lib.twl.common.views.imagesview.MultiViewerBean;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>gpeng on 2020/4/18
 * Describe:
 */
public class DraggableImageViewerHelper {

    public static void showImages(Context context, View view, List<Image> images, int index) {
        DraggableParamsInfo draggableParamsInfo = null;
        if (view != null) {
            draggableParamsInfo = createImageDraggableParamsWithWHRadio(view);
        }
        showImagesDraggableParamsInfo(context, draggableParamsInfo, images, index);
    }

    public static void showImages(Context context, View view, List<MultiViewerBean> images, int index, String chatId, int chatType, boolean cantGoPicVideo, int order) {
        DraggableParamsInfo draggableParamsInfo = null;
        if (view != null) {
            draggableParamsInfo = createImageDraggableParamsWithWHRadio(view);
        }
        showImagesDraggableParamsInfo(context, draggableParamsInfo, images, index, chatId, chatType, cantGoPicVideo, order);
    }

    public static void showImagesDraggableParamsInfo(Context context, DraggableParamsInfo draggableParamsInfo, List<Image> images, int index) {
        List<MultiViewerBean> multiViewerBeans = new ArrayList<>();
        for (int i = 0; images != null && i < images.size(); i++) {
            Image image = images.get(i);
            MultiViewerBean multiViewerBean = new MultiViewerBean(MultiViewerBean.TYPE_IMAGE);
            multiViewerBean.setImage(image);
            multiViewerBeans.add(multiViewerBean);
        }
        showImagesDraggableParamsInfo(context, draggableParamsInfo, multiViewerBeans, index, "", MessageConstants.MSG_SINGLE_CHAT, false, Constants.ORDER_ASC);
    }

    public static void showImagesDraggableParamsInfo(Context context, DraggableParamsInfo draggableParamsInfo, List<MultiViewerBean> images, int index, String chatId, int chatType, boolean cantGoPicVideo) {
        showImagesDraggableParamsInfo(context, draggableParamsInfo, images, index, chatId, chatType, cantGoPicVideo, Constants.ORDER_ASC);
    }

    public static void showImagesDraggableParamsInfo(Context context, DraggableParamsInfo draggableParamsInfo, List<MultiViewerBean> images, int index, String chatId, int chatType, boolean cantGoPicVideo, boolean autoLoadMore) {
        showImagesDraggableParamsInfo(context, draggableParamsInfo, images, index, chatId, chatType, cantGoPicVideo, Constants.ORDER_ASC, autoLoadMore);
    }

    public static void jumpToMediaViewerPage(Context context, DraggableParamsInfo draggableParamsInfo, List<MultiViewerBean> images, int index, String chatId, int chatType, boolean cantGoPicVideo, boolean autoLoadMore) {
        jumpToMediaViewerPage(context, draggableParamsInfo, images, index, chatId, chatType, Constants.ORDER_ASC, cantGoPicVideo, true, autoLoadMore);
    }

    public static void showImagesDraggableParamsInfo(Context context, DraggableParamsInfo draggableParamsInfo, List<MultiViewerBean> multiViewerBeans, int index, String chatId, int chatType, boolean cantGoPicVideo, int order) {
        jumpToMediaViewerPage(context, draggableParamsInfo, multiViewerBeans, index, chatId, chatType, order, cantGoPicVideo, false, true);
    }

    public static void showImagesDraggableParamsInfo(Context context, DraggableParamsInfo draggableParamsInfo, List<MultiViewerBean> multiViewerBeans, int index, String chatId, int chatType, boolean cantGoPicVideo, int order, boolean autoLoadMore) {
        jumpToMediaViewerPage(context, draggableParamsInfo, multiViewerBeans, index, chatId, chatType, order, cantGoPicVideo, false, autoLoadMore);
    }

    /**
     * 跳转到图片预览列表界面
     *
     * @param context             context
     * @param draggableParamsInfo
     * @param multiViewerBeans    数据源
     * @param index               定位的索引
     * @param cantGoPicVideo      点击右上角操作图标不可以跳转图片，视频列表界面
     * @param autoLoadMore        图片预览时是否支持自动加载前后更多消息的图片来预览,默认为true,目前仅消息卡片不支持
     */
    public static void jumpToMediaViewerPage(Context context,
                                             DraggableParamsInfo draggableParamsInfo,
                                             List<MultiViewerBean> multiViewerBeans,
                                             int index,
                                             String chatId,
                                             int chatType,
                                             int order,
                                             boolean cantGoPicVideo,
                                             boolean interruptWithdraw,
                                             boolean autoLoadMore
    ) {
        if (LList.isEmpty(multiViewerBeans)) {
            return;
        }
        if (index < 0 || index >= multiViewerBeans.size()) {
            index = 0;
        }
        MultiViewerBean multiViewerBean = multiViewerBeans.get(index);
        if (draggableParamsInfo != null && multiViewerBean != null && multiViewerBean.getType() == MultiViewerBean.TYPE_IMAGE) {
            Image image = multiViewerBeans.get(index).getImage();
            if (!TextUtils.isEmpty(image.getTinyUrl()) && !image.getTinyUrl().startsWith("file://")) {
                image.setDraggableInfo(draggableParamsInfo);
            }
        }
        ServiceManager.getInstance().getMessageService().setPreviewImages(multiViewerBeans);
        Bundle bundle = new Bundle();
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT, index);
        bundle.putString(BundleConstants.BUNDLE_DATA_STRING, chatId);
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT_1, chatType);
        bundle.putBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN, cantGoPicVideo);
        bundle.putBoolean(BundleConstants.BUNDLE_AUTO_LOAD_MORE, autoLoadMore);
        bundle.putInt(BundleConstants.BUNDLE_ORDER, order);
        bundle.putBoolean(BundleConstants.BUNDLE_INTERRUPT_WITHDRAW, interruptWithdraw);
        Intent intent = new Intent(context, MediaViewerActivity.class);
        intent.putExtras(bundle);
        AppUtil.startActivity(context, intent, ActivityAnimType.SCALE);
    }

    /**
     * 根据宽高比，显示一张图片
     */
    public static DraggableParamsInfo createImageDraggableParamsWithWHRadio(View view) {
        DraggableParamsInfo draggableInfo = new DraggableParamsInfo();
        if (view != null) {
            int[] location = new int[2];
            view.getLocationInWindow(location);
            Rect windowRect = new Rect();
            view.getWindowVisibleDisplayFrame(windowRect);
            int top = location[1];
            draggableInfo = new DraggableParamsInfo(
                    location[0],
                    top,
                    view.getWidth(),
                    view.getHeight()
            );
        }
        return draggableInfo;
    }

}
