package com.twl.hi.viewer;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.basic.dialog.DialogUtils;
import com.twl.hi.basic.helpers.AppPageRouterHelper;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.viewer.bean.MediaViewerListener;
import com.twl.hi.viewer.databinding.ViewerActivityMediaViewerBinding;
import com.twl.hi.viewer.viewmodel.MediaViewerViewModel;
import com.twl.utils.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.LList;
import lib.twl.common.util.ToastUtils;
import lib.twl.common.views.imagesview.MultiViewerBean;


/**
 * 图片&视频浏览页面
 * <p>
 * Created by ChaiJiangpeng on 2020/4/18
 * Describe:
 */
public class MediaViewerActivity extends FoundationVMActivity<ViewerActivityMediaViewerBinding, MediaViewerViewModel> implements View.OnClickListener {

    private static final String TAG = "ImagesViewerActivity";

    public static final String BUNDLE_PREVIEW_IMAGES = "bundle-preview-images";

    private boolean cantGoPicVideo; //点击右上角操作图标不可以跳转图片，视频列表界面,默认false表示可以跳转
    private int selectedChatType = 0;
    private String selectedChatId;
    private long mSelectedMediaMsgId = -1;
    private boolean mAutoLoadMore = true;

    DialogUtils mDialogUtils = null;
    private boolean mIsDialogShowing = false;
    private List<MultiViewerBean> mDraggableImages;

    private final Map<String, Boolean> mImageOCRDataMap = new HashMap<>();

    private View mMediaOptionsContainer;

    private final ExecutorFactory.RunnableWrapper mRunnableWrapper = new ExecutorFactory.RunnableWrapper(new Runnable() {
        @Override
        public void run() {
            updateOptionsVisible(false);
        }
    });

    @Override
    public int getContentLayoutId() {
        return R.layout.viewer_activity_media_viewer;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return 0;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getIntent().getExtras();
        if (bundle == null) {
            return;
        }
        mDraggableImages = ServiceManager.getInstance().getMessageService().getPreviewImages();
        if (mDraggableImages == null) {
            mDraggableImages = (List<MultiViewerBean>) bundle.getSerializable(BUNDLE_PREVIEW_IMAGES);
        }
        if (LList.isEmpty(mDraggableImages)) {
            return;
        }
        mMediaOptionsContainer = findViewById(R.id.media_options_container);
        findViewById(R.id.ib_media_video_close).setOnClickListener(this);
        findViewById(R.id.ib_media_image_ocr).setOnClickListener(this);
        findViewById(R.id.ib_media_all_media).setOnClickListener(this);
        findViewById(R.id.ib_media_opt_more).setOnClickListener(this);
        cantGoPicVideo = bundle.getBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN, false);
        mAutoLoadMore = bundle.getBoolean(BundleConstants.BUNDLE_AUTO_LOAD_MORE, true);
        String chatId = bundle.getString(BundleConstants.BUNDLE_DATA_STRING);
        int chatType = bundle.getInt(BundleConstants.BUNDLE_DATA_INT_1, MessageConstants.MSG_SINGLE_CHAT);
        int order = bundle.getInt(BundleConstants.BUNDLE_ORDER, Constants.ORDER_ASC);
        int index = bundle.getInt(BundleConstants.BUNDLE_DATA_INT, 0);
        getViewModel().init(chatId, chatType, order);
        setupGroupStatusObserver(chatType);
        getDataBinding().galleryViewer.setMediaViewerListener(new MediaViewerListener() {
            @Override
            public void onViewerClose() {
                AppUtil.finishActivity(MediaViewerActivity.this, ActivityAnimType.NONE);
            }

            @Override
            public void onImageOCRFinish(String imageFilePath, boolean hasText) {
                mImageOCRDataMap.put(imageFilePath, hasText);
                if (StringUtils.isEquals(imageFilePath, getDataBinding().galleryViewer.getCurrentDisplayImagePath())) {
                    findViewById(R.id.ib_media_image_ocr).setVisibility(hasText ? View.VISIBLE : View.GONE);
                    if (hasText) {
                        pointOCREntranceShow();
                    }
                }
            }

            @Override
            public void onImageDragRestore() {
                updateOptionsVisible(true);
            }

            @Override
            public void showViewerOptions() {
                updateOptionsVisible(true);
            }

            @Override
            public void updateUserTouchable(boolean touchable) {
                getDataBinding().galleryViewer.setUserInputEnabled(touchable);
            }
        });
        getDataBinding().galleryViewer.setCanShowPicVideo(!TextUtils.isEmpty(chatId) && !cantGoPicVideo);
        getDataBinding().galleryViewer.showImagesWithAnimator(mDraggableImages, index, chatId, this, (position, totalCount, multiViewerBean) -> {
            selectedChatType = multiViewerBean.getChatType();
            selectedChatId = multiViewerBean.getChatId();

            updateSelectedImageMsgId(mDraggableImages, index);

            updateMediaViewerOptionsView(multiViewerBean);

            if (!mAutoLoadMore) {
                return;
            }
            if (position == 0) {
                getViewModel().loadPreviousPage(multiViewerBean.getMid(), getDataBinding().galleryViewer::addPrePages);
            } else if (position == totalCount - 1) {
                getViewModel().loadNextPage(multiViewerBean.getMid(), getDataBinding().galleryViewer::addPages);
            }
        });
        updateSelectedImageMsgId(mDraggableImages, index);
        //图片撤回时响应处理
        boolean needInterruptWithdraw = bundle.getBoolean(BundleConstants.BUNDLE_INTERRUPT_WITHDRAW, false);
        TLog.info("MediaWithdraw", "ImagesViewerActivity,interruptWithdraw is: [" + needInterruptWithdraw + "]");
        if (!TextUtils.isEmpty(chatId) && needInterruptWithdraw) {
            ServiceManager.getInstance().getMessageService().queryWithdrawnMessageIds(chatId, chatType).observe(this, msgIds -> {
                if (isCurrentMsgWithdrawn(msgIds)) {
                    showWithdrawDialog();
                    getDataBinding().galleryViewer.blur();
                }
            });
        }
    }

    /**
     * 底部选项ui相关操作
     */
    private void updateMediaViewerOptionsView(MultiViewerBean multiViewerBean) {

        ExecutorFactory.execMainTaskDelay(() -> {
            if (multiViewerBean.getType() == MultiViewerBean.TYPE_VIDEO) {
                findViewById(R.id.ib_media_video_close).setVisibility(View.VISIBLE);
                mMediaOptionsContainer.setBackgroundResource(R.drawable.bg_shape_gradient_gray);
            } else {
                findViewById(R.id.ib_media_video_close).setVisibility(View.GONE);
                mMediaOptionsContainer.setBackgroundResource(R.color.color_transparent);
            }
        }, 100);

        mMediaOptionsContainer.setVisibility(View.VISIBLE);
        ExecutorFactory.removeMainHandlerCallbacks(mRunnableWrapper);
        ExecutorFactory.execMainTaskDelay(mRunnableWrapper, 5000);

        boolean isTextFound = Boolean.TRUE.equals(mImageOCRDataMap.get(getDataBinding().galleryViewer.getCurrentDisplayImagePath()));
        findViewById(R.id.ib_media_image_ocr).setVisibility(isTextFound ? View.VISIBLE : View.GONE);
        if (isTextFound) {
            pointOCREntranceShow();
        }
    }

    private void pointOCREntranceShow() {
        new PointUtils.BuilderV4()
                .name("chat-pic-ocr-show")
                .point();
    }

    private void updateOptionsVisible(boolean isVisible) {
        if (mMediaOptionsContainer != null) {
            if (isVisible) {
                ExecutorFactory.removeMainHandlerCallbacks(mRunnableWrapper);
            }
            mMediaOptionsContainer.setVisibility(isVisible ? View.VISIBLE : View.GONE);
        }
    }

    private void setupGroupStatusObserver(int chatType) {
        if (chatType == MessageConstants.MSG_GROUP_CHAT) {
            getViewModel().getGroupLiveData().observe(this, group -> {
                if (group != null && group.getStatus() != MessageConstants.GROUP_CHAT_STATUS_NORMAL) {
                    AppPageRouterHelper.backToMainTabActivity(MediaViewerActivity.this);
                    ToastUtils.failure(R.string.tips_not_in_group);
                }
            });
        }
    }

    private boolean isCurrentMsgWithdrawn(List<Long> msgIds) {
        TLog.info("MediaWithdraw", "ImagesViewerActivity,isCurrentMsgWithdrawn msgItem id:" + msgIds + ",selected id is " + mSelectedMediaMsgId);
        return msgIds.contains(mSelectedMediaMsgId);
    }

    private void updateSelectedImageMsgId(List<MultiViewerBean> imageList, int index) {
        MultiViewerBean multiViewerBean = LList.getElement(imageList, index);
        if (multiViewerBean != null) {
            mSelectedMediaMsgId = multiViewerBean.getMid();
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        Map<String, Object> paramsMap = new HashMap<>();
        if (id == R.id.ib_media_all_media) {
            if (cantGoPicVideo) {
                getDataBinding().galleryViewer.closeWithAnimator();
            } else {
                Bundle bundle = new Bundle();
                bundle.putString(BundleConstants.BUNDLE_DATA_STRING, TextUtils.isEmpty(selectedChatId) ? getViewModel().getChatId() : selectedChatId);
                bundle.putInt(BundleConstants.BUNDLE_DATA_INT, selectedChatType == 0 ? getViewModel().getChatType() : selectedChatType);
                AppUtil.startUri(this, ChatPageRouter.CHAT_RECORD_SEARCH_FOR_VIDEO_OR_PIC_ACTIVITY, bundle);
            }
            paramsMap.put("type", 2);
        } else if (id == R.id.ib_media_opt_more) {
            getDataBinding().galleryViewer.onMoreClick();
            paramsMap.put("type", 1);
        } else if (id == R.id.ib_media_image_ocr) {
            getDataBinding().galleryViewer.onImageOCRClick();
            paramsMap.put("type", 3);
        } else if (id == R.id.ib_media_video_close) {
            AppUtil.finishActivity(MediaViewerActivity.this, ActivityAnimType.NONE);
        }
        PointUtils.pointV4("chat-pic-ocr-click", paramsMap);
    }

    public void showWithdrawDialog() {
        if (mIsDialogShowing) {
            return;
        }
        mIsDialogShowing = true;
        mDialogUtils = new DialogUtils.Builder(this)
                .setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .setContent(R.string.viewer_media_msg_has_withdrawn)
                .setPositive(R.string.sure)
                .setPositiveListener(v -> {
                    mDialogUtils.dismiss();
                    mIsDialogShowing = false;
                    onBackPressed();
                })
                .build();
        if (!mDialogUtils.isShowing()) {
            mDialogUtils.show();
        }
    }

    @Override
    public void onBackPressed() {
        if (!getDataBinding().galleryViewer.closeWithAnimator()) {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ServiceManager.getInstance().getMessageService().setPreviewImages(null);
    }
}
