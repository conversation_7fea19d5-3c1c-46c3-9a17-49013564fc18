package com.twl.hi.organization.user.callback

/**
 *@author: musa on 2022/6/10
 *@e-mail: <EMAIL>
 *@desc: 名片页点击回调
 */
interface UserInfoCallbackNew {

    /**回退*/
    fun onBack()

    /**跳转编辑*/
    fun onEdit()

    /**分享*/
    fun onShare()

    /**点击头像*/
    fun onClickAvatar()

    /**查看汇报人*/
    fun onViewReporter(reporterId: String)

    /**跳转日程*/
    fun onToSchedule()

    /**点击电话*/
    fun onClickPhoneNumber(phoneNumber: String)

    /**音视频电话*/
    fun onClickMediaCall()

    /**发消息*/
    fun onSendMessage()

    /**点击链接*/
    fun onClickLink(url : String)

    fun viewChatHistory()
}