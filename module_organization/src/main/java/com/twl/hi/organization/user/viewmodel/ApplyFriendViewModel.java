package com.twl.hi.organization.user.viewmodel;

import android.app.Application;
import android.content.res.Resources;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.twl.hi.basic.model.SelectBottomBean;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.organization.R;
import com.twl.hi.organization.api.request.ApplyFriendRequest;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.List;

import hi.kernel.Constants;
import lib.twl.common.util.ProcessHelper;

public class ApplyFriendViewModel extends FoundationViewModel {
    private static final String TAG = "AddContactViewModel";
    public static final int FIRST_APPLY = 0;
    private MutableLiveData<Integer> hasApply = new MutableLiveData<>();
    private int mShiningType = 0;
    private List<SelectBottomBean> mShiningList = new ArrayList<>();
    private ObservableField<String> mShiningContent = new ObservableField<>();
    private Resources resources;
    private ObservableField<String> mReason = new ObservableField<>();

    public ApplyFriendViewModel(Application application) {
        super(application);
        resources = application.getResources();
        mShiningList.add(new SelectBottomBean(0, resources.getString(R.string.no_shining), R.mipmap.organization_icon_no_shining));
        mShiningList.add(new SelectBottomBean(1, resources.getString(R.string.shining_type_app), R.mipmap.organization_icon_shining_type_app));
        mShiningList.add(new SelectBottomBean(2, resources.getString(R.string.shining_type_sms), R.mipmap.organization_icon_shining_type_sms));
        mShiningList.add(new SelectBottomBean(3, resources.getString(R.string.shining_type_phone), R.mipmap.organization_icon_shining_type_phone));
        mShiningContent.set(resources.getString(R.string.no_shining));
    }

    public void applyFriend(String userId) {
        setShowProgressBar("");
        ApplyFriendRequest request = new ApplyFriendRequest(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                int applyStatus = ProcessHelper.getUserCompanyPreferences().getInt(Constants.KEY_APPLY_FRIEND_ID, FIRST_APPLY);
                hasApply.postValue(applyStatus);
                ProcessHelper.getUserCompanyPreferences().edit().putInt(Constants.KEY_APPLY_FRIEND_ID, FIRST_APPLY + 1).commit();
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {

            }

            @Override
            public void onComplete() {
                hideShowProgressBar();
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.friendId = userId;
        request.reason = mReason.get();
        if (mShiningType > 0) {
            request.shiningType = mShiningType;
        }
        HttpExecutor.execute(request);
    }

    public MutableLiveData<Integer> getHasApply() {
        return hasApply;
    }

    public Integer getShiningType() {
        return mShiningType;
    }

    public void setShiningType(Integer mShiningType) {
        this.mShiningType = mShiningType;
    }

    public List<SelectBottomBean> getShiningList() {
        return mShiningList;
    }

    public ObservableField<String> getShiningContent() {
        return mShiningContent;
    }

    public void setShiningContent(String shiningContent) {
        this.mShiningContent.set(shiningContent);
    }

    public ObservableField<String> getReason() {
        return mReason;
    }

    public void setReason(String reason) {
        this.mReason.set(reason);
    }
}