<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.twl.hi.foundation.model.message.MessageConstants" />
        <variable
            name="viewModel"
            type="com.twl.hi.organization.user.viewmodel.UserInfoViewModelNew" />

        <variable
            name="callback"
            type="com.twl.hi.organization.user.callback.UserInfoCallbackNew" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="40dp">

        <FrameLayout
            android:layout_height="match_parent"
            android:layout_width="wrap_content"
            android:onClick="@{() -> callback.onBack()}"
            android:paddingHorizontal="5dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_icon_white_back"
                android:layout_gravity="center"
                android:layout_marginStart="15dp" />
        </FrameLayout>

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@mipmap/ic_edit_white"
            android:onClick="@{() -> callback.onEdit()}"
            app:visibleGone="@{viewModel.userCardInfo.isSelf}"
            app:layout_constraintEnd_toStartOf="@id/iv_share"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="24dp"/>

        <ImageView
            android:id="@+id/iv_share"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@mipmap/organization_ic_share_white"
            android:onClick="@{() -> callback.onShare()}"
            app:visibleGone="@{viewModel.userCardInfo.contact.userType == MessageConstants.MSG_CONTACT_TYPE_USER ? true : false}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="20dp"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>