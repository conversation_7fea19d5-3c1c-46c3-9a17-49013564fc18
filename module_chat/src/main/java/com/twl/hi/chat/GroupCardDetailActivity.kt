package com.twl.hi.chat

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import com.twl.hi.basic.activity.FoundationVMActivity
import com.twl.hi.chat.api.response.GroupCardDetailResponse
import com.twl.hi.chat.callback.GroupDetailCallback
import com.twl.hi.chat.databinding.ChatActivityGroupDetailBinding
import com.twl.hi.chat.viewmodel.GroupDetailActivityViewModel
import com.twl.hi.export.organization.router.OrganizationPageRouter
import com.twl.hi.foundation.logic.ServiceManager
import hi.kernel.BundleConstants
import hi.kernel.Constants
import hi.kernel.HiKernel
import lib.twl.common.ext.getStatusBarsHeight
import lib.twl.common.util.ActivityAnimType
import lib.twl.common.util.AppUtil
import lib.twl.common.util.ExecutorFactory

/**
 * Author: DingDong
 * Date: 2022/3/14
 * Description: 群名片详情页
 */
class GroupCardDetailActivity :
    FoundationVMActivity<ChatActivityGroupDetailBinding, GroupDetailActivityViewModel>(),
    GroupDetailCallback {
    override fun getContentLayoutId() = R.layout.chat_activity_group_detail
    override fun getCallbackVariable() = BR.callback
    override fun getCallback() = this
    override fun getBindingVariable() = BR.viewModel

    override fun clickEnterGroup() {
        ExecutorFactory.execLocalTask {
            val groupId = viewModel.groupDetail.value?.groupId
            val groupMember = ServiceManager.getInstance().databaseService.groupDao.findGroupMember(
                groupId,
                HiKernel.getHikernel().account.userId
            )
            if (groupMember == null) {
                if (isPublic) {
                    viewModel.joinPublicGroup(groupId.orEmpty()) {
                        GroupChatActivity.start(this@GroupCardDetailActivity, it)
                    }
                } else {
                    viewModel.enterGroup(
                        groupId.orEmpty(),
                        intent.getLongExtra(Constants.MSG_ID, 0L),
                        intent.getStringExtra(Constants.SEND) ?: ""
                    ) {
                        GroupChatActivity.start(this@GroupCardDetailActivity, it)
                    }
                }
            } else {
                GroupChatActivity.startWithAnim(
                    this@GroupCardDetailActivity,
                    groupId,
                    ActivityAnimType.ALPHA
                )
            }
        }

    }

    override fun clickLeft(view: View?) {
        finish()
    }

    override fun clickRight(view: View?) {
    }

    companion object {
        /**
         * shareUserId 发送方sender
         * msgId 消息ID mid
         * 其他字段对应Group表中字段
         */
        @JvmStatic
        @JvmOverloads
        fun intentStart(
            context: Context,
            groupId: String,
            msgId: Long,
            shareUserId: String?,
            groupName: String?,
            groupAvatar: String?,
            isPublic: Boolean? = false
        ) {
            AppUtil.startActivity(context,
                Intent(context, GroupCardDetailActivity::class.java).apply {
                    putExtra(Constants.GROUP, groupName)
                    putExtra(BundleConstants.BUNDLE_AVATAR, groupAvatar)
                    putExtra(Constants.CHAT_ID, groupId)
                    putExtra(Constants.MSG_ID, msgId)
                    putExtra(Constants.SEND, shareUserId)
                    putExtra(Constants.IS_PUBLIC_GROUP, isPublic)
                })
        }

        @JvmStatic
        @JvmOverloads
        fun intentStart(
            context: Context,
            groupId: String,
            isPublic: Boolean? = false,
            fromSearch: Boolean = false
        ) {
            AppUtil.startActivity(context,
                Intent(context, GroupCardDetailActivity::class.java).apply {
                    putExtra(Constants.CHAT_ID, groupId)
                    putExtra(Constants.IS_PUBLIC_GROUP, isPublic)
                    putExtra(BundleConstants.BUNDLE_FROM_SEARCH, fromSearch)
                })
        }
    }

    private var isPublic = false
    private var isFromSearch = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val chatId = intent.getStringExtra(Constants.CHAT_ID)
        val groupName = intent.getStringExtra(Constants.GROUP)
        val groupAvatar = intent.getStringExtra(BundleConstants.BUNDLE_AVATAR)
        isPublic = intent.getBooleanExtra(Constants.IS_PUBLIC_GROUP, false)
        isFromSearch = intent.getBooleanExtra(BundleConstants.BUNDLE_FROM_SEARCH, false)
        viewModel.initGroupData(groupName, groupAvatar)
        dataBinding.clGroupDetailTop.setPadding(0, getStatusBarsHeight(), 0, 0)
        dataBinding.titleBar.root.layoutParams =
            (dataBinding.titleBar.root.layoutParams as ViewGroup.MarginLayoutParams).apply {
                setMargins(
                    0,
                    getStatusBarsHeight(),
                    0,
                    0
                )
            }
        viewModel.getGroupDetail(chatId ?: "")
        dataBinding.tvGroupOwnerName.movementMethod = LinkMovementMethod.getInstance()
        dataBinding.enterGroupBtn.text =
            if (isFromSearch) getString(R.string.join_group) else getString(R.string.enter_group)
        viewModel.groupDetail.observe(this,
            Observer<GroupCardDetailResponse> {
                dataBinding.setVariable(BR.groupCardDetail, it)
                dataBinding.tvGroupOwnerName.text = viewModel.createOwnerName(
                    it.groupOwnerId,
                    it.groupOwnerName,
                    it.groupOwnerNickName
                ) { userId ->
                    val bundle = Bundle()
                    bundle.putString(BundleConstants.BUNDLE_DATA_LONG, userId)
                    AppUtil.startUri(
                        this@GroupCardDetailActivity,
                        OrganizationPageRouter.USER_INFO_ACTIVITY,
                        bundle
                    )
                }
            })
    }

    override fun shouldFullScreen() = true
}