package com.twl.hi.chat.bindadapter;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.ShapeDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.ViewKt;
import androidx.databinding.BindingAdapter;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.airbnb.lottie.LottieAnimationView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.generic.RoundingParams;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayout;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.api.request.CalendarEchoRequest;
import com.twl.hi.basic.bindadapter.BasicBindingAdapters;
import com.twl.hi.basic.callback.ChatItemListener;
import com.twl.hi.basic.callback.HighLightCallback;
import com.twl.hi.basic.callback.MessageWithInlineLinkCallback;
import com.twl.hi.basic.databinding.PopChatLongClickBinding;
import com.twl.hi.basic.model.SearchContact;
import com.twl.hi.basic.model.SearchGroup;
import com.twl.hi.basic.util.ThemeUtils;
import com.twl.hi.basic.views.CommonTextView;
import com.twl.hi.basic.views.expand.ExpandableTextView;
import com.twl.hi.basic.views.multitext.CommonIconTextView;
import com.twl.hi.basic.views.multitext.MLinkEmotionTextView;
import com.twl.hi.chat.*;
import com.twl.hi.chat.api.response.bean.MailSearchBean;
import com.twl.hi.chat.api.response.bean.SearchCardBean;
import com.twl.hi.chat.callback.ChatMessageItemCallback;
import com.twl.hi.chat.callback.ChatMsgTextSelectionUpdateCallback;
import com.twl.hi.chat.callback.MsgContentClickListener;
import com.twl.hi.chat.chatgroup.singleton.ChatGroupDataManager;
import com.twl.hi.chat.common.GroupCacheInfo;
import com.twl.hi.chat.message.adapter.EmojiReplyAdapter;
import com.twl.hi.chat.message.callback.FavoriteListCallback;
import com.twl.hi.chat.messagecard.config.CardViewConfig;
import com.twl.hi.chat.messagecard.module.ModuleViewFactory;
import com.twl.hi.chat.messagecard.utils.ExtensionCardHelper;
import com.twl.hi.chat.util.AudioPlayWrapper;
import com.twl.hi.chat.util.ChatImageHelper;
import com.twl.hi.chat.util.ChatTextSelectionHelper;
import com.twl.hi.chat.util.ChatUtils;
import com.twl.hi.chat.widget.TextMessageLayout;
import com.twl.hi.chat.widget.emojireply.EmojiReplyFlexLayoutManager;
import com.twl.hi.emotion.utils.EmojiHelper;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.export.organization.router.OrganizationPageRouter;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.request.TaskOperateRequest;
import com.twl.hi.foundation.api.request.schedule.ScheduleJoinRequest;
import com.twl.hi.foundation.api.response.TaskOperateResponse;
import com.twl.hi.foundation.api.response.bean.ChatRecordAllSearchServerBean;
import com.twl.hi.foundation.api.response.bean.ChatRecordSummaryBean;
import com.twl.hi.foundation.api.response.bean.FavoriteInterface;
import com.twl.hi.foundation.logic.GroupService;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Agreement;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.Group;
import com.twl.hi.foundation.model.Hypertext;
import com.twl.hi.foundation.model.emotion.EmotionItem;
import com.twl.hi.foundation.model.message.*;
import com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard;
import com.twl.hi.foundation.model.message.extensioncard.data.CardHeader;
import com.twl.hi.foundation.model.message.extensioncard.data.modules.Module;
import com.twl.hi.foundation.model.message.extensioncard.data.modules.ModuleForNote;
import com.twl.hi.foundation.model.security.BusinessModuleEnum;
import com.twl.hi.foundation.utils.ContactUtils;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.image.GlideApp;
import com.twl.hi.module_email.bindingadapter.EmailBindingAdapter;
import com.twl.hi.richtext.render.RichTextView;
import com.twl.hi.richtext.span.CustomCodeBlockSpan;
import com.twl.hi.richtext.span.HyperlinkSpan;
import com.twl.hi.router.base.RouterBaseConstant;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;
import com.twl.http.gson.adapter.NumberUtils;
import com.twl.utils.StringUtils;
import com.twl.utils.URLUtils;
import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import hi.kernel.HiKernel;
import io.noties.markwon.core.spans.CodeBlockSpan;
import io.noties.markwon.core.spans.CodeSpan;
import kotlin.collections.SetsKt;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.util.*;
import lib.twl.common.util.image.BHImageHelper;
import lib.twl.common.util.image.ImageUtils;
import lib.twl.common.util.selection.SelectableTextView;
import lib.twl.common.util.selection.TextSelection;
import lib.twl.common.util.selection.listener.OnTextSelectionLifecycleCallback;
import lib.twl.common.views.adapter.entity.MultiItemEntity;

import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.google.android.flexbox.FlexboxLayout.SHOW_DIVIDER_MIDDLE;
import static com.twl.hi.richtext.render.MarkdownFactoryKt.toMarkDown;

public class ChatBindingAdapter {

    private static final String TAG = "ChatBindingAdapter";

    @BindingAdapter("visible")
    public static void setVisible(View view, boolean visible) {
        view.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    @BindingAdapter("lastTime")
    public static void setLastTime(TextView textView, long time) {
        if (time == 0) {
            textView.setText("");
            return;
        }
        if (LDate.isToday(time)) {
            textView.setText(LDate.getDate(time, "HH:mm"));
            return;
        }
        if (LDate.isYesterday(time)) {
            textView.setText("昨天");
            return;
        }
        if (LDate.isPreViewWeek(time)) {
            textView.setText(LDate.dayOfWeek(time));
            return;
        } else if (LDate.isSameYear(time)) {
            textView.setText(LDate.getDate(time, "M月d日"));
            return;
        }
        textView.setText(LDate.getDate(time, "yyyy年M月d日"));
    }

    @BindingAdapter("markTime")
    public static void setMarkTime(TextView textView, long time) {
        if (LDate.isToday(time)) {
            textView.setText("今天");
            return;
        }
        if (LDate.isYesterday(time)) {
            textView.setText("昨天");
            return;
        }
        if (LDate.isPreViewWeek(time)) {
            textView.setText(LDate.dayOfWeek(time));
            return;
        } else if (LDate.isSameYear(time)) {
            textView.setText(LDate.getDate(time, "M月d日"));
            return;
        }
        textView.setText(LDate.getDate(time, "yyyy年M月d日"));
    }

    @BindingAdapter("setTime")
    public static void setTime(TextView textView, long time) {
        textView.setText(formatMessageTime(time));
    }

    @BindingAdapter("markSendTime")
    public static void markSendTime(TextView textView, long time) {
        textView.setText(markSendTime(time));
    }

    @BindingAdapter("setSendTime")
    public static void setSendTime(TextView textView, long time) {
        textView.setText(" 发送于" + formatMessageTime(time));
    }

    public static String formatMessageTime(long time) {
        if (LDate.isToday(time)) {
            return (LDate.getDate(time, "HH:mm"));
        }
        if (LDate.isYesterday(time)) {
            return ("昨天 " + LDate.getDate(time, "HH:mm"));
        }
        if (LDate.isPreViewWeek(time)) {
            return (LDate.dayOfWeek(time) + " " + LDate.getDate(time, "HH:mm"));
        }
        if (LDate.isSameYear(time)) {
            return (LDate.getDate(time, "M月d日  HH:mm"));
        }
        return (LDate.getDate(time, "yyyy年M月d日  HH:mm"));
    }

    public static String markSendTime(long time) {
        if (LDate.isToday(time)) {
            return (LDate.getDate(time, "HH:mm:ss"));
        }
        if (LDate.isYesterday(time)) {
            return ("昨天 " + LDate.getDate(time, "HH:mm:ss"));
        }
        if (LDate.isPreViewWeek(time)) {
            return (LDate.dayOfWeek(time) + " " + LDate.getDate(time, "HH:mm:ss"));
        }
        if (LDate.isSameYear(time)) {
            return (LDate.getDate(time, "M/d  HH:mm:ss"));
        }
        return (LDate.getDate(time, "yyyy/M/d  HH:mm:ss"));
    }

    @BindingAdapter("fileCircleType")
    public static void setFileCircleIcon(SimpleDraweeView simpleDraweeView, String fileName) {
        if (!TextUtils.isEmpty(fileName)) {
            String end = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length()).toLowerCase();
            switch (end) {
                case "zip":
                case "rar":
                case "7z":
                case "apk":
                    simpleDraweeView.setImageResource(R.mipmap.icon_zip_circle);
                    break;
                case "m4a":
                case "mp3":
                case "mid":
                case "xmf":
                case "ogg":
                case "wav":
                    simpleDraweeView.setImageResource(R.mipmap.icon_voice_circle);
                    break;
                case "doc":
                case "docx":
                    simpleDraweeView.setImageResource(R.mipmap.icon_word_circle);
                    break;
                case "xls":
                case "xlsx":
                    simpleDraweeView.setImageResource(R.mipmap.icon_excel_circle);
                    break;
                case "txt":
                    simpleDraweeView.setImageResource(R.mipmap.icon_txt_circle);
                    break;
                case "ppt":
                case "pptx":
                    simpleDraweeView.setImageResource(R.mipmap.icon_ppt_circle);
                    break;
                case "pdf":
                    simpleDraweeView.setImageResource(R.mipmap.icon_pdf_circle);
                    break;
                default:
                    simpleDraweeView.setImageResource(R.mipmap.icon_document_circle);
                    break;
            }
        }
    }

    @BindingAdapter("visibleGone")
    public static void setVisibleGone(View view, boolean visible) {
        view.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    @BindingAdapter("avatarChatRecordServerBean")
    public static void avatarChatRecordServerBean(View view, ChatRecordAllSearchServerBean serverBean) {
        BasicBindingAdapters.setAvatar(view, serverBean.getAvatar(), serverBean.getStrAvatar(), 0);
    }

    @BindingAdapter("textMultiItemEntity")
    public static void textMultiItemEntity(TextView view, MultiItemEntity item) {
        if (item instanceof SearchGroup) {
            Group group = ((SearchGroup) item).getGroup();
            if (group != null) {
                view.setText(TextUtils.isEmpty(group.getGroupRemark()) ? group.getGroupName() : group.getGroupRemark());
            }
        } else if (item instanceof SearchContact) {
            Contact contact = ((SearchContact) item).getContact();
            if (contact != null) {
                view.setText(contact.getShowName());
            }
        }
    }

    @BindingAdapter("avatarSummaryBean")
    public static void avatarSummaryBean(View view, ChatRecordSummaryBean summaryBean) {
        BasicBindingAdapters.setAvatar(view, summaryBean.getAvatar(), summaryBean.getStrAvatar(), 0);
    }

    @BindingAdapter({"chatMessage", "chatListener"})
    public static void setChatItemListener(View view, ChatMessage chatMessage, ChatItemListener chatItemListener) {
        if (chatItemListener != null) {
            view.setOnClickListener(v -> chatItemListener.onMessageClick(view, chatMessage));

            view.setOnLongClickListener(v -> {
                if (isLongClickDisabled(chatMessage)) return true;
                chatItemListener.onMessageLongClick(v, chatMessage);
                return true;
            });
        }

    }

    private static boolean isLongClickDisabled(ChatMessage chatMessage) {
        return chatMessage instanceof MessageForHint ||(chatMessage instanceof MessageForExtensionCard && ((MessageForExtensionCard)chatMessage).isStreaming());
    }

    @BindingAdapter({"showUserByUserId"})
    public static void showUserByUserId(TextView textView, String showUserByUserId) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(showUserByUserId);
        String showName = "";
        if (contact != null) {
            showName = contact.getUserName();
        }
        if (TextUtils.isEmpty(showName)) {
            showName = String.valueOf(showUserByUserId);
        }
        textView.setText(showName);
    }

    @BindingAdapter({"meetingTitle"})
    public static void meetingTitle(CommonIconTextView textView, String meetingTitle) {
        textView.setText(meetingTitle);
    }

    @BindingAdapter({"resendChatMessage", "resendChatListnner"})
    public static void setResendChatItemListnner(ImageView view, ChatMessage resendChatMessage, ChatItemListener resendChatListnner) {
        view.setOnClickListener(v -> resendChatListnner.onResendClick(view, resendChatMessage));
    }

    @BindingAdapter({"singleMsgStatus", "hideStatus"})
    public static void setStatus(ImageView imageView, ChatMessage chatMessage, boolean hideStatus) {
        imageView.setEnabled(false); //默认设置enabled为false，不可点击
        resetStatusIvAnim(imageView);
        if (chatMessage == null) return;
        switch (chatMessage.getStatus()) {
            case MessageConstants.MSG_STATE_SENDING:
                updateSendingMsgStatusIv(imageView, chatMessage);
                break;
            case MessageConstants.MSG_STATE_FAILURE:
                imageView.setEnabled(true);
                imageView.setImageResource(R.drawable.chat_ic_icon_message_failure);
                break;
            case MessageConstants.MSG_STATE_DELIVERY:
                if (hideStatus) {
                    imageView.setVisibility(View.GONE);
                    return;
                }
                imageView.setImageResource(R.drawable.chat_ic_icon_unread_message);
                break;
            case MessageConstants.MSG_STATE_PART_READ:
                if (hideStatus) {
                    imageView.setVisibility(View.GONE);
                    return;
                }
                imageView.setEnabled(true);
                imageView.setImageResource(R.drawable.chat_ic_icon_read_part_message);
                break;
            case MessageConstants.MSG_STATE_READ:
                if (hideStatus) {
                    imageView.setVisibility(View.GONE);
                    return;
                }
                imageView.setEnabled(true);
                imageView.setImageResource(R.drawable.chat_ic_icon_read_message);
                break;
            default:
                imageView.setVisibility(View.GONE);
                break;
        }
        // 发给自己的消息 只有发送成功后才隐藏状态
        if (TextUtils.equals(chatMessage.getChatId(), HiKernel.getHikernel().getAccount().getUserId())
                && chatMessage.getStatus() >= MessageConstants.MSG_STATE_DELIVERY) {
            imageView.setVisibility(View.GONE);
        }
    }

    private static void resetStatusIvAnim(ImageView imageView) {
        Object tag = imageView.getTag();
        if (tag instanceof Runnable) {
            imageView.removeCallbacks((Runnable) tag);
            imageView.setTag(null);
        }
        imageView.clearAnimation();
        imageView.setVisibility(View.VISIBLE);
    }

    private static void updateSendingMsgStatusIv(ImageView imageView, ChatMessage chatMessage) {
        imageView.setImageResource(R.drawable.chat_ic_icon_send_message);
        imageView.setVisibility(View.INVISIBLE);
        Runnable runnable = () -> {
            imageView.setVisibility(View.VISIBLE);
            if (ServiceManager.getInstance().getMessageService().getFinishedMsgIds().contains(chatMessage.getMid())) {
                ServiceManager.getInstance().getMessageService().getFinishedMsgIds().remove(chatMessage.getMid());
                return;
            }
            Animation rotateAnimation = AnimationUtils.loadAnimation(imageView.getContext(), R.anim.chat_anim_circle_rotate);
            LinearInterpolator interpolator = new LinearInterpolator();
            rotateAnimation.setInterpolator(interpolator);
            imageView.startAnimation(rotateAnimation);
        };
        imageView.postDelayed(runnable, getAnimStartDelay(chatMessage));
        imageView.setTag(runnable);
    }

    private static long getAnimStartDelay(ChatMessage chatMessage) {
        if (ServiceManager.getInstance().getMessageService().getLoadingMsgIds().contains(chatMessage.getMid())
                || ServiceManager.getInstance().getMessageService().getResendingMsgIds().contains(chatMessage.getMid())) {
            return 0;
        } else {
            ServiceManager.getInstance().getMessageService().addLoadingMsgId(chatMessage.getMid());
            return 500;
        }
    }

    @BindingAdapter({"setHintSpanableText", "setHintCallback", "groupSource"})
    public static void showHintSpanable(MLinkEmotionTextView linkEmotionTextView, MessageForHint messageForHint, ChatItemListener listener, int groupSource) {
        if (messageForHint != null && StringUtils.isNotEmpty(messageForHint.getContent())) {
            linkEmotionTextView.setHintContent(messageForHint, listener, groupSource);
            if (messageForHint.getTextColor() != 0) {
                linkEmotionTextView.setTextColor(messageForHint.getTextColor());
            }
            if (messageForHint.getBgColor() != 0) {
                Drawable drawable = linkEmotionTextView.getResources().getDrawable(R.drawable.bg_corner_27);
                drawable.setColorFilter(messageForHint.getBgColor(), PorterDuff.Mode.SRC_ATOP);
                linkEmotionTextView.setBackground(drawable);
                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) linkEmotionTextView.getLayoutParams();
                layoutParams.topMargin = QMUIDisplayHelper.dpToPx(8);
                layoutParams.bottomMargin = QMUIDisplayHelper.dpToPx(8);
            } else {
                linkEmotionTextView.setBackground(null);
                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) linkEmotionTextView.getLayoutParams();
                layoutParams.topMargin = 0;
                layoutParams.bottomMargin = 0;
            }
        }
    }

    @BindingAdapter("showPicTiny")
    public static void showPicTiny(SimpleDraweeView view, MessageForPic messageForPic) {
//        ImageInfo tiny = messageForPic.getTiny();
//        if (tiny != null) {
//            BasicBindingAdapters.setImage(view, tiny.getUrl());
//        }
        BasicBindingAdapters.setImage(view, ChatImageHelper.getMsgThumbnailUrl(messageForPic));
    }

    @BindingAdapter("setDuration")
    public static void setAudioDuration(TextView textView, int duration) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
        int hours = duration / 3600;
        if (hours > 0) {
            simpleDateFormat.applyPattern("HH:mm:ss");
        } else {
            simpleDateFormat.applyPattern("mm:ss");
        }
        textView.setText(simpleDateFormat.format(duration * 1000));
    }

    @BindingAdapter("setAnimationWidth")
    public static void setAnimationWidth(LottieAnimationView lottieAnimationView, MessageForAudio messageForAudio) {
        Object wrapper = lottieAnimationView.getTag();
        if (wrapper instanceof AudioPlayWrapper) {
            AudioPlayWrapper audioPlayWrapper = (AudioPlayWrapper) wrapper;
            if (audioPlayWrapper.isPlay(messageForAudio)) {
                lottieAnimationView.playAnimation();
            } else {
                lottieAnimationView.pauseAnimation();
            }
        }
    }

    @BindingAdapter("setFavoriteTime")
    public static void setFavoriteTime(TextView textView, long time) {
        if (LDate.isSameYear(time)) {
            textView.setText(LDate.getDate(time, "M月d日"));
            return;
        }
        textView.setText(LDate.getDate(time, "yyyy年M月d日"));
    }

    @BindingAdapter("avatarCardContact")
    public static void setCardContactAvatar(View view, MessageForUserCard messageForUserCard) {
        if (messageForUserCard == null) {
            BasicBindingAdapters.setAvatar(view, null, "");
            return;
        }
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(messageForUserCard.getUserId());
        if (contact == null) {
            BasicBindingAdapters.setAvatar(view, null, "");
            return;
        }
        String url = contact.getAvatar();
        String avatar = ContactUtils.getDisplayAvatarStr(contact.getUserName());
        BasicBindingAdapters.setAvatar(view, url, avatar);
    }

    @BindingAdapter("groupCardContact")
    public static void setGroupCardContact(View view, MessageForGroupCard messageForGroupCard) {
        if (messageForGroupCard == null) {
            BasicBindingAdapters.setAvatar(view, null, "");
            return;
        }
        String url = messageForGroupCard.getGroupCard().getAvatar();
        String avatar = ContactUtils.getDisplayAvatarStr(messageForGroupCard.getGroupCard().getGroupName());
        BasicBindingAdapters.setAvatar(view, url, avatar);
    }

    @BindingAdapter("max_width_percent")
    public static void setMaxWidthPercent(View view, float percent) {
        float screenWidthInDP = QMUIDisplayHelper.getScreenWidthInDP(view.getContext());
        if (screenWidthInDP <= 400) {
            return;
        }
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) view.getLayoutParams();
        layoutParams.matchConstraintMaxWidth = (int) (QMUIDisplayHelper.getScreenWidth(view.getContext()) * percent);
        view.setLayoutParams(layoutParams);
    }

    @BindingAdapter({"showSticker", "showDynamic"})
    public static void showSticker(SimpleDraweeView view, MessageForSticker messageForSticker, Boolean showDynamic) {
        if (messageForSticker != null && messageForSticker.getStickerInfo() != null) {
            if (messageForSticker.isEmoji()) {
                EmotionItem emojiItem = new EmojiHelper().getEmojiItemBySid(messageForSticker.getStickerInfo().getStickerId());
                if (emojiItem != null) {
                    if (showDynamic && StringUtils.isNotEmpty(emojiItem.getDynamicUrl())) {
                        innerShowMsgPic(view, emojiItem.getDynamicUrl());
                    } else {
                        innerShowMsgPic(view, emojiItem.getUri());
                    }
                }
                return;
            }
            if (messageForSticker.getStickerInfo().getOriginImage() != null) {
                innerShowMsgPic(view, messageForSticker.getStickerInfo().getOriginImage().getUrl());
            }
        }
    }

    @BindingAdapter("showStickerScale")
    public static void showStickerScale(SimpleDraweeView view, MessageForSticker messageForSticker) {
        if (messageForSticker != null && messageForSticker.getStickerInfo() != null && messageForSticker.getStickerInfo().getOriginImage() != null) {
            int width = messageForSticker.getStickerInfo().getOriginImage().getWidth();
            int height = messageForSticker.getStickerInfo().getOriginImage().getHeight();
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            if (messageForSticker.isEmoji()) {
                // 黄豆表情
                layoutParams.width = QMUIDisplayHelper.dpToPx(48);
                layoutParams.height = QMUIDisplayHelper.dpToPx(48);
            } else {
                ImageUtils.ImageSize imageSize = ChatImageHelper.getStickerImageSize(width, height, true);
                layoutParams.width = imageSize.getWidth();
                layoutParams.height = imageSize.getHeight();
                TLog.info(TAG, "showStickerScale,imageSize:" + imageSize + ",layoutParams:" + layoutParams);
            }
            view.setLayoutParams(layoutParams);
        }
    }

    @BindingAdapter("setRedEnvelopType")
    public static void setRedEnvelopType(TextView textView, MessageForRedEnvelope messageForRedEnvelope) {
        if (messageForRedEnvelope == null) {
            return;
        }
        if (messageForRedEnvelope.getRedEnvelope().getType() == MessageConstants.MSG_RED_ENVELOPE_NORMAL) {
            textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }
        if (messageForRedEnvelope.getRedEnvelope().getType() == MessageConstants.MSG_RED_ENVELOPE_AVERAGE) {
            textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.chat_ic_icon_chat_redpacket_jun, 0);
        }
        if (messageForRedEnvelope.getRedEnvelope().getType() == MessageConstants.MSG_RED_ENVELOPE_LUCK) {
            textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.chat_ic_icon_chat_redpacket_pin, 0);
        }
    }

    @BindingAdapter({"favoriteMessage", "favoriteCallback"})
    public static void bindClickGestureListener(TextMessageLayout layout, FavoriteInterface bean, FavoriteListCallback callback) {
        RichTextView textView = layout.getTextView();
        textView.setOnLongClickListener(v -> {
            callback.onLongClick(bean);
            return true;
        });
    }

    @BindingAdapter("videoThumbnail")
    public static void showVideoThumbnail(SimpleDraweeView view, MessageForVideo messageForVideo) {
        TLog.info("showVideoThumbnail", "showVideoThumbnail,messageForVideo:" + messageForVideo.getVideoInfo().getThumbnail());
        if (messageForVideo != null && messageForVideo.getVideoInfo() != null && messageForVideo.getVideoInfo().getThumbnail() != null) {
            ViewGroup parent = (ViewGroup) view.getParent();
            ImageUtils.ImageSize imageSize = ChatImageHelper.initVideoImageSize(
                    messageForVideo.getVideoInfo().getThumbnail().getWidth(), messageForVideo.getVideoInfo().getThumbnail().getHeight());
            ViewGroup.LayoutParams layoutParams = parent.getLayoutParams();
            layoutParams.width = imageSize.getWidth();
            layoutParams.height = imageSize.getHeight();
            parent.setLayoutParams(layoutParams);
            BasicBindingAdapters.setImage(view, messageForVideo.getVideoInfo().getThumbnail().url);
        }
    }

    @BindingAdapter("showPicScale")
    public static void showPicScale(SimpleDraweeView view, ChatMessage chatMessage) {
        if (!(chatMessage instanceof MessageForPic)) {
            return;
        }
        MessageForPic messageForPic = (MessageForPic) chatMessage;

        if (messageForPic.getOrigin() != null) {
            ImageUtils.ImageSize imageSize = ChatImageHelper.getImageSize(messageForPic);
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            layoutParams.width = imageSize.getWidth();
            layoutParams.height = imageSize.getHeight();
            TLog.info(TAG, "showPicScale,imageSize:" + imageSize + ",layoutParams:" + layoutParams);
            view.setLayoutParams(layoutParams);
        }
    }

    /**
     * 消息图片/gif展示
     */
    @BindingAdapter("showPicGif")
    public static void showPicGif(SimpleDraweeView view, ChatMessage chatMessage) {
        if (!(chatMessage instanceof MessageForPic)) {
            return;
        }
        MessageForPic messageForPic = (MessageForPic) chatMessage;

        if (messageForPic.getOrigin() != null) {
            innerShowMsgPic(view, ChatImageHelper.getMsgPreviewImageUrl(messageForPic));
        }
    }

    @BindingAdapter({"thumbnailUrl", "thumbnailReply", "thumbnailMessage"})
    public static void showReplyThumbnail(SimpleDraweeView view, String url, ChatMessage replyMessage, ChatMessage chatMessage) {
        HashMap<String, String> paramsMap = new HashMap<>();
        paramsMap.put("type", "1");
        paramsMap.put("width", view.getLayoutParams().width * 2 + "");
        paramsMap.put("height", view.getLayoutParams().height * 2 + "");
        String newUrl = URLUtils.addParams(url, paramsMap, true);
        TLog.debug(TAG, "showThumbnailByUrl,url:" + newUrl);
        updateThumbnailImageBorder(view, replyMessage, chatMessage);
        innerShowMsgPic(view, newUrl);
    }

    private static void updateThumbnailImageBorder(SimpleDraweeView view, ChatMessage replyMessage, ChatMessage chatMessage) {
        boolean isStickerMessage = replyMessage instanceof MessageForSticker;
        boolean isSendByMe = chatMessage != null && chatMessage.isSendByMe();
        int colorResId = isStickerMessage ? R.color.transparent : (isSendByMe ? R.color.color_C4D3FF : R.color.color_E1E1E5);
        RoundingParams roundingParams = RoundingParams.fromCornersRadius(QMUIDisplayHelper.dpToPx(4));
        roundingParams.setBorder(view.getContext().getColor(colorResId), QMUIDisplayHelper.dpToPx(1));
        if (view.getHierarchy() != null) {
            view.getHierarchy().setRoundingParams(roundingParams);
        }
    }

    @BindingAdapter("setRedEnvelopNote")
    public static void setRedEnvelopNote(TextView textView, MessageForRedEnvelope messageForRedEnvelope) {
        if (messageForRedEnvelope == null || messageForRedEnvelope.getRedEnvelope() == null) {
            return;
        }
        if (messageForRedEnvelope.getRedEnvelope().getStatus() == MessageConstants.MSG_RED_ENVELOPE_STATUS_SNATCH) {
            if (StringUtils.isEmpty(messageForRedEnvelope.getRedEnvelope().getNote())) {
                textView.setText("恭喜发财，大吉大利！");
            } else {
                textView.setText(messageForRedEnvelope.getRedEnvelope().getNote());
            }
        }
        if (messageForRedEnvelope.getRedEnvelope().getStatus() == MessageConstants.MSG_RED_ENVELOPE_STATUS_ROBBED) {
            textView.setText("手慢了，红包派完了！");
        }
        if (messageForRedEnvelope.getRedEnvelope().getStatus() == MessageConstants.MSG_RED_ENVELOPE_STATUS_OVERDUE) {
            textView.setText("红包已过期");
        }
    }

    @BindingAdapter("showAction")
    public static void setActionImage(ImageView image, ChatMessage msg) {
        if (msg != null) {
            if (msg.getStatus() > MessageConstants.MSG_STATE_FAILURE) {
                image.setImageResource(R.drawable.chat_ic_message_reply_detail_pitched);
            } else if (msg.getStatus() == MessageConstants.MSG_STATE_FAILURE) {
                image.setImageResource(R.drawable.chat_ic_icon_message_failure);
            } else if (msg.getStatus() == MessageConstants.MSG_STATE_SENDING) {
                image.setImageResource(R.drawable.chat_ic_icon_message_sending);
            }
        }
    }

    @BindingAdapter({"chatMessage", "selectUpdate", "containerMarginTop"})
    public static void setTextSelect(TextMessageLayout textMessageLayout,
                                     ChatMessage chatMessage, ChatMsgTextSelectionUpdateCallback textSelectionUpdateCallback, int containerMarginTop) {
        if (chatMessage == null) {
            return;
        }
        RichTextView textView = textMessageLayout.getTextView();
        PopChatLongClickBinding binding = PopChatLongClickBinding.inflate(LayoutInflater.from(textView.getContext()));
        TextSelection selection = getMsgTextSelection(textView, binding.getRoot(), containerMarginTop);
        selection.setOnTextSelectionLifecycleCallback(new OnTextSelectionLifecycleCallback() {
            @Override
            public void onUpDate(TextSelection textSelection) {
                textSelectionUpdateCallback.onTextSelectionUpDate(textSelection, chatMessage, binding, textView);
            }

            @Override
            public void onShow(TextSelection textSelection) {
                textSelectionUpdateCallback.onTextSelectionShow(textSelection, chatMessage, binding, !textMessageLayout.getExpanded());
                if (!textMessageLayout.getExpanded()) {
                    textSelection.postShowSelectView(TextSelection.DEFAULT_SHOW_DURATION);
                    textMessageLayout.setExpanded(true);
                }
            }
        });
    }

    public static TextSelection getMsgTextSelection(@NonNull final SelectableTextView textView, View menu, int containerMarginTop) {
        return new TextSelection.Builder(textView)
                .setContainerMarginTop(containerMarginTop)
                .setMenuView(menu)
                .setCursorHandleColor(ThemeUtils.useNewTheme ? R.color.color_text_primary : R.color.color_6077E5)
                .setCursorHandleSizeInDp(18)
                .setNoSplitSpanClasses(SetsKt.hashSetOf(
                        CodeSpan.class,
                        CodeBlockSpan.class,
                        CustomCodeBlockSpan.class,
                        HyperlinkSpan.class))
                .setSelectListener(ChatTextSelectionHelper.getInstance().getTextSelectionListener())
                .setOnMenuShowStrategyCallback((view, mSelectionInfo) -> true)
                .build();
    }

    @BindingAdapter(value = {"setSpanableText", "hypertextList", "textLinkListener"}, requireAll = false)
    public static void showSpanable(TextMessageLayout layout, MessageForMarkdownText messageForText, List<Hypertext> hypertextList, MessageWithInlineLinkCallback callback) {
        if (messageForText == null) {
            return;
        }
        layout.renderMessage(messageForText, hypertextList, (view, message, url) -> {
            if (checkIsZhiShuForbidden(hypertextList, url)) return;
            if (callback != null) {
                callback.onInlineLinkClick(view, message, url);
            }
        });
    }

    @BindingAdapter(value = {"setSpanableText", "hypertextList", "textLinkListener", "translate"})
    public static void showSpanable(TextMessageLayout layout, MessageForMarkdownText messageForText, List<Hypertext> hypertextList, MessageWithInlineLinkCallback callback, Boolean translate) {
        if (messageForText == null) {
            return;
        }
        if (translate != null && translate) {
            if (messageForText.isTranslated()) {
                layout.setVisibility(View.VISIBLE);
                layout.renderTranslatedMessage(messageForText, hypertextList, (view, message, url) -> {
                    if (checkIsZhiShuForbidden(hypertextList, url)) return;
                    if (callback != null) {
                        callback.onInlineLinkClick(view, message, url);
                    }
                });
            } else {
                layout.setVisibility(View.GONE);
            }
        } else {
            layout.renderMessage(messageForText, hypertextList, (view, message, url) -> {
                if (checkIsZhiShuForbidden(hypertextList, url)) return;
                if (callback != null) {
                    callback.onInlineLinkClick(view, message, url);
                }
            });
        }
    }

    @BindingAdapter({"translatedMessage", "chatItemListener"})
    public static void setTranslatedListener(TextMessageLayout layout, ChatMessage chatMessage, ChatItemListener chatItemListener) {
        RichTextView view = layout.getTextView();
        view.setOnLongClickListener(v -> {
            chatItemListener.showTranslatedMessageMenu(view, chatMessage);
            return true;
        });
    }

    @BindingAdapter({"translatedMessage", "chatItemListener"})
    public static void setTranslatedListener(TextView view, ChatMessage chatMessage, ChatItemListener chatItemListener) {
        view.setOnLongClickListener(v -> {
            chatItemListener.showTranslatedMessageMenu(view, chatMessage);
            return true;
        });
    }

    @BindingAdapter(value = {"setSpanableText", "hypertextList", "textLinkListener", "translate"}, requireAll = false)
    public static void showSpanable(TextMessageLayout layout, MessageForText messageForText, List<Hypertext> hypertextList, MessageWithInlineLinkCallback callback, Boolean translate) {
        if (messageForText == null) {
            return;
        }
        long urlRuleValidTime = ProcessHelper.getUserPreferences().getLong(Constants.KEY_URL_RULE_VALID_TIME, 0L);
        layout.setUseLinkify(messageForText.getTime() < urlRuleValidTime);
        if (translate != null && translate) {
            if (messageForText.isTranslated()) {
                layout.setVisibility(View.VISIBLE);
                layout.renderTranslatedMessage(messageForText, hypertextList, (view, message, url) -> {
                    if (checkIsZhiShuForbidden(hypertextList, url)) return;
                    if (callback != null) {
                        callback.onInlineLinkClick(view, message, url);
                    }
                });
            } else {
                layout.setVisibility(View.GONE);
            }
        } else {
            layout.renderMessage(messageForText, hypertextList, (view, message, url) -> {
                if (checkIsZhiShuForbidden(hypertextList, url)) return;
                if (callback != null) {
                    callback.onInlineLinkClick(view, message, url);
                }
            });
        }
    }

    @BindingAdapter(value = {"setSpanableText", "hypertextList", "textLinkListener"}, requireAll = false)
    public static void showSpanable(TextMessageLayout layout, MessageForRichText richText, List<Hypertext> hypertextList, MessageWithInlineLinkCallback callback) {
        if (richText == null) {
            return;
        }
        long urlRuleValidTime = ProcessHelper.getUserPreferences().getLong(Constants.KEY_URL_RULE_VALID_TIME, 0L);
        layout.setUseLinkify(richText.getTime() < urlRuleValidTime);
        layout.renderMessage(richText, hypertextList, (view, message, url) -> {
            if (checkIsZhiShuForbidden(hypertextList, url)) return;
            if (callback != null) {
                callback.onInlineLinkClick(view, message, url);
            }
        });
    }

    private static boolean checkIsZhiShuForbidden(List<Hypertext> hypertextList, String url) {
        if (ServiceManager.getInstance().getSecurityService().isForbidden(BusinessModuleEnum.ZHI_SHU) //直书被禁用
                && getInlineLinkType(url, hypertextList) == 1) { //改链接是直书类型
            ToastUtils.failure(R.string.no_permission_access);
            return true;
        }
        return false;
    }

    /**
     * 通过比对，判断点击的链接是什么类型
     */
    private static int getInlineLinkType(String targetUrl, List<Hypertext> hypertextList) {
        if (LList.isEmpty(hypertextList)) {
            return 0;
        }
        for (Hypertext hypertext : hypertextList) {
            if (TextUtils.equals(hypertext.getReferenceContent(), targetUrl) || TextUtils.equals(hypertext.getDestinationLink(), targetUrl)) {
                return hypertext.getType();
            }
        }
        return 0;
    }

    @BindingAdapter({"chatMessage", "chatListener", "showReply"})
    public static void setAudioBgAndListener(View view, ChatMessage chatMessage, ChatItemListener chatItemListener, Boolean showReply) {
        if (chatMessage == null) {
            return;
        }
        view.setOnClickListener(v -> chatItemListener.onMessageClick(view, chatMessage));

        view.setOnLongClickListener(v -> {
            if (chatMessage instanceof MessageForRedEnvelope || chatMessage instanceof MessageForHint) {
                return true;
            }
            chatItemListener.onMessageLongClick(v, chatMessage);
            return true;
        });
        if ((showReply != null && showReply) && !LList.isEmpty(chatMessage.getEmojiReplies())) {
            if (chatMessage.isSendByMe()) {
                view.setBackgroundResource(R.drawable.chat_bg_message_with_emoji_or_reply_to);
            } else {
                view.setBackgroundResource(R.drawable.chat_bg_message_with_emoji_or_reply);
            }

        } else {
            if (chatMessage.isSendByMe()) {
                view.setBackgroundResource(R.drawable.bg_corner_8_chat_message_to);
            } else {
                view.setBackgroundResource(R.drawable.chat_bg_message_from_text);
            }

        }
    }

    @BindingAdapter({"systemCardColor"})
    public static void setSystemCardTextColor(TextView textView, @Nullable MessageForSystemCard.SystemCardRichText richText) {
        if (richText == null || richText.getColor() == 0) {
            textView.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_0D0D1A));
        } else {
            textView.setTextColor(richText.getColor());
        }
    }

    @BindingAdapter(value = {"setCommonIconText", "textSize", "iconSize", "agreements", "messageType", "setHintCallback"}, requireAll = false)
    public static void showCommonIconText(CommonIconTextView textView, String text, int textSize, int iconSize, List<Agreement> agreements, int messageType, ChatItemListener listener) {
        if (!TextUtils.isEmpty(text)) {
            textView.setEmotionText(text, textSize, iconSize, agreements, messageType, listener);
        }
    }

    @BindingAdapter("reWrite")
    public static void showCommonIconText(TextView textView, ChatMessage chatMessage) {
        if (chatMessage != null && chatMessage.isWithdrawn() && chatMessage.isSendByMe()) {
            long mid = -chatMessage.getMid();
            HashMap<Long, Long> withDrawnMessages = ServiceManager.getInstance().getMessageService().getLocalWithDrawMsg();
            Set<Long> ids = withDrawnMessages.keySet();
            if (ids.contains(mid)) {
                if (System.currentTimeMillis() - withDrawnMessages.get(mid) < MessageConstants.MSG_AT_ALL_REWRITE_TIME) {
                    textView.setVisibility(View.VISIBLE);
                } else {
                    textView.setVisibility(View.GONE);
                    ServiceManager.getInstance().getMessageService().getLocalWithDrawMsg().remove(mid);
                }
            } else {
                textView.setVisibility(View.GONE);
            }
        } else {
            textView.setVisibility(View.GONE);
        }
    }

    @BindingAdapter("choseSystemCard")
    public static void decideOneChildForSystemCard(ViewGroup viewGroup, MessageForSystemCard message) {
        if (viewGroup.getChildCount() != 2) {
            TLog.error("BindingAdapter", "无法识别系统卡片的展示目标 View");
            if (viewGroup.getChildCount() < 2) {
                return;
            }
        }
        if (isScheduleCard(message)) {
            viewGroup.getChildAt(0).setVisibility(View.VISIBLE);
            viewGroup.getChildAt(1).setVisibility(View.GONE);
        } else {
            viewGroup.getChildAt(0).setVisibility(View.GONE);
            viewGroup.getChildAt(1).setVisibility(View.VISIBLE);
        }
    }

    private static boolean isScheduleCard(MessageForSystemCard message) {
        if (message == null) {
            return false;
        }
        if (message.scheduleCardInfo != null) {
            return true;
        }
        Map<String, String> params = URLUtils.parseUrlParams(message.getCardProtocol());
        String protocolType = params.get("type");
        if (Constants.AGREEMENT_CALENDAR_INFO.equals(protocolType)) {
            String scheduleTypeString = params.get("scheduleType");
            return String.valueOf(Constants.TYPE_SCHEDULE).equals(scheduleTypeString);
        }
        return false;
    }

    @BindingAdapter("systemCardScheduleInfo")
    public static void setSystemCardScheduleInfo(ViewGroup viewGroup, MessageForSystemCard msg) {
        TextView tvPositive = viewGroup.findViewById(R.id.tv_btn_positive);
        TextView tvNegative = viewGroup.findViewById(R.id.tv_btn_negative);
        tvPositive.setVisibility(View.GONE);
        tvNegative.setVisibility(View.GONE);
        if (msg != null && msg.taskInfo != null && msg.taskInfo.getCardType() == Constants.TYPE_MESSAGE_CARD_TASK_TYPE) {
            tvPositive.setVisibility(View.VISIBLE);
            //waitingType 1 认领 2 已认领 3 已被认领
            switch (msg.taskInfo.getWaitingType()) {
                case 1:
                    tvPositive.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_282833));
                    tvPositive.setText(BaseApplication.getApplication().getResources().getString(R.string.workflow_task_claim));
                    tvPositive.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            TaskOperateRequest request = new TaskOperateRequest(new BaseApiRequestCallback<TaskOperateResponse>() {
                                @Override
                                public void onSuccess(ApiData<TaskOperateResponse> data) {
                                    ToastUtils.ss(BaseApplication.getApplication().getResources().getString(R.string.chat_task_claim_success));
                                }

                                @Override
                                public void onComplete() {

                                }

                                @Override
                                public void onFailed(ErrorReason reason) {

                                }
                            });
                            request.taskId = msg.taskInfo.getTaskId();
                            request.type = Constants.TYPE_TASK_DETAIL_OPERATE_CLAIM;
                            request.msgId = String.valueOf(msg.getMid());
                            request.source = 2;
                            HttpExecutor.execute(request);
                        }
                    });
                    break;
                case 2:
                    tvPositive.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_5D68E8));
                    tvPositive.setText(BaseApplication.getApplication().getResources().getString(R.string.chat_task_claimed));
                    tvPositive.setOnClickListener(null);
                    break;
                default:
                    tvPositive.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_5D68E8));
                    tvPositive.setText(BaseApplication.getApplication().getResources().getString(R.string.chat_other_task_claimed));
                    tvPositive.setOnClickListener(null);
                    break;
            }
        }
    }

    @BindingAdapter("actionPositive")
    public static void bindSchedulePositiveAction(TextView view, MessageForSystemCard msg) {
        String userId = HiKernel.getHikernel().getAccount().getUserId();
        if (msg == null || msg.scheduleCardInfo == null || !msg.scheduleCardInfo.getShowButton()) {
            view.setVisibility(View.GONE);
            return;
        }
        //业务判断可见
        if (msg.getCardTitle() == null
                || msg.getCardTitle().getStyle() == MessageConstants.SYSTEM_CARD_CONTENT_DELETED
                || msg.scheduleCardInfo.getDeleted() == Constants.SCHEDULE_DELETE) {
            view.setVisibility(View.GONE);
            return;
        }
        GroupService groupService = ServiceManager.getInstance().getGroupService();
        if (!groupService.isInGroups(msg.scheduleCardInfo.getGroupIds())
                && (msg.scheduleCardInfo.getNewUserIds() == null || !msg.scheduleCardInfo.getNewUserIds().contains(userId))) {
            //没参与
            view.setVisibility(View.VISIBLE);
            view.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_282833));
            view.setBackgroundResource(R.drawable.chat_bg_schedule_card_button_default);
            view.setText(BaseApplication.getApplication().getResources().getString(R.string.schedule_join_schedule));
            view.setOnClickListener(v -> {
                ScheduleJoinRequest request = new ScheduleJoinRequest(new BaseApiRequestCallback<HttpResponse>() {
                    @Override
                    public void onSuccess(ApiData<HttpResponse> data) {
                        ToastUtils.ss(BaseApplication.getApplication().getResources().getString(R.string.schedule_joined_schedule_success));
                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {

                    }
                });
                request.scheduleId = String.valueOf(msg.scheduleCardInfo.getScheduleId());
                request.msgId = String.valueOf(msg.getMid());
                HttpExecutor.execute(request);
            });
        } else if (!LList.isEmpty(msg.scheduleCardInfo.getAcceptIds()) && msg.scheduleCardInfo.getAcceptIds().contains(userId)) {
            //表示我已经接收 展示接收按钮
            view.setVisibility(View.VISIBLE);
            view.setOnClickListener(null);
            view.setTextColor(ThemeUtils.getThemeTextColorInt());
            view.setBackgroundResource(ThemeUtils.useNewTheme ? R.drawable.chat_bg_schedule_card_button_positive : R.drawable.chat_bg_schedule_card_button_positive_old);
            view.setText(BaseApplication.getApplication().getResources().getString(R.string.schedule_accept_ed));
        } else {
            view.setVisibility(View.VISIBLE);
            view.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_282833));
            view.setBackgroundResource(R.drawable.chat_bg_schedule_card_button_default);
            view.setText(BaseApplication.getApplication().getResources().getString(R.string.schedule_accept));

            view.setOnClickListener(v -> {
                CalendarEchoRequest request = new CalendarEchoRequest(new BaseApiRequestCallback<HttpResponse>() {
                    @Override
                    public void onSuccess(ApiData<HttpResponse> data) {
                        ToastUtils.ss(BaseApplication.getApplication().getResources().getString(R.string.operate_success));
                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {

                    }
                });
                request.scheduleId = String.valueOf(msg.scheduleCardInfo.getScheduleId());
                request.echoType = String.valueOf(Constants.TYPE_SCHEDULE_ECHO_ACCEPT);
                request.msgId = String.valueOf(msg.getMid());
                HttpExecutor.execute(request);
            });
        }
    }

    @BindingAdapter("actionNegative")
    public static void bindScheduleNegativeAction(TextView view, MessageForSystemCard msg) {
        String userId = HiKernel.getHikernel().getAccount().getUserId();
        //服务端数据判断可见
        if (msg == null || msg.scheduleCardInfo == null || !msg.scheduleCardInfo.getShowButton()) {
            view.setVisibility(View.GONE);
            return;
        }
        //业务判断可见
        if (msg.getCardTitle() == null
                || msg.getCardTitle().getStyle() == MessageConstants.SYSTEM_CARD_CONTENT_DELETED
                || msg.scheduleCardInfo.getDeleted() == Constants.SCHEDULE_DELETE) {
            view.setVisibility(View.GONE);
            return;
        }
        GroupService groupService = ServiceManager.getInstance().getGroupService();
        if (!groupService.isInGroups(msg.scheduleCardInfo.getGroupIds())
                && (msg.scheduleCardInfo.getNewUserIds() == null || !msg.scheduleCardInfo.getNewUserIds().contains(userId))) {
            view.setVisibility(View.GONE);
        } else if (!LList.isEmpty(msg.scheduleCardInfo.getRejectIds()) && msg.scheduleCardInfo.getRejectIds().contains(userId)) {
            //表示我已经拒绝 展示拒绝按钮
            view.setVisibility(View.VISIBLE);
            view.setOnClickListener(null);
            view.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_ED6966));
            view.setBackgroundResource(R.drawable.chat_bg_schedule_card_button_active);
            view.setText(BaseApplication.getApplication().getResources().getString(R.string.schedule_refuse_ed));
        } else {
            //没有拒绝
            view.setVisibility(View.VISIBLE);
            view.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_282833));
            view.setBackgroundResource(R.drawable.chat_bg_schedule_card_button_default);
            view.setText(BaseApplication.getApplication().getResources().getString(R.string.schedule_refuse));

            view.setOnClickListener(v -> {
                CalendarEchoRequest request = new CalendarEchoRequest(new BaseApiRequestCallback<HttpResponse>() {
                    @Override
                    public void onSuccess(ApiData<HttpResponse> data) {
                        ToastUtils.ss(BaseApplication.getApplication().getResources().getString(R.string.operate_success));
                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {

                    }
                });
                request.scheduleId = String.valueOf(msg.scheduleCardInfo.getScheduleId());
                request.echoType = String.valueOf(Constants.TYPE_SCHEDULE_ECHO_REFUSE);
                request.msgId = String.valueOf(msg.getMid());
                HttpExecutor.execute(request);
            });
        }
    }

    @BindingAdapter({"systemRichButton", "position"})
    public static void setRichButton(CommonTextView textView, List<MessageForSystemCard.SystemCardRichButton> buttons, int position) {
        if (LList.isEmpty(buttons) || buttons.size() <= position) {
            textView.setVisibility(View.GONE);
            return;
        }
        textView.setVisibility(View.VISIBLE);
        MessageForSystemCard.SystemCardRichButton button = buttons.get(position);
        textView.setText(button.getText());
        textView.setTextColor(button.getColor());
    }

    @BindingAdapter("systemCardStatus")
    public static void setSystemCardTextStyle(TextView textStyle, int systemCardStatus) {
        if (systemCardStatus == MessageConstants.SYSTEM_CARD_CONTENT_DELETED) {
            textStyle.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG | Paint.ANTI_ALIAS_FLAG);
        } else {
            textStyle.getPaint().setFlags(Paint.ANTI_ALIAS_FLAG);
        }
    }

    @BindingAdapter({"itemBackground", "chatListener", "showReply"})
    public static void setMessageItemBackgroundWithEmojiOrReply(ViewGroup viewGroup, ChatMessage message, ChatItemListener chatListener, Boolean showReply) {
        if (message != null && message.getMediaType() == MessageConstants.MSG_VIDEO_MEETING_CARD) {
            viewGroup.setBackgroundResource(R.drawable.chat_bg_meeting_card);
        } else {
            if (showReply != null
                    && showReply
                    && message != null
                    && (!LList.isEmpty(message.getEmojiReplies())
                    || message.getMessageReply() != null
                    && message.getMessageReply().getReplyId() > 0)
            ) {
                if (message.isSendByMe()) {
                    viewGroup.setBackgroundResource(R.drawable.bg_corner_8_chat_message_to);
                } else {
                    viewGroup.setBackgroundResource(R.drawable.chat_bg_message_from_text);
                }
                Context context = viewGroup.getContext();
                ViewKt.setPadding(viewGroup, QMUIDisplayHelper.dp2px(context, 12));
            } else {
                viewGroup.setBackground(null);
                viewGroup.setPadding(0, 0, 0, 0);
            }
        }

        viewGroup.setOnLongClickListener(v -> {
            if (message instanceof MessageForRedEnvelope
                    || message instanceof MessageForHint
                    || message instanceof MessageForLinkCall) {
                return true;
            }
            chatListener.onMessageLongClick(v, message);
            return true;
        });
    }

    @BindingAdapter({"senderName", "withReply", "showReply"})
    public static void setReplyName(TextView textView, CharSequence senderName, String senderId, boolean showReply) {
        if (showReply) {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(senderId);
            if (contact != null) {
                textView.setText(textView.getResources().getString(R.string.chat_some_one_reply_to_other, senderName, contact.getChatShowName()));
            } else {
                textView.setText(senderName);
            }
        } else {
            textView.setText(senderName);
        }
    }

    @BindingAdapter("replyTime")
    public static void setReplyTime(TextView textView, long replyTime) {
        textView.setText(LDate.getTimeAgo4(replyTime));
    }

    @BindingAdapter({"itemBg", "showBg"})
    public static void setMessageLink(ViewGroup parentView, Drawable itemBg, boolean showBg) {
        if (showBg) {
            parentView.setBackground(itemBg);
        }
    }

    @BindingAdapter({"urlLink"})
    public static void setMessageLink(ViewGroup parentView, MessageForLink message) {
        if (message == null || TextUtils.isEmpty(message.getUrl())) {
            parentView.setVisibility(View.GONE);
        } else {
            parentView.setVisibility(View.VISIBLE);
            String title = message.getTitle();
            String description = message.getDescription();
            if (TextUtils.isEmpty(title)) {
                title = message.getUrl();
            }
            if (TextUtils.isEmpty(description)) {
                description = message.getUrl();
            }

            ((TextView) parentView.findViewById(R.id.tv_url_parse_title)).setText(title);
            ((SimpleDraweeView) parentView.findViewById(R.id.sdv_url_parse)).setImageURI(message.getIcon());
            ((TextView) parentView.findViewById(R.id.tv_url_parse_description)).setText(description);
        }
    }

    @BindingAdapter({"emojiList", "itemListener", "showReply"})
    public static void setMessageEmojiReply(RecyclerView recyclerView, ChatMessage message, ChatItemListener listener, Boolean showReply) {
        if (showReply != null && showReply && message != null && LList.isNotEmpty(message.getEmojiReplies())) {
            recyclerView.setVisibility(View.VISIBLE);
            if (recyclerView.getLayoutManager() == null) {
                EmojiReplyFlexLayoutManager manager = new EmojiReplyFlexLayoutManager(recyclerView.getContext());
                recyclerView.setLayoutManager(manager);
                recyclerView.setItemAnimator(null);
            }

            if (recyclerView.getAdapter() == null) {
                EmojiReplyAdapter adapter = new EmojiReplyAdapter(recyclerView.getContext(), listener, message);
                adapter.setHasStableIds(true);
                recyclerView.setAdapter(adapter);
                adapter.submitList(message.getEmojiReplies());
            } else {
                EmojiReplyAdapter adapter = (EmojiReplyAdapter) recyclerView.getAdapter();
                adapter.setMessage(message);
                boolean sameList = adapter.getCurrentList().equals(message.getEmojiReplies());
                adapter.submitList(message.getEmojiReplies());
                if (sameList) {
                    adapter.notifyDataSetChanged();
                }
            }

        } else {
            recyclerView.setAdapter(null);
            recyclerView.setVisibility(View.GONE);
        }
    }

    @BindingAdapter("showImageGif")
    public static void showImageGif(SimpleDraweeView view, String url) {
        innerShowMsgPic(view, url);
    }

    @BindingAdapter({"messageInfo", "chatType"})
    public static void setMessageBottom(ViewGroup viewGroup, ChatMessage msg, int chatType) {
        ImageView imageView = viewGroup.findViewById(R.id.iv_reply_count);
        TextView textView = viewGroup.findViewById(R.id.tv_reply_count);
        TextView markTextView = viewGroup.findViewById(R.id.tv_mark);
        if (imageView.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ((ViewGroup.MarginLayoutParams) imageView.getLayoutParams()).topMargin = ThemeUtils.useNewTheme ? QMUIDisplayHelper.dpToPx(1.5f) : 0;
        }
        if (msg != null) {
            if (msg.getReplyCount() > 0) {
                imageView.setVisibility(View.VISIBLE);
                textView.setVisibility(View.VISIBLE);
                textView.setText(textView.getContext().getResources().getString(R.string.chat_reply_count, msg.getReplyCount()));
                textView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        long topId = msg.getMessageReply() == null ? 0 : msg.getMessageReply().getTopId();
                        if (chatType == MessageConstants.MSG_SINGLE_CHAT) { //单聊
                            SingleChatReplyDetailActivity.start(textView.getContext(), msg.getChatId(), msg.getMid(), topId <= 0 ? msg.getMid() : topId);
                        } else { //群聊
                            GroupChatReplyDetailActivity.start(textView.getContext(), msg.getChatId(), msg.getMid(), topId <= 0 ? msg.getMid() : topId);
                        }
                    }
                });
            } else {
                imageView.setVisibility(View.GONE);
                textView.setVisibility(View.GONE);
            }
            if (StringUtils.isNotEmpty(msg.getMarkUserId())) {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(msg.getMarkUserId());
                String name;
                if (contact != null) {
                    name = contact.getChatShowName();
                } else {
                    name = viewGroup.getContext().getResources().getString(R.string.chat_contact_empty);
                }
                markTextView.setVisibility(View.VISIBLE);
                markTextView.setText(viewGroup.getContext().getResources().getString(R.string.chat_mark_tips, name));
            } else {
                markTextView.setVisibility(View.GONE);
            }
            if (textView.getVisibility() == View.GONE && markTextView.getVisibility() == View.GONE) {
                viewGroup.setVisibility(View.GONE);
            } else {
                viewGroup.setVisibility(View.VISIBLE);
            }
        } else {
            imageView.setVisibility(View.GONE);
            textView.setVisibility(View.GONE);
            markTextView.setVisibility(View.GONE);
            viewGroup.setVisibility(View.GONE);
        }
    }

    @BindingAdapter(value = {"showRedEnvelopeType", "showRedEnvelopeMoney", "showRedEnvelopeCount"}, requireAll = false)
    public static void showRedEnvelopeMoney(TextView textView, int type, String money, String count) {
        double newM;
        if (type == MessageConstants.MSG_RED_ENVELOPE_AVERAGE) {
            newM = NumberUtils.toDouble(money) * NumberUtils.toInt(count);
        } else {
            newM = NumberUtils.toDouble(money);
        }
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        String format = decimalFormat.format(newM);
        SpannableString ss = new SpannableString(format);
        textView.setText(ss);
    }

    @BindingAdapter(value = {"redEnvelopeType", "redEnvelopeMoney",
            "redEnvelopeCount", "redEnvelopeGroupCount"}, requireAll = false)
    public static void redEnvelopeEnable(TextView textView, int type, String money, String count, int groupCount) {
        int fen = (int) (NumberUtils.toDouble(money) * 100);
        boolean enable = fen > 0;
        if (SingleRedEnvelopeFragment.ENABLE_MAX_SINGLE_RED_ENVELOPE) {
            enable &= fen <= SingleRedEnvelopeFragment.MAX_SINGLE_RED_ENVELOPE * 100;
        }
        if (type == MessageConstants.MSG_RED_ENVELOPE_NORMAL) {
            textView.setEnabled(enable);
        } else if (type == MessageConstants.MSG_RED_ENVELOPE_AVERAGE) {
            boolean coun = NumberUtils.String2Int(count) > 0
                    && NumberUtils.String2Int(count) <= groupCount
                    && NumberUtils.String2Int(count) <= GroupRedEnvelopeFragment.MAX_RED_ENVELOPE;

            if (SingleRedEnvelopeFragment.ENABLE_MAX_SINGLE_RED_ENVELOPE) {
                double sum = fen * NumberUtils.String2Int(count) / 100.0;
                textView.setEnabled(enable && coun && sum <= SingleRedEnvelopeFragment.MAX_SINGLE_RED_ENVELOPE);
            } else {
                textView.setEnabled(enable && coun);
            }
        } else if (type == MessageConstants.MSG_RED_ENVELOPE_LUCK) {
            boolean coun = NumberUtils.String2Int(count) > 0
                    && NumberUtils.String2Int(count) <= groupCount
                    && NumberUtils.String2Int(count) <= GroupRedEnvelopeFragment.MAX_RED_ENVELOPE;

            boolean ava = (fen * 1.0f / NumberUtils.String2Int(count)) >= 1;
            textView.setEnabled(enable && coun && ava);
        }
    }

    @BindingAdapter("userInfoId")
    public static void setGroupAttendanceWorkTypeItemTimes(LinearLayout linearLayout, String userInfoId) {
        linearLayout.setOnClickListener(v ->
                OrganizationPageRouter.jumpToUserInfoActivity(linearLayout.getContext(), userInfoId)
        );
    }

    @BindingAdapter("markMessage")
    public static void setMarkMessageTips(TextView textView, String userId) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
        if (contact != null) {
            textView.setText(textView.getResources().getString(R.string.chat_item_mark_message_tips, ContactUtils.getDisplayNameByContact(contact)));
        } else {
            textView.setVisibility(View.GONE);
        }
    }

    @BindingAdapter({"setEmotionText", "textSize"})
    public static void showEmotionText(MLinkEmotionTextView linkEmotionTextView, String text, int textSize) {
        if (StringUtils.isNotEmpty(text)) {
            linkEmotionTextView.setEmotionText(text, textSize);
        }
    }

    @BindingAdapter("picTagVisible")
    public static void setPicTagVisible(View view, ChatMessage chatMessage) {
        if (!(chatMessage instanceof MessageForPic)) {
            return;
        }
        MessageForPic messageForPic = (MessageForPic) chatMessage;

        if (messageForPic.getOrigin() != null) {
            boolean imageIsLongImage = BHImageHelper.isLongImage(messageForPic.getOrigin().width, messageForPic.getOrigin().height);
            view.setVisibility(imageIsLongImage ? View.VISIBLE : View.GONE);
        }
    }

    @BindingAdapter("taskCardTaskInfo")
    public static void setTaskCardTaskInfo(TextView textView, MessageForTaskCommentCard.TaskCommentCardTaskInfo taskInfo) {
        if (taskInfo == null) {
            return;
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(textView.getContext().getResources().getString(R.string.chat_creator));
        stringBuilder.append("：");
        if (StringUtils.isNotEmpty(taskInfo.getCreatorId())) {
            Contact contactById = ServiceManager.getInstance().getContactService().getContactById(taskInfo.getCreatorId());
            if (contactById != null) {
                stringBuilder.append(contactById.getUserName());
            } else {
                stringBuilder.append(textView.getContext().getResources().getString(R.string.nothing));
            }
        } else {
            stringBuilder.append(textView.getContext().getResources().getString(R.string.nothing));
        }
        stringBuilder.append("    ");
        stringBuilder.append(textView.getContext().getResources().getString(R.string.executor));
        stringBuilder.append("：");
        if (StringUtils.isNotEmpty(taskInfo.getCreatorId())) {
            Contact contactById = ServiceManager.getInstance().getContactService().getContactById(taskInfo.getExecutorId());
            if (contactById != null) {
                stringBuilder.append(contactById.getUserName());
            } else {
                stringBuilder.append(textView.getContext().getResources().getString(R.string.nothing));
            }
        } else {
            stringBuilder.append(textView.getContext().getResources().getString(R.string.nothing));
        }
        textView.setText(stringBuilder.toString());
    }

    @BindingAdapter({"bindMsgId", "timeAnnotationMessage"})
    public static void setTimeAnnotation(TextView view, long currentMid, ChatMessage timeAnnotationMessage) {
        if (timeAnnotationMessage == null || currentMid != timeAnnotationMessage.getMid()) {
            view.setText(null);
            view.setVisibility(View.GONE);
        } else {
            view.setText(formatTime(timeAnnotationMessage.getTime()));
            view.setVisibility(View.VISIBLE);
        }
    }

    private static String formatTime(long time) {
        if (LDate.isToday(time)) {
            return LDate.getDate(time, "HH:mm:ss");
        }
        if (LDate.isSameYear(time)) {
            return LDate.getDate(time, "M月d日  HH:mm:ss");
        }
        return LDate.getDate(time, "yyyy年M月d日  HH:mm:ss");
    }

    @BindingAdapter({"setTaskComment", "textSize", "messageType", "setHintCallback"})
    public static void setTaskComment(CommonIconTextView textView, List<MessageForTaskCommentCard.TaskCommentCardCommentText> commentTexts, int textSize, int messageType, ChatItemListener listener) {
        if (LList.isNotEmpty(commentTexts)) {
            MessageForTaskCommentCard.TaskCommentCardCommentText commentInfo = commentTexts.get(0);
            MessageForSystemCard.SystemCardRichText commentRichText = commentInfo.getContent();
            if (commentRichText != null) {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(commentInfo.getUserId());
                textView.setEmotionText(
                        contact != null && StringUtils.isNotEmpty(contact.getUserName())
                                ? String.format("%s：%s", contact.getUserName(), commentRichText.getText())
                                : commentRichText.getText(),
                        textSize,
                        textSize,
                        commentRichText.getAgreements(),
                        messageType,
                        listener
                );
            }
        }
    }

    @BindingAdapter({"transText", "chatTransTextListener"})
    public static void setChatTransTextListener(View view, ChatMessage chatMessage, ChatItemListener chatItemListener) {
        view.setOnLongClickListener(v -> {
            chatItemListener.copyAudioTransText(view, chatMessage);
            return true;
        });
    }

    @BindingAdapter({"setAudioAnimation", "isMe"})
    public static void setAudioAnimation(ImageView imageView, MessageForAudio messageForAudio, boolean isMe) {
        Object wrapper = imageView.getTag();
        if (wrapper instanceof AudioPlayWrapper) {
            AudioPlayWrapper audioPlayWrapper = (AudioPlayWrapper) wrapper;
            if (audioPlayWrapper.isPlay(messageForAudio)) {
                if (isMe) {
                    imageView.setBackground(imageView.getContext().getResources().getDrawable(R.drawable.chat_bg_audio_play_send));
                } else {
                    imageView.setBackground(imageView.getContext().getResources().getDrawable(R.drawable.chat_bg_audio_play_send));
                }
                AnimationDrawable animationDrawable = (AnimationDrawable) imageView.getBackground();
                animationDrawable.start();
            } else {
                Drawable drawable = imageView.getBackground();
                if (drawable instanceof AnimationDrawable) {
                    ((AnimationDrawable) drawable).stop();
                }
                if (isMe) {
                    imageView.setBackground(imageView.getContext().getResources().getDrawable(R.mipmap.chat_icon_audio_play_to_3));
                } else {
                    imageView.setBackground(imageView.getContext().getResources().getDrawable(R.mipmap.chat_icon_audio_play_from_3));
                }

            }
        }
    }

    @BindingAdapter({"showFavoriteHighLight", "favorId", "callback"})
    public static void showFavoriteHighLight(ViewGroup viewGroup, String showFavoriteHighLightId, String favorId, HighLightCallback callback) {
        if (TextUtils.equals(showFavoriteHighLightId, favorId) && viewGroup.getTag(R.id.showLight) == null) {
            viewGroup.setTag(R.id.showLight, showFavoriteHighLightId);
            ValueAnimator animator = new ValueAnimator();
            animator.setFloatValues(0f, 1f);
            animator.setDuration(2000);
            animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    float value = (float) animation.getAnimatedValue();
                    int color = ChatUtils.computeColor(0xFFF5F5F7, 0xffffffff, value);
                    viewGroup.getBackground().setColorFilter(color, PorterDuff.Mode.SRC);
                    if (value >= 1 && callback != null) {
                        callback.highLightOver();
                        viewGroup.setBackgroundResource(R.color.app_white);
                        viewGroup.setTag(R.id.showLight, null);
                    }
                }
            });
            animator.start();
        }
    }

    @BindingAdapter("groupOwner")
    public static void setGroupOwner(TextView textView, String groupOwner) {
        boolean isEmptyOwner = TextUtils.isEmpty(groupOwner);
        textView.setVisibility(isEmptyOwner ? View.GONE : View.VISIBLE);
        if (!isEmptyOwner) {
            textView.setText(String.format(textView.getResources().getString(R.string.chat_department_owner), groupOwner));
        }
    }

    @BindingAdapter({"message", "groupCache"})
    public static void setContactNameWithGroupName(TextView textView, ChatMessage message, GroupCacheInfo groupCache) {
        if (message == null) {
            return;
        }
        String senderName;
        if (message.getSender().getSenderType() == ChatMessage.SENDER_TYPE_ROBOT) {
            senderName = groupCache.getGroupRobot(message.getSender().getSenderId());
            if (StringUtils.isEmpty(senderName)) {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(message.getSender().getSenderId());
                senderName = contact == null ? "" : ContactUtils.getDisplayNameByContact(contact);
            }
        } else {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(message.getSender().getSenderId());
            senderName = contact == null ? "" : ContactUtils.getDisplayNameByContact(contact);
        }

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(senderName);

        String groupName = groupCache.getGroupDisplayName(message.getChatId());
        if (!TextUtils.isEmpty(groupName)) {
            stringBuilder.append(" | ");
            stringBuilder.append(groupName);
        }
        textView.setText(stringBuilder.toString());
    }

    @BindingAdapter({"bean", "groupCache"})
    public static void setFavoriteSharedMessageSourceContactName(TextView textView, FavoriteInterface bean, GroupCacheInfo groupCache) {
        if (TextUtils.equals(bean.getType(), FavoriteInterface.TYPE_USER)) {
            setContactNameWithGroupName(textView, bean.getMessage(), groupCache);
            return;
        }

        ChatMessage message = bean.getMessage();
        String senderName;
        if (message.getType() == MessageConstants.MSG_GROUP_CHAT) {
            senderName = groupCache.getGroupDisplayName(message.getChatId());
            if (StringUtils.isEmpty(senderName)) {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(message.getSender().getSenderId());
                senderName = contact == null ? "" : ContactUtils.getDisplayNameByContact(contact);
            }
        } else {
            if (message.getSender().getSenderType() == ChatMessage.SENDER_TYPE_ROBOT) {
                senderName = groupCache.getGroupRobot(message.getSender().getSenderId());
            } else {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(message.getSender().getSenderId());
                senderName = contact == null ? "" : ContactUtils.getDisplayNameByContact(contact);
            }
        }
        Log.i("BossHiTag", "ChatBindingAdapter -> setContactNameWithGroupName " + senderName + " " + message.getChatId() + " " + message.getType());
        textView.setText(senderName);
    }

    @BindingAdapter({"chatGroupBackground", "viewSelected"})
    public static void setChatGroupBackground(View view, int index, boolean selected) {
        view.setBackground(ChatGroupDataManager.getInstance().getBackGround(index, selected));
    }

    @BindingAdapter({"chatGroupCenterTextColor", "viewSelected"})
    public static void setChatGroupCenterTextColor(TextView textView, int index, boolean selected) {
        textView.setTextColor(selected ? Color.WHITE : ChatGroupDataManager.getInstance().getNormalTextColor(index));
    }

    @BindingAdapter("chatGroupTextStyleBlod")
    public static void setChatGroupTextStyleBlod(TextView textView, boolean isBlod) {
        textView.getPaint().setFakeBoldText(isBlod);
    }

    @BindingAdapter("indexText")
    public static void setChatGroupInfoText(TextView textView, String text) {
        SpannableString spannableString = new SpannableString(text);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#0D0D1A")), 0, 2, Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
        textView.setText(spannableString);
    }

    @BindingAdapter("resId")
    public static void setDrawableResId(ImageView imageView, int resId) {
        if (resId > 0) {
            imageView.setImageResource(resId);
        }
    }

    @BindingAdapter("emojiImage")
    public static void emojiImage(ImageView imageView, EmotionItem emotionItem) {
        if (emotionItem == null) {
            return;
        }
        if (emotionItem.getDrawableId() > 0) {
            imageView.setImageResource(emotionItem.getDrawableId());
        } else {
            Glide.with(imageView.getContext())
                    .load(emotionItem.getUri())
                    .into(imageView);
        }
    }

    private static void innerShowMsgPic(SimpleDraweeView view, String imageUrl) {
        if (TextUtils.isEmpty(imageUrl)) {
            return;
        } else {
            Object tag = view.getTag();
            if (tag instanceof String) {
                String tagValue = (String) tag;
                if (TextUtils.equals(tagValue, imageUrl)) {
                    return;
                }
            }
            view.setTag(imageUrl);
        }

        // 加载前清空controller,避免isGifAsync异步执行过程中因为item复用导致旧的图片先被展示出来的问题
        view.setController(null);

        ImageUtils.isGifAsync(view, imageUrl, isGif -> {
            if (isGif) {
                Glide.with(view)
                        .asGif()
                        .load(imageUrl)
                        .into(view);
            } else {
                loadImgByFresco(view, imageUrl);
            }
        });
    }

    private static void loadImgByFresco(SimpleDraweeView view, String imageUrl) {
        DraweeController controller = Fresco.newDraweeControllerBuilder()
                .setUri(Uri.parse(imageUrl))
                .setAutoPlayAnimations(true)//设置为true将循环播放Gif动画
                .setOldController(view.getController())
                .setContentDescription(imageUrl)
                .build();
        view.setController(controller);
    }

    @BindingAdapter({"chatListener", "msgContentClickListener", "chatMessage", "enable", "replicable", "msgContentWidth", "hypertextList"})
    public static void messageCardContent(LinearLayout container, ChatMessageItemCallback listener, MsgContentClickListener msgContentClickListener, MessageForExtensionCard message, boolean enable, boolean replicable, int msgContentWidth, List<Hypertext> hypertexts) {
        TLog.info("EXTENSION_CARD","ChatBindingAdapter -> messageCardContent ${EXTENSION_CARD}");
        if (message == null) {
            container.removeAllViews();
            return;
        }
        if (!message.isStreamMessage()) {
            TLog.info("EXTENSION_CARD","ChatBindingAdapter -> messageCardContent not stream "+message.getMid()+"  "+message.isStreamMessage());

            container.removeAllViews();
            PopChatLongClickBinding binding = PopChatLongClickBinding.inflate(LayoutInflater.from(container.getContext()));
            binding.rvOption.setLayoutManager(new LinearLayoutManager(container.getContext()));
            generateExtensionCardView(container, listener, msgContentClickListener, message, enable, replicable, binding, msgContentWidth, hypertexts);
            return;
        }
        // 流式消息增量对比和更新
        MessageForExtensionCard.ExtensionCardInfo info = message.getCardInfo();
        if (info == null || info.getModules() == null) {
            TLog.info("EXTENSION_CARD","ChatBindingAdapter -> messageCardContent info null "+message.getMid());
            container.removeAllViews();
            return;
        }
        TLog.info("EXTENSION_CARD","ChatBindingAdapter -> messageCardContent 更新内容 "+message.getMid());

        List<Module> modules = info.getModules();
        int i = 0;
        while (i < modules.size()) {
            Module module = modules.get(i);
            String tag = module.getTag()+"";
            View child = i < container.getChildCount() ? container.getChildAt(i) : null;
            if (child != null && tag.equals(child.getTag())) {
                // 类型相同，直接updateData
                if (child instanceof com.twl.hi.chat.messagecard.module.IModuleView) {
                    ((com.twl.hi.chat.messagecard.module.IModuleView) child).updateData(module);
                }
            } else {
                // 类型不同，移除旧的，插入新的
                PopChatLongClickBinding binding = PopChatLongClickBinding.inflate(LayoutInflater.from(container.getContext()));
                binding.rvOption.setLayoutManager(new LinearLayoutManager(container.getContext()));
                View newView = ModuleViewFactory.createModuleView(
                        new CardViewConfig.Builder()
                                .setContext(container.getContext())
                                .setMsgId(message.getMid())
                                .setEnable(enable)
                                .setReplicable(replicable)
                                .setGlobalWidth(msgContentWidth - QMUIDisplayHelper.dpToPx(24))
                                .setDataBinding(binding)
                                .setExtensionCardMessage(message)
                                .setOnElementOptListener(ExtensionCardHelper.getElementOptListener(container, listener, msgContentClickListener, message, info))
                                .setOnTextSelectionLifecycleCallback(new OnTextSelectionLifecycleCallback() {
                                    @Override
                                    public void onUpDate(TextSelection textSelection) {
                                        if (listener != null) {
                                            listener.onTextSelectionUpDate(textSelection, message, binding, container.getChildAt(container.getChildCount() - 1));
                                        }
                                    }
                                    @Override
                                    public void onShow(TextSelection textSelection) {
                                        if (listener != null) {
                                            listener.onTextSelectionShow(textSelection, message, binding, false);
                                        }
                                    }
                                })
                                .setHypertexts(hypertexts == null ? message.getHypertexts() : hypertexts)
                                .build(),
                        module);
                if (child != null) {
                    container.removeViewAt(i);
                }
                container.addView(newView, i);
            }
            i++;
        }
        // 移除多余的view
        while (container.getChildCount() > modules.size()) {
            container.removeViewAt(container.getChildCount() - 1);
        }
    }

    private static void generateExtensionCardView(LinearLayout container,
                                                  ChatMessageItemCallback listener,
                                                  MsgContentClickListener msgContentClickListener,
                                                  MessageForExtensionCard message,
                                                  boolean enable, boolean replicable,
                                                  PopChatLongClickBinding binding,
                                                  int msgContentWidth,
                                                  List<Hypertext> hypertexts
    ) {
        MessageForExtensionCard.ExtensionCardInfo info = message.getCardInfo();
        if (info != null && info.getModules() != null) {
            List<Module> modules = info.getModules();
            for (Module moduleItem : modules) {
                if(hypertexts == null){
                    hypertexts = message.getHypertexts();
                }
                for (Hypertext hypertext : hypertexts) {
                    //消息卡片不显示链接icon，服务端不能将icon置空，只能端上自己处理
                    Class<Hypertext> hypertextClass = Hypertext.class;
                    try {
                        Field hideIcon = hypertextClass.getDeclaredField("hideIcon");
                        hideIcon.setAccessible(true);
                        hideIcon.set(hypertext, true);
                    } catch (Exception e) {
                        TLog.error(TAG, e, "generateExtensionCardView error");
                    }
                }
                View moduleView = ModuleViewFactory.createModuleView(
                        new CardViewConfig.Builder()
                                .setContext(container.getContext())
                                .setMsgId(message.getMid())
                                .setEnable(enable)
                                .setReplicable(replicable)
                                .setGlobalWidth(msgContentWidth - QMUIDisplayHelper.dpToPx(24))
                                .setDataBinding(binding)
                                .setExtensionCardMessage(message)
                                .setOnElementOptListener(ExtensionCardHelper.getElementOptListener(container, listener, msgContentClickListener, message, info))
                                .setOnTextSelectionLifecycleCallback(new OnTextSelectionLifecycleCallback() {

                                    @Override
                                    public void onUpDate(TextSelection textSelection) {
                                        if (listener != null) {
                                            listener.onTextSelectionUpDate(textSelection, message, binding, container.getChildAt(container.getChildCount() - 1));
                                        }
                                    }

                                    @Override
                                    public void onShow(TextSelection textSelection) {
                                        if (listener != null) {
                                            listener.onTextSelectionShow(textSelection, message, binding, false);
                                        }
                                    }
                                })
                                .setHypertexts(hypertexts)
                                .build(),
                        moduleItem);
                if (moduleView != null) {
                    if (container.getChildCount() == 0) {
                        //第一个子view，需要根据标题类型设置topMargin
                        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) moduleView.getLayoutParams();
                        CardHeader header = info.getHeader();
                        if (layoutParams != null) {
                            if (header == null || header.isColorHeader()) {
                                if (moduleItem instanceof ModuleForNote) {
                                    layoutParams.topMargin = QMUIDisplayHelper.dp2px(container.getContext(), 4);
                                } else {
                                    layoutParams.topMargin = QMUIDisplayHelper.dp2px(container.getContext(), 12);
                                }
                            } else {
                                layoutParams.topMargin = QMUIDisplayHelper.dp2px(container.getContext(), 0);
                            }
                        }
                    }
                    container.addView(moduleView);
                }
            }
        }
    }

    @BindingAdapter("messageCardTitle")
    public static void messageCardTitle(TextView textView, CardHeader cardHeader) {
        if (cardHeader == null) {
            textView.setVisibility(View.GONE);
            return;
        }
        textView.setVisibility(View.VISIBLE);
        GradientDrawable background = new GradientDrawable();
        int radius = QMUIDisplayHelper.dp2px(textView.getContext(), 8);
        background.setCornerRadii(new float[]{radius, radius, radius, radius, 0, 0, 0, 0});
        if (!TextUtils.isEmpty(cardHeader.getBgEndColor()) && !TextUtils.isEmpty(cardHeader.getBgStartColor())) {
            background.setColors(new int[]{Color.parseColor(cardHeader.getBgStartColor()), Color.parseColor(cardHeader.getBgEndColor())});
            background.setOrientation(GradientDrawable.Orientation.TL_BR);
        }
        background.setStroke(QMUIDisplayHelper.dp2px(textView.getContext(), 2), Color.TRANSPARENT);
        if (!TextUtils.isEmpty(cardHeader.getTextColor())) {
            textView.setTextColor(Color.parseColor(cardHeader.getTextColor()));
        }
        textView.setBackground(background);
        if (cardHeader.getType() == CardHeader.HEADER_TYPE_NORMAL) {
            textView.setText(cardHeader.getText());
        }

    }

    @BindingAdapter("circleAvatar")
    public static void circleAvatar(ImageView imageView, String src) {
        GlideApp.with(imageView)
                .load(src)
                .circleCrop()
                .into(imageView);
    }

    @BindingAdapter("mailSearchAvatar")
    public static void emailAvatar(ImageView view, MailSearchBean bean) {
        if (bean == null || TextUtils.isEmpty(bean.senderRealAddress)) {
            EmailBindingAdapter.setMailAvatar(view, null, "", "");
            return;
        }
        ExecutorFactory.execWorkTask(new Runnable() {
            @Override
            public void run() {
                String mailAddress = bean.senderRealAddress;
                Contact contact = ServiceManager.getInstance().getContactService().getContactByMailAddr(mailAddress);
                ExecutorFactory.execMainTask(new Runnable() {
                    @Override
                    public void run() {
                        if (contact == null) {
                            EmailBindingAdapter.setMailAvatar(view, null, bean.senderName, mailAddress);
                        } else {
                            EmailBindingAdapter.setMailContactAvatar(view, contact);
                        }
                    }
                });
            }
        });
    }

    @BindingAdapter({"searchCardInfo"})
    public static void setupCardInfo(ViewGroup parent, SearchCardBean.CardInfo cardInfo) {
        ExpandableTextView tvCardContent = parent.findViewById(R.id.tv_card_content);
        tvCardContent.bind(cardInfo);
        LinearLayout operationContainer = parent.findViewById(R.id.ll_actions);
        Spanned spanned = toMarkDown(parent.getContext(), cardInfo.content);
        tvCardContent.setContent(spanned);
        setupCardOperations(operationContainer, cardInfo.operation);
    }

    @BindingAdapter({"operations"})
    public static void setupCardOperations(LinearLayout operationContainer, List<SearchCardBean.CardInfo.Operation> operations) {
        Context context = operationContainer.getContext();
        operationContainer.removeAllViews();
        List<SearchCardBean.CardInfo.Operation.Info> serviceInfos = new ArrayList<>();
        List<SearchCardBean.CardInfo.Operation.Info> consultInfos = new ArrayList<>();
        List<LinearLayout> operationItems = operations.stream().map(operation -> {
            if (operation.info == null || operation.info.isEmpty()) {
                return null;
            }
            String name = operation.name;
            int type = operation.type;
            // item
            LinearLayout operationLl = new LinearLayout(context);
            operationLl.setOrientation(LinearLayout.HORIZONTAL);
            // name
            TextView operationNameTv = new TextView(context);
            operationNameTv.setText(String.format("%s：", name));
            operationNameTv.setTextColor(context.getColor(R.color.color_858A99));
            operationNameTv.setTextSize(14);
            operationNameTv.setGravity(Gravity.CENTER);
            operationNameTv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, QMUIDisplayHelper.dpToPx(36)));
            operationLl.addView(operationNameTv);
            // actions
            FlexboxLayout flexboxLayout = new FlexboxLayout(context);
            flexboxLayout.setFlexWrap(FlexWrap.WRAP);
            flexboxLayout.setShowDivider(SHOW_DIVIDER_MIDDLE);
            ShapeDrawable divider = new ShapeDrawable();
            divider.setIntrinsicWidth(QMUIDisplayHelper.dp2px(context, 8));
            divider.setIntrinsicHeight(QMUIDisplayHelper.dp2px(context, 12));
            divider.setAlpha(0);
            flexboxLayout.setDividerDrawable(divider);
            operation.info.stream().map(info -> {
                TextView textView = new TextView(context);
                textView.setTextSize(14);
                textView.setTextColor(context.getColor(R.color.color_424A57));
                textView.setText(info.name);
                textView.setLines(1);
                textView.setSingleLine(true);
                textView.setGravity(Gravity.CENTER_VERTICAL);
                textView.setEllipsize(TextUtils.TruncateAt.END);
                if (!operation.isConsult()) {
                    // 服务
                    textView.setPadding(QMUIDisplayHelper.dp2px(context, 16), QMUIDisplayHelper.dp2px(context, 6), QMUIDisplayHelper.dp2px(context, 16), QMUIDisplayHelper.dp2px(context, 6));
                    textView.setBackground(context.getDrawable(R.drawable.chat_bg_search_card_container));
                    textView.setOnClickListener(v -> {
                        Map<String, Object> params = new HashMap<>();
                        params.put("type", "service");
                        params.put("rank", serviceInfos.indexOf(info) + 1);
                        PointUtils.pointV4("search-card-click", params);
                        AppUtil.getDefaultUriRequest(context, info.link)
                                .putField(RouterBaseConstant.WEB_STYLE, Constants.WEB_STYLE_SHARE_URL)
                                .start();
                    });
                    serviceInfos.add(info);
                } else {
                    // 咨询
                    int size = QMUIDisplayHelper.dp2px(context, 24);
                    GlideApp.with(context)
                            .load(info.link)
                            .placeholder(R.drawable.ic_search_card_consult)
                            .transform(new CircleCrop())
                            .override(size, size)
                            .into(new CustomTarget<Drawable>() {
                                @Override
                                public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                                    textView.setCompoundDrawablesWithIntrinsicBounds(resource, null, null, null);
                                    textView.setCompoundDrawablePadding(QMUIDisplayHelper.dp2px(context, 4));
                                }

                                @Override
                                public void onLoadCleared(@Nullable Drawable placeholder) {
                                    textView.setCompoundDrawablesWithIntrinsicBounds(placeholder, null, null, null);
                                    textView.setCompoundDrawablePadding(QMUIDisplayHelper.dp2px(context, 4));
                                }
                            });
                    textView.setBackground(context.getDrawable(R.drawable.chat_bg_search_card_operation_consult));
                    textView.setPadding(QMUIDisplayHelper.dp2px(context, 10), QMUIDisplayHelper.dp2px(context, 6), QMUIDisplayHelper.dp2px(context, 10), QMUIDisplayHelper.dp2px(context, 6));
                    textView.setOnClickListener(v -> {
                        Map<String, Object> params = new HashMap<>();
                        params.put("type", "help");
                        params.put("rank", consultInfos.indexOf(info) + 1);
                        PointUtils.pointV4("search-card-click", params);
                        String chatId = info.id;
                        Bundle bundle = new Bundle();
                        if (info.isSingleChat()) {
                            ExecutorFactory.execWorkTask(() -> {
                                Contact contact = ServiceManager.getInstance().getContactService().queryContactFromDb(chatId);
                                ExecutorFactory.execMainTask(() -> {
                                    if (contact != null) {
                                        if (contact.getUserType() == 2) {
                                            // 系统用户进入会话
                                            bundle.putString(BundleConstants.BUNDLE_USER_ID, chatId);
                                            AppUtil.startUri(context, ChatPageRouter.SINGLE_CHAT_ACTIVITY, bundle);
                                        } else {
                                            // 联系人进入名片
                                            Bundle bundle1 = new Bundle();
                                            bundle1.putString(BundleConstants.BUNDLE_DATA_LONG, chatId);
                                            AppUtil.startUri(context, OrganizationPageRouter.USER_INFO_ACTIVITY, bundle1);
                                        }
                                    } else {
                                        ToastUtils.ss("无效的联系人");
                                    }
                                });
                            });
                        } else {
                            GroupCardDetailActivity.intentStart(context, chatId, true);
                        }
                    });
                    consultInfos.add(info);
                }
                return textView;
            }).forEachOrdered(flexboxLayout::addView);
            operationLl.addView(flexboxLayout);
            return operationLl;
        }).collect(Collectors.toList());
        IntStream.range(0, operationItems.size())
                .forEachOrdered(index -> {
                    LinearLayout linearLayout = operationItems.get(index);
                    if (linearLayout != null) {
                        operationContainer.addView(linearLayout);
                        if (index < operations.size() - 1) {
                            View view = new View(context);
                            view.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, QMUIDisplayHelper.dpToPx(12)));
                            operationContainer.addView(view);
                        }
                    }
                });
    }

}
