package com.twl.hi.chat;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import com.sankuai.waimai.router.Router;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.PageConstantsKt;
import com.twl.hi.basic.SubPageTransferActivity;
import com.twl.hi.basic.databinding.PopChatLongClickBinding;
import com.twl.hi.basic.dialog.DialogUtils;
import com.twl.hi.basic.model.TemporaryTokenBean;
import com.twl.hi.basic.model.WebViewBean;
import com.twl.hi.basic.util.Scene;
import com.twl.hi.chat.api.request.BotNplFeedBackRequest;
import com.twl.hi.chat.callback.ChatMessageItemCallback;
import com.twl.hi.chat.messagecard.utils.ExtensionCardHelper;
import com.twl.hi.chat.router.handler.custom.ImagePreviewHandler;
import com.twl.hi.chat.util.AudioPlayWrapper;
import com.twl.hi.chat.util.ChatPointUtils;
import com.twl.hi.chat.util.ChatRobotHelper;
import com.twl.hi.chat.util.F2AudioManager;
import com.twl.hi.chat.viewmodel.ChatBaseViewModel;
import com.twl.hi.export.audio.shorthand.IAudioShortHandService;
import com.twl.hi.export.audio.shorthand.router.AudioShortHandPageRouter;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.export.organization.router.OrganizationPageRouter;
import com.twl.hi.export.schedule.router.SchedulePageRouter;
import com.twl.hi.export.webview.WebViewPageRouter;
import com.twl.hi.export.workflow.router.WorkflowPagerRouter;
import com.twl.hi.foundation.api.base.BaseUpdateRequestCallback;
import com.twl.hi.foundation.base.fragment.ContentListRefreshFragment;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.GroupMember;
import com.twl.hi.foundation.model.message.*;
import com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard;
import com.twl.hi.foundation.utils.ContactUtils;
import com.twl.hi.router.base.RouterBaseConstant;
import com.twl.hi.viewer.DraggableImageViewerHelper;
import com.twl.hi.viewer.bean.ImageExtraIndexBean;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.utils.SettingBuilder;
import com.twl.utils.StringUtils;
import com.twl.utils.URLUtils;
import com.twl.utils.network.NetworkHelper;
import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import hi.kernel.HiKernel;
import kotlin.Pair;
import lib.twl.common.callback.OuterCallback;
import lib.twl.common.dialog.MyProgressDialog;
import lib.twl.common.ext.KotlinExtKt;
import lib.twl.common.util.*;
import lib.twl.common.util.selection.TextSelection;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static lib.twl.common.ext.ViewExtKt.getStatusBarsHeight;

/**
 * 聊天基类fragment
 *
 * @param <D> ViewDataBinding
 * @param <M> ChatBaseViewModel
 */
public abstract class ChatBaseFragment<D extends ViewDataBinding, M extends ChatBaseViewModel> extends ContentListRefreshFragment<D, M>
        implements ChatMessageItemCallback {

    private static final String TAG = "ChatBaseFragment";

    /**
     * 用于防止重复点击跳转消息的dialog
     */
    private MyProgressDialog myProgressDialog;
    private DialogUtils mSaveFileLocalDialogUtil;
    private DialogUtils mNoFileMsgSelectDialogUtil;
    protected boolean showTextStyle = false;

    protected AudioPlayWrapper mAudioPlayWrapper = new AudioPlayWrapper();

    @Override
    protected void initFragment() {
        super.initFragment();
        getViewModel().getGroupIdLiveData().observe(this, groupId -> {
            if (groupId == null) {
                return;
            }
            Bundle groupBundle = new Bundle();
            groupBundle.putString(BundleConstants.BUNDLE_CHAT_ID, groupId);
            AppUtil.startUri(activity, ChatPageRouter.GROUP_CHAT_ACTIVITY, groupBundle);
        });
        //订阅loading的状态
        getViewModel().getLoadingState().observe(this, loadingState -> {
            if (loadingState.isShowed()) {
                myProgressDialog = new MyProgressDialog(getContext());
                myProgressDialog.setCancelable(loadingState.isCancelable());
                myProgressDialog.show();
            } else {
                myProgressDialog.dismiss();
            }
        });
        getViewModel().getAllFileMsgsLiveData().observe(this, allFileMsg -> {
            if (!allFileMsg) {
                showDownloadFileMessageWarning();
            }
        });

        getViewModel().getHasDownloadingMsgs().observe(this, hasDownloading -> {
            if (hasDownloading) {
            }
        });
        getViewModel().getNoFileMsgSelectedLiveData().observe(this, noSelected -> {
            if (noSelected) {
                showNoSelectedFileMsgsWarning();
            }
        });
    }

    @Override
    public void onPause() {
        super.onPause();
        stopAudioPlay();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        F2AudioManager.getInstance().release();
    }

    /**
     * 停止音频播放
     */
    protected void stopAudioPlay() {
        F2AudioManager.getInstance().pause();
        mAudioPlayWrapper.setPlay(false);
    }

    private void showNoSelectedFileMsgsWarning() {
        if (mNoFileMsgSelectDialogUtil == null) {
            mNoFileMsgSelectDialogUtil = new DialogUtils.Builder(getContext())
                    .setTitle("只能保存图片/视频/文件到本地")
                    .setCanceledOnTouchOutside(true)
                    .setPositive("知道了")
                    .setPositiveListener(v -> mNoFileMsgSelectDialogUtil.dismiss())
                    .build();
        }
        mNoFileMsgSelectDialogUtil.show();
    }

    private void showDownloadFileMessageWarning() {
        if (mSaveFileLocalDialogUtil == null) {
            mSaveFileLocalDialogUtil = new DialogUtils.Builder(getContext())
                    .setTitle("只能保存图片/视频/文件到本地，其他类型的消息将不被保存")
                    .setPositive("确定")
                    .setCanceledOnTouchOutside(true)
                    .setPositiveListener(v -> {
                        getViewModel().startDownloadCheckMsgs(getViewModel().getSelectedMsgData(), true);
                        resetMultipleStatus();
                        mSaveFileLocalDialogUtil.dismiss();
                    })
                    .build();
        }
        mSaveFileLocalDialogUtil.show();
    }

    protected void resetMultipleStatus() {
    }

    @Override
    public void onTextSelectionShow(TextSelection textSelection, ChatMessage chatMessage, PopChatLongClickBinding binding, boolean richClose) {

    }

    @Override
    public void onTextSelectionUpDate(TextSelection textSelection, ChatMessage chatMessage, PopChatLongClickBinding binding, View anchor) {

    }

    public void onProtocol(String protocol) {
        onProtocol(protocol, null);
    }

    protected void onProtocol(MessageForSystemCard messageForSystemCard) {
        onProtocol(messageForSystemCard.getCardProtocol(), messageForSystemCard);
    }

    private void onProtocol(String protocol, MessageForSystemCard messageForSystemCard) {
        //bosshi://bosshi.app/openwith?type=calendarInfo&scheduleId=1014&scheduleType=2&operateDate=2020-09-09
        //bosshi://bosshi.app/openwith?type=applyInfo&applyId=123&applyType=1 查询假期
        //bosshi://bosshi.app/openwith?type=ceoTopic
        //bosshi://bosshi.app/openwith?type=workAbsence?date=2019-07-11
        //bosshi://bosshi.app/openwith?type=friendApplyList
        //bosshi://bosshi.app/openwith?type=qrScan
        //bosshi://bosshi.app/openwith?type=taskInfo&taskId=xxxxxx
        //bosshi://bosshi.app/openwith?type=shimo&url=https://shimo.weizhipin.com/docs/H6GkVcQ8G3qq8PR8
        //bosshi://bosshi.app/openwith?type=shining&shiningId=xxxxxx
        //bosshi://bosshi.app/openwith?type=webLogin&qrCode=bosshi-XXXXX-1001-XXXXXX
        //bosshi://bosshi.app/openwith?type=groupInfo&groupId=12345&qrCode=bosshi-XXXXX-XXXX-XXXXXX
        //bosshi://bosshi.app/openwith?type=thirdLogin&qrCode=bosshi-XXXXX-1001-XXXXXX
        //bosshi://bosshi.app/openwith?type=clock
        //bosshi://bosshi.app/openwith?type=taskGroupLink
        //bosshi://bosshi.app/openwith?type=calendarView&date=2021-08-01
        //bosshi://bosshi.app/openwith?type=meetingRoom&roomId=1adc5d0e22d1e58cwCyI//音视频会议
        if (TextUtils.isEmpty(protocol) && messageForSystemCard != null) {
            protocol = messageForSystemCard.getCardProtocol();
        }

        //是AppLink则处理并拦截
        Uri wholeUrl = Uri.parse(protocol);
        String miniAppUrl = wholeUrl.getQueryParameter("miniAppUrl");
        String appLink = "";
        if (!TextUtils.isEmpty(miniAppUrl)) {
            //如果是小程序，就把获取子页面路径拼接到打开小程序的AppLink后边
            List<String> finalContainer = new ArrayList<>(1);
            finalContainer.add(miniAppUrl);
            //path
            Optional.ofNullable(wholeUrl.getQueryParameter("path"))
                    .filter(it -> !it.isEmpty())
                    .ifPresent(it -> finalContainer.set(0, KotlinExtKt.appendUrlParams(finalContainer.get(0), new Pair<>("path", it))));
            //chatType
            Optional.ofNullable(wholeUrl.getQueryParameter("chatType"))
                    .filter(it -> !it.isEmpty())
                    .ifPresent(it -> finalContainer.set(0, KotlinExtKt.appendUrlParams(finalContainer.get(0), new Pair<>("chatType", it))));
            //chatId
            Optional.ofNullable(wholeUrl.getQueryParameter("chatId"))
                    .filter(it -> !it.isEmpty())
                    .ifPresent(it -> finalContainer.set(0, KotlinExtKt.appendUrlParams(finalContainer.get(0), new Pair<>("chatId", it))));
            //scene
            Optional.ofNullable(wholeUrl.getQueryParameter("scene"))
                    .filter(it -> !it.isEmpty())
                    .ifPresent(it -> finalContainer.set(0, KotlinExtKt.appendUrlParams(finalContainer.get(0), new Pair<>("scene", it))));
            appLink = finalContainer.get(0);
        } else {
            appLink = protocol;
        }
        AppUtil.getDefaultUriRequest(getContext(), appLink)
                .putField(RouterBaseConstant.OLD_LINK_MESSAGE, messageForSystemCard)
                .putField(RouterBaseConstant.OLD_CHAT_TYPE, getViewModel().getType())
                .start();
    }

    @Override
    public void onAgreementClick(String agreement) {
        if (agreement.startsWith(Constants.URI_SCHEME_BOSS_HI)) {
            Map<String, String> params = URLUtils.parseUrlParams(agreement);
            String type = params.get("type");
            if (!TextUtils.isEmpty(type)) {
                switch (type) {
                    case Constants.AGREEMENT_URL:
                        String urlString = params.get(Constants.PARAMS_URL);
                        if (!TextUtils.isEmpty(urlString)) {
                            clickAgreementUrl(urlString, params);
                        }
                        break;
                    case Constants.AGREEMENT_GROUP:
                        clickAgreementGroup(params);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    @Override
    public void onInlineLinkClick(View view, ChatMessage message, String url) {
        QMUIKeyboardHelper.hideKeyboard(activity);
        AppUtil.getDefaultUriRequest(activity, url)
                .putField(ImagePreviewHandler.IMAGE_PREVIEW_CALLBACK, new OuterCallback<String>() { //图片预览回调
                    @Override
                    public void onSuccess(String destination) {
                        ImageExtraIndexBean imageExtraIndexBean = new ImageExtraIndexBean(destination, message);
                        getViewModel().prepareForMediaView(imageExtraIndexBean);
                    }
                })
                .putField(RouterBaseConstant.WEB_STYLE, Constants.WEB_STYLE_SHARE_URL)
                .start();

    }

    protected void clickAgreementGroup(Map<String, String> params) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                String groupId = params.get("groupId");
                String qrCode = params.get("qrCode");
                if (StringUtils.isNotEmpty(groupId)) {
                    GroupMember groupMember = ServiceManager.getInstance().getDatabaseService().getGroupDao().findGroupMember(groupId, HiKernel.getHikernel().getAccount().getUserId());
                    if (groupMember != null) {
                        getViewModel().getGroupIdLiveData().postValue(groupId);
                    } else {
                        getViewModel().joinGroup(groupId, qrCode);
                    }
                } else {
                    getViewModel().joinGroup(groupId, qrCode);
                }
            }
        });
    }

    private void clickAgreementUrl(String urlString, Map<String, String> params) {
        int noTitle = 0; //0 表示默认title， 1表示不需要默认title
        int browser = 1; //1表示展示更多，0标识不展示
        try {
            noTitle = Integer.parseInt(params.get("noHead"));
        } catch (NumberFormatException formatException) {
            TLog.error(TAG, formatException.getMessage());
        }
        try {
            browser = Integer.parseInt(params.get("browser"));
        } catch (NumberFormatException formatException) {
            TLog.error(TAG, formatException.getMessage());
        }

        StringBuilder builder = new StringBuilder(urlString);
        Map<String, String> urlParams = URLUtils.parseUrlParams(urlString);
        if (urlParams.size() > 0) {
            builder.append("&barheight=");
        } else {
            builder.append("?barheight=");
        }
        builder.append(KotlinExtKt.getPxToDp(getStatusBarsHeight(activity)));
        TemporaryTokenBean temporaryTokenBean = new TemporaryTokenBean(builder.toString());
        temporaryTokenBean.setStyle(Constants.WEB_STYLE_SHARE_URL);
        if (noTitle == 1) {
            temporaryTokenBean.setStyle(Constants.WEB_STYLE_HAS_NO_TITLE_AND_TRANSLUCENT);
        } else {
            if (browser == 0) {
                temporaryTokenBean.setStyle(Constants.WEB_STYLE_HAS_NO_SHARE);
            }
        }
        temporaryTokenBean.setFinishActivity(false);
        String hiAppId = urlParams.get(MessageConstants.HI_APP_ID);
        if (!TextUtils.isEmpty(hiAppId) || TextUtils.equals(MessageConstants.KAN_ZHUN, params.get(MessageConstants.OC))) {
            getViewModel().getTemporaryToken(temporaryTokenBean, hiAppId);
        } else {
            getViewModel().setExternalScanResult(temporaryTokenBean);
        }
    }

    protected void jumpScheduleDetail(String scheduleId, String currDate, String msgId, boolean isNeedRequest) {
        if (activity instanceof FragmentActivity) {
            SchedulePageRouter.jumpScheduleDetailPage((FragmentActivity) activity, scheduleId, null, currDate, Constants.CREATE_FROM_CHAT_MESSAGE, msgId, isNeedRequest);
        }
    }

    @Override
    public void onRichButton(String protocol, long msgId) {
        Map<String, String> params = URLUtils.parseUrlParams(protocol);
        int inner = 0;
        if (!TextUtils.isEmpty(params.get("type"))) {
            if (TextUtils.equals(params.get("type"), MessageConstants.POST)) {
                try {
                    if (!TextUtils.isEmpty(params.get("inner"))) {
                        inner = Integer.parseInt(params.get("inner"));
                    }
                } catch (Exception e) {
                    TLog.error(TAG, e.getMessage());
                }
                String url = params.get("url");
                Map<String, String> urlParams = URLUtils.parseUrlParams(url);
                String hiAppId = urlParams.get(MessageConstants.HI_APP_ID);
                if (inner == 0) {
                    url = url + "&msgId=" + msgId;
                    getViewModel().getTemporaryTokenToRequest(hiAppId, url, inner);
                } else {
                    if (TextUtils.isEmpty(urlParams.get("msgId"))) {
                        urlParams.put("msgId", String.valueOf(msgId));
                    }
                    getViewModel().requestPost(URLUtils.getNoParamsUrl(url), inner, null, urlParams);
                }
            }
        }
    }

    protected void urlClick(String url) {
        Map<String, String> params = URLUtils.parseUrlParams(url);
        String hiAppId = params.get(MessageConstants.HI_APP_ID);
        TemporaryTokenBean temporaryTokenBean = new TemporaryTokenBean(url);
        temporaryTokenBean.setStyle(Constants.WEB_STYLE_SHARE_URL);
        if (!TextUtils.isEmpty(hiAppId)) {
            if (!NetworkHelper.INSTANCE.isAvailable()) {
                ToastUtils.failure("网络不给力，请重试");
                return;
            }
            getViewModel().getTemporaryToken(temporaryTokenBean, hiAppId);
        } else {
            getViewModel().setExternalScanResult(temporaryTokenBean);
        }
    }

    @Override
    public void onClickEnterVideoMeeting(MessageForVideoMeetingCard chatMessage) {

        IAudioShortHandService service = Router.getService(
                IAudioShortHandService.class,
                AudioShortHandPageRouter.AUDIO_SHORTHAND_SERVICE);
        if (service != null && service.getShortHanding()) {
            service.showShortHandTipDialog(getViewLifecycleOwner()).observe(getViewLifecycleOwner(), new Observer<Boolean>() {
                @Override
                public void onChanged(Boolean aBoolean) {
                    if (aBoolean) {
                        gotoClickEnterVideoMeeting(chatMessage);
                    }
                }
            });
        } else {
            gotoClickEnterVideoMeeting(chatMessage);
        }

    }

    private void gotoClickEnterVideoMeeting(MessageForVideoMeetingCard chatMessage) {
        if (chatMessage.getVideoMeetingInfo() != null
                && chatMessage.getVideoMeetingInfo().getButtons() != null
                && chatMessage.getVideoMeetingInfo().getButtons().size() > 0) {
            TimeDifferenceUtil.getInstance().start(TimeTag.VIDEO_MEETING_ENTER);
            onProtocol(chatMessage.getVideoMeetingInfo().getButtons().get(0).getProtocol());
        }
    }

    /**
     * 点击应用卡片
     */
    public void onAppCardClickInternal(MessageForAppCard messageForAppCard) {
        forwardToAppDetailsPage(messageForAppCard);
    }

    /**
     * 跳转到具体的应用界面
     * 转发到聊天打开应用卡片使用h5Url，
     * 工作台打开应用使用appUrl
     *
     * @param messageForAppCard 应用信息
     */
    private void forwardToAppDetailsPage(MessageForAppCard messageForAppCard) {
        MessageForAppCard.HiAppCardInfo appCardInfo = messageForAppCard.getHiAppCardInfo();
        //兼容低版本的的消息卡片
        ServiceManager.getInstance().getWorkbenchService().compatOpenWorkbenchPlugin(
                ((AppCompatActivity) getActivity()),
                appCardInfo.getAppIdStr(),
                String.valueOf(appCardInfo.getAppCode()),
                appCardInfo.getAppName(),
                getViewModel().getType() == MessageConstants.MSG_SINGLE_CHAT ?
                        Scene.SingleChatCard.INSTANCE.getCode() : Scene.GroupChatCard.INSTANCE.getCode(),
                String.valueOf(messageForAppCard.openChatId),
                String.valueOf(messageForAppCard.openChatType));
    }

    @Override
    public void onTextClick(View view, MessageForText messageForText) {

    }

    @Override
    public void onFileClick(View view, MessageForFile messageForFile) {
        getViewModel().onFileClick(activity, messageForFile, getCurrentPageSource());
    }

    @Override
    public void onStickerClick(View view, MessageForSticker messageForSticker) {

    }

    @Override
    public void onAudioClick(View view, MessageForAudio messageForAudio) {
        IAudioShortHandService service = Router.getService(
                IAudioShortHandService.class,
                AudioShortHandPageRouter.AUDIO_SHORTHAND_SERVICE);
        if (service != null && service.getShortHanding()) {
            ToastUtils.ss("AI速记正在录音，无法播放音频");
            return;
        }
        getViewModel().onPlayAudio(messageForAudio).observe(this, b -> {
            if (b) {
                View animationView = view.findViewById(R.id.animation_view);
                if (animationView instanceof ImageView) {
                    mAudioPlayWrapper.setMessageForAudio(messageForAudio, true, (ImageView) animationView, isInChatInputPage());
                }
            } else {
                mAudioPlayWrapper.setPlay(false);
            }
        });
    }

    @Override
    public void onUserCardClick(View view, MessageForUserCard messageForUserCard) {
        handleUserCardClick(messageForUserCard);
    }

    @Override
    public void onMsgShareCardClick(View view, MessageForChatShare messageForChatShare) {
        String title = messageForChatShare.getChatShareInfo().getTitle();
        long msgId = messageForChatShare.getMid();
        SubPageTransferActivity.jump(
                activity,
                ChatRecordFragment.class,
                ChatRecordFragment.getBundle(title, msgId, "", messageForChatShare.getSender().getSenderId(), getViewModel().getChatId(), getViewModel().getType())
        );
    }

    @Override
    public void onPicClick(View view, MessageForPic message) {
        ImageExtraIndexBean imageExtraIndexBean = new ImageExtraIndexBean("", message);
        getViewModel().prepareForMediaView(imageExtraIndexBean, DraggableImageViewerHelper.createImageDraggableParamsWithWHRadio(view));
    }

    @Override
    public void onVideoClick(View view, MessageForVideo message) {
        IAudioShortHandService service = Router.getService(
                IAudioShortHandService.class,
                AudioShortHandPageRouter.AUDIO_SHORTHAND_SERVICE);
        if (service != null && service.getShortHanding()) {
            ToastUtils.ss("AI速记正在录音，无法播放视频");
            return;
        }
        ImageExtraIndexBean imageExtraIndexBean = new ImageExtraIndexBean("", message);
        getViewModel().prepareForMediaView(imageExtraIndexBean, DraggableImageViewerHelper.createImageDraggableParamsWithWHRadio(view));
    }

    @Override
    public void onReplyThumbnailClick(View view, ChatMessage replyMessage, ChatMessage chatMessage) {
        if (replyMessage instanceof MessageForPic || replyMessage instanceof MessageForVideo) {
            ImageExtraIndexBean imageExtraIndexBean = new ImageExtraIndexBean("", replyMessage);
            imageExtraIndexBean.autoLoadMore = false;
            imageExtraIndexBean.disableAllMedia = true;
            getViewModel().prepareForMediaView(imageExtraIndexBean, DraggableImageViewerHelper.createImageDraggableParamsWithWHRadio(view));
        } else {
            onReplyContainerClick(view, replyMessage, chatMessage);
        }
    }

    @Override
    public void onRedEnvelopClick(View view, MessageForRedEnvelope messageForRedEnvelope) {

    }

    @Override
    public void onSystemCardClick(View view, MessageForSystemCard messageForSystemCard) {

    }

    @Override
    public void onLinkCallClick(View view, MessageForLinkCall messageForLinkCall) {

    }

    @Override
    public void onMessageLinkClick(MessageForLink messageForLink) {
        String url = messageForLink.getUrl();
        urlClick(url);
    }

    @Override
    public void onImageCardClick(MessageForImageCard messageForImageCard) {
        onProtocol(messageForImageCard.getImageCardInfo().getProtocol());
    }

    @Override
    public void onOnlineFileClick(MessageForOnlineFile chatMessage) {
        WebViewBean webViewBean = new WebViewBean();
        String stringBuilder = SettingBuilder.getInstance().getOnlineFileMidUrl() + "?redirectUrl=" + chatMessage.getFileInfo().getFileUrl();
        webViewBean.setUrl(stringBuilder);
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constants.DATA_WEB_BEAN, webViewBean);
        AppUtil.startUri(activity, WebViewPageRouter.WEB_VIEW_ACTIVITY, bundle);
    }

    @Override
    public void onTaskCommentCardClick(View view, MessageForTaskCommentCard messageForTaskCommentCard) {
        MessageForTaskCommentCard.TaskCommentCardTaskInfo taskInfo = messageForTaskCommentCard.getTaskInfo();
        if (!TextUtils.isEmpty(taskInfo.getTaskId())) {
            WorkflowPagerRouter.jumpToTaskDetailEditActivity(activity, taskInfo.getTaskId(),
                    String.valueOf(messageForTaskCommentCard.getMid()));
        }
    }

    @Override
    public void onAppCardClick(MessageForAppCard chatMessage) {
        onAppCardClickInternal(chatMessage);
    }

    @Override
    public void onGroupCardClick(View view, MessageForGroupCard messageForGroupCard) {
        GroupCardDetailActivity.intentStart(view.getContext(),
                messageForGroupCard.getGroupCard().getGroupId(),
                messageForGroupCard.getMid(),
                messageForGroupCard.getSender().getSenderId(),
                messageForGroupCard.getGroupCard().getGroupName(),
                messageForGroupCard.getGroupCard().getAvatar());
    }

    @Override
    public void onMessageExtensionCardClick(View view, MessageForExtensionCard messageForExtensionCard) {
        ExtensionCardHelper.handleMessageExtensionCardClick(getContext(), messageForExtensionCard);
    }

    @Override
    public void onExtensionCardImageClick(View view, String imgUrl, MessageForExtensionCard messageForExtensionCard) {
        ImageExtraIndexBean imageExtraIndexBean = new ImageExtraIndexBean(imgUrl, messageForExtensionCard);
        imageExtraIndexBean.autoLoadMore = false;
        getViewModel().prepareForMediaView(imageExtraIndexBean, DraggableImageViewerHelper.createImageDraggableParamsWithWHRadio(view));
    }

    @Override
    public void onClickMessage2SendMessage(String content) {
        TLog.info(TAG, "onClickMessage2SendMessage: " + content);
        getViewModel().sendMarkdownMessage(null,content, getViewModel().getChatId(),null,getViewModel().getType() ,null,0,null);
    }

    public void handleUserCardClick(MessageForUserCard messageForUserCard) {
        if (messageForUserCard == null) return;
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(messageForUserCard.getUserId());
        if (ContactUtils.isRobot(contact)) {
            ChatRobotHelper.handleRobotCardClick(activity, messageForUserCard.getUserId());
        } else {
            OrganizationPageRouter.jumpToUserInfoActivity(activity, messageForUserCard.getUserId());
        }
    }

    /**
     * 获取打开文件管理器的Intent
     * 某些手机需要不支持，需要兜底
     */
    protected Intent getOpenDocumentIntent(boolean isOrdinary) {
        Intent intent = null;
        if (isOrdinary) { //一般打开文件管理器
            intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
        } else { //部分机型用上边的打不开，这个。比如小米六
            intent = new Intent(Intent.ACTION_GET_CONTENT);
        }
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");//设置Mimetype，我这里是任意文件类型，任意后缀的可以这样写。
        intent.putExtra("android.content.extra.SHOW_ADVANCED", true);
        return intent;
    }

    @Override
    public void clickFeedback(ChatMessage chatMessage, int feedBackType) {
        ChatMessage.NlpFeedBack nlpFeedBack = chatMessage.getNlpFeedBack();
        if (nlpFeedBack == null || nlpFeedBack.getFeedBackType() != null) {
            return;
        }
        BotNplFeedBackRequest botNplFeedBackRequest = new BotNplFeedBackRequest(new BaseUpdateRequestCallback<HttpResponse>() {
            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                ServiceManager.getInstance().getMessageService().updateNlpFeedBack(chatMessage.getMid(), feedBackType);
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                super.onSuccess(data);
            }
        });
        botNplFeedBackRequest.feedBackType = feedBackType;
        botNplFeedBackRequest.nlpId = nlpFeedBack.getNlpId();
        botNplFeedBackRequest.msgId = chatMessage.getMid();
        HttpExecutor.execute(botNplFeedBackRequest);

        ChatPointUtils.pointBot(feedBackType, chatMessage.getMid());
    }

    protected boolean isInChatInputPage() {
        return false;
    }

    public String getCurrentPageSource() {
        return PageConstantsKt.COMMON_CHAT_PAGE;
    }
}
