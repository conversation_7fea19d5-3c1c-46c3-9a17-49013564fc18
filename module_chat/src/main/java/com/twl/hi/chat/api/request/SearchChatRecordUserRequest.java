package com.twl.hi.chat.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.chat.api.response.SearchChatRecordUserResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class SearchChatRecordUserRequest extends BaseApiRequest<SearchChatRecordUserResponse> {
    @Expose
    public String content;
    @Expose
    public int page = 1;
    @Expose
    public int size = 30;
    @Expose
    public boolean highlight;

    public SearchChatRecordUserRequest(BaseApiRequestCallback<SearchChatRecordUserResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_SEARCH_CHAT_RECORD_USER;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
