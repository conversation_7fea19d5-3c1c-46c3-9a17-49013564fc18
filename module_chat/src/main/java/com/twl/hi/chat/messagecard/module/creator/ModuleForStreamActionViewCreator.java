package com.twl.hi.chat.messagecard.module.creator;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.chat.R;
import com.twl.hi.chat.messagecard.config.CardViewConfig;
import com.twl.hi.chat.messagecard.module.IModuleView;
import com.twl.hi.chat.messagecard.module.StreamActionConstants;
import com.twl.hi.chat.messagecard.utils.ExtensionCardHelper;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.request.MessageCallbackNewCardRequest;
import com.twl.hi.foundation.model.message.extensioncard.data.element.Element;
import com.twl.hi.foundation.model.message.extensioncard.data.element.ElementForButton;
import com.twl.hi.foundation.model.message.extensioncard.data.element.ElementForCallback;
import com.twl.hi.foundation.model.message.extensioncard.data.element.LineElement;
import com.twl.hi.foundation.model.message.extensioncard.data.modules.Module;
import com.twl.hi.foundation.model.message.extensioncard.data.modules.ModuleForStreamAction;
import com.twl.hi.richtext.clipboard.ClipboardHelper;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;
import hi.kernel.HiKernel;
import lib.twl.common.util.LList;
import lib.twl.common.util.ToastUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/15
 * description: 流式Action模块View构造器
 */
public class ModuleForStreamActionViewCreator extends ModuleViewCreator<ModuleForStreamAction> {

    /**
     * 已支持的元素类型
     */
    public ModuleForStreamActionViewCreator(ModuleForStreamAction module, CardViewConfig config) {
        super(module, config);
    }

    @Override
    public View createView() {
        Context context = config.getContext();
        StreamActionModuleView streamActionModuleView = new StreamActionModuleView(context, config);
        streamActionModuleView.updateData(module);
        return streamActionModuleView;
    }

    /**
     * 流式Action模块视图
     * 实现 IModuleView 接口
     */
    public static class StreamActionModuleView extends LinearLayout implements IModuleView {

        private static final String TAG = "StreamActionModuleView";
        private final CardViewConfig config;

        // 按钮视图引用
        private View btnStopGenerate;
        private View btnCopy;
        private View btnRefresh;
        private View btnThumbsUp;
        private View btnThumbsDown;

        // 图标视图引用
        private ImageView ivThumbsUp;
        private ImageView ivThumbsDown;

        // 文字视图引用
        private TextView tvCopy;
        private TextView tvRefresh;

        private View divider;

        public StreamActionModuleView(Context context, CardViewConfig config) {
            super(context);
            this.config = config;
            initViews();
        }

        /**
         * 初始化视图
         */
        private void initViews() {
            // 加载XML布局
            LayoutInflater.from(getContext()).inflate(R.layout.chat_stream_action_layout, this, true);
            // 获取按钮视图引用
            if(config.isInChatActivity() && config.isOriginalMessage()){
                if(config.getExtensionCardMessage()!=null
                        && config.getExtensionCardMessage().getCardInfo()!=null
                        && config.getExtensionCardMessage().getCardInfo().getGlobalConfig()!=null){
                    String replyUserId = config.getExtensionCardMessage().getCardInfo().getGlobalConfig().getReplyUserId();
                    if(TextUtils.isEmpty(replyUserId) || HiKernel.getHikernel().getAccount().getUserId().equals(replyUserId)){
                        setVisibility(View.VISIBLE);
                    }else{
                        setVisibility(View.GONE);
                    }

                }
            }else{
                setVisibility(View.GONE);
            }

            btnStopGenerate = findViewById(R.id.btn_stop_generate);
            btnCopy = findViewById(R.id.btn_copy);
            btnRefresh = findViewById(R.id.btn_refresh);
            btnThumbsUp = findViewById(R.id.btn_thumbs_up);
            btnThumbsDown = findViewById(R.id.btn_thumbs_down);

            // 获取图标视图引用
            ivThumbsUp = findViewById(R.id.iv_thumbs_up);
            ivThumbsDown = findViewById(R.id.iv_thumbs_down);

            // 获取文字视图引用
            tvCopy = findViewById(R.id.tv_copy);
            tvRefresh = findViewById(R.id.tv_refresh);

            divider = findViewById(R.id.divider);
        }



        @Override
        public void updateData(Module module) {
            TLog.debug(TAG, "updateData ModuleForStreamAction " + module.getClass().getSimpleName());

            if (!(module instanceof ModuleForStreamAction)) return;

            ModuleForStreamAction streamActionModule = (ModuleForStreamAction) module;

            if (streamActionModule.getActions() == null) {
                TLog.debug(TAG, "ModuleForStreamAction.getActions() is empty");
                hideAllButtons();
                return;
            }

            final List<LineElement> actions = filterAction(streamActionModule.getActions().getActionElementList());
            if (LList.isEmpty(actions)) {
                TLog.debug(TAG, "actions is empty");
                hideAllButtons();
                return;
            }
            // 判断是否为生成中状态
            boolean isGenerating = hasStopGenerateButton(actions);

            if (isGenerating) {
                // 生成中：只显示停止生成按钮
                showGeneratingState(actions);
            } else {
                // 生成完成：显示操作按钮组
                showCompletedState(actions);
            }
        }

        /**
         * 隐藏所有按钮
         */
        private void hideAllButtons() {
            btnStopGenerate.setVisibility(View.GONE);
            btnCopy.setVisibility(View.GONE);
            btnRefresh.setVisibility(View.GONE);
            btnThumbsUp.setVisibility(View.GONE);
            btnThumbsDown.setVisibility(View.GONE);
        }

        /**
         * 显示生成中状态（只显示停止生成按钮）
         */
        private void showGeneratingState(List<LineElement> actions) {
            hideAllButtons();

            // 查找停止生成按钮数据并设置
            for (LineElement action : actions) {
                if (action.getElementTag() == Element.ELEMENT_STOP_STREAM) {
                    setupStopGenerateButton((ElementForButton) action);
                    btnStopGenerate.setVisibility(View.VISIBLE);
                    break;
                }
            }
        }

        /**
         * 显示生成完成状态（显示操作按钮组）
         */
        private void showCompletedState(List<LineElement> actions) {
            hideAllButtons();
            boolean textButton = false;
            boolean iconButton = false;
            // 按顺序设置按钮：复制、刷新、点赞、点踩
            for (LineElement action : actions) {
                if (!(action instanceof ElementForButton)) {
                    TLog.warn(TAG, "showCompletedState: action is not ElementForButton, skip");
                    continue;
                }
                ElementForButton buttonElement = (ElementForButton) action;
                int elementTag = action.getElementTag();
                TLog.debug(TAG, "showCompletedState elementTag:" + elementTag);
                switch (elementTag) {
                    case Element.ELEMENT_COPY_STREAM:
                        setupCopyButton(buttonElement);
                        btnCopy.setVisibility(View.VISIBLE);
                        textButton = true;
                        break;
                    case Element.ELEMENT_REFRESH_STREAM:
                        setupRefreshButton(buttonElement);
                        btnRefresh.setVisibility(View.VISIBLE);
                        textButton = true;
                        break;
                    case Element.ELEMENT_PRAISE_STREAM:
                        setupThumbsUpButton(buttonElement);
                        btnThumbsUp.setVisibility(View.VISIBLE);
                        iconButton = true;
                        break;
                    case Element.ELEMENT_THUMBS_DOWN_STREAM:
                        setupThumbsDownButton(buttonElement);
                        btnThumbsDown.setVisibility(View.VISIBLE);
                        iconButton = true;
                        break;
                }
            }
            if (textButton && iconButton) {
                divider.setVisibility(View.VISIBLE);
            }
        }

        /**
         * 检查是否存在停止生成按钮
         */
        private boolean hasStopGenerateButton(List<LineElement> actions) {
            for (LineElement action : actions) {
                if (action.getElementTag() == Element.ELEMENT_STOP_STREAM) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 设置停止生成按钮
         */
        private void setupStopGenerateButton(ElementForButton buttonElement) {
            // 设置点击事件
            btnStopGenerate.setOnClickListener(v -> handleButtonClick(v, buttonElement));
            // 停止生成按钮的文字和图标已在XML中固定设置
        }

        /**
         * 设置复制按钮
         */
        private void setupCopyButton(ElementForButton buttonElement) {
            // 设置文字
            String buttonText = getButtonText(buttonElement);
            if (buttonText != null && !buttonText.isEmpty()) {
                tvCopy.setText(buttonText);
            }

            // 设置点击事件
            btnCopy.setOnClickListener(v -> handleButtonClick(v, buttonElement));
        }

        /**
         * 设置刷新按钮
         */
        private void setupRefreshButton(ElementForButton buttonElement) {
            // 设置文字
            String buttonText = getButtonText(buttonElement);
            if (buttonText != null && !buttonText.isEmpty()) {
                tvRefresh.setText(buttonText);
            }

            // 设置点击事件
            btnRefresh.setOnClickListener(v -> handleButtonClick(v, buttonElement));
        }

        /**
         * 设置点赞按钮
         */
        private void setupThumbsUpButton(ElementForButton buttonElement) {
            // 根据状态设置图标
            boolean isSelected = false;
            try {
                ElementForButton.ElementForButtonData buttonData = buttonElement != null ? buttonElement.getButton() : null;
                if (buttonData != null) {
                    isSelected = buttonData.getType() == StreamActionConstants.BUTTON_TYPE_SELECTED;
                }
            } catch (Exception e) {
                TLog.error(TAG, "setupThumbsUpButton error: " + e.getMessage(), e);
                isSelected = false;
            }
            int iconResource = isSelected ? R.drawable.chat_ic_stream_praised : R.drawable.chat_ic_stream_praise;
            ivThumbsUp.setImageResource(iconResource);

            // 设置点击事件
            btnThumbsUp.setOnClickListener(v -> handleButtonClick(v, buttonElement));
        }

        /**
         * 设置点踩按钮
         */
        private void setupThumbsDownButton(ElementForButton buttonElement) {
            // 根据状态设置图标
            boolean isSelected = false;
            try {
                ElementForButton.ElementForButtonData buttonData = buttonElement != null ? buttonElement.getButton() : null;
                if (buttonData != null) {
                    isSelected = buttonData.getType() == StreamActionConstants.BUTTON_TYPE_SELECTED;
                }
            } catch (Exception e) {
                TLog.error(TAG, "setupThumbsDownButton error: " + e.getMessage(), e);
                isSelected = false;
            }
            int iconResource = isSelected ? R.drawable.chat_ic_stream_thumbs_down_filled : R.drawable.chat_ic_stream_thumbs_down;
            ivThumbsDown.setImageResource(iconResource);

            // 设置点击事件
            btnThumbsDown.setOnClickListener(v -> handleButtonClick(v, buttonElement));
        }


        /**
         * 过滤支持的Action元素
         */
        private List<LineElement> filterAction(List<LineElement> originalActions) {
            List<LineElement> res = new ArrayList<>();
            HashSet<Integer> streamActionSupport = new HashSet<>();
            streamActionSupport.add(Element.ELEMENT_STOP_STREAM);
            streamActionSupport.add(Element.ELEMENT_COPY_STREAM);
            streamActionSupport.add(Element.ELEMENT_REFRESH_STREAM);
            streamActionSupport.add(Element.ELEMENT_PRAISE_STREAM);
            streamActionSupport.add(Element.ELEMENT_THUMBS_DOWN_STREAM);

            for (int i = 0; originalActions != null && i < originalActions.size(); i++) {
                LineElement lineElement = originalActions.get(i);
                if (streamActionSupport.contains(lineElement.getElementTag())) {
                    res.add(lineElement);
                }
            }
            return res;
        }


        /**
         * 获取按钮文字
         */
        private String getButtonText(ElementForButton buttonElement) {
            if (buttonElement.getButton() != null && buttonElement.getButton().getText() != null) {
                return buttonElement.getButton().getText().getText();
            }
            return "";
        }

        /**
         * 处理按钮点击事件
         */
        private void handleButtonClick(View view, ElementForButton buttonElement) {
            if (buttonElement.getButton() == null) {
                TLog.error(TAG, "handleButtonClick: buttonElement.getButton() is null");
                return;
            }
            //复制
            if (buttonElement.getElementTag() == Element.ELEMENT_COPY_STREAM) {
                copyContent(buttonElement);
                //刷新
            } else {
                realDoClick(view, buttonElement);
            }
        }


        /**
         * 复制内容
         *
         * @param buttonElement
         */
        private void copyContent(ElementForButton buttonElement) {
            if (config.getExtensionCardMessage() != null) {
                String text = ClipboardHelper.toMarkDownText(config.getExtensionCardMessage());
                TLog.info("copyContent", "copyContent: %s", text);
                ClipboardHelper.copyOriginText(config.getContext(), text);
                ToastUtils.success(getResources().getString(R.string.copy_success));

            }
        }

        /**
         * 真正执行点击逻辑
         *
         * @param buttonElement
         */
        private void realDoClick(View view, ElementForButton buttonElement) {
            long msgId = config.getMsgId();
            List<ElementForCallback> callBackList = buttonElement.getButton().getCallBacks();

            if (msgId > 0 && !LList.isEmpty(callBackList)) {
                MessageCallbackNewCardRequest request = new MessageCallbackNewCardRequest();
                request.msgId = msgId;
                request.callback = ExtensionCardHelper.getFormattedCallback(callBackList, buttonElement.getElementTag());
                view.setClickable(false);
                request.setCallback(new BaseApiRequestCallback<HttpResponse>() {
                    @Override
                    public void onSuccess(ApiData<HttpResponse> data) {
                        updateButtonState(view,buttonElement);
                    }

                    @Override
                    public void onComplete() {
                        view.setClickable(true);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        if (reason != null && !TextUtils.isEmpty(reason.getErrReason())) {
                            ToastUtils.failure(reason.getErrReason());
                        }
                    }
                });
                HttpExecutor.execute(request);
            }
        }

        private void updateButtonState(View view, ElementForButton buttonElement){
            if(view == btnThumbsUp){//点赞
                try {
                    ElementForButton.ElementForButtonData buttonData = buttonElement != null ? buttonElement.getButton() : null;
                    if (buttonData != null) {
                        if(buttonData.getType() == StreamActionConstants.BUTTON_TYPE_SELECTED){
                            buttonData.setType(StreamActionConstants.BUTTON_TYPE_NORMAL);
                        }else{
                            buttonData.setType(StreamActionConstants.BUTTON_TYPE_SELECTED);
                        }
                        setupThumbsUpButton(buttonElement);
                    }
                } catch (Exception e) {
                    TLog.error(TAG, "updateButtonState thumbsUp error: " + e.getMessage(), e);
                }
            }else if(view == btnThumbsDown){
                try {
                    ElementForButton.ElementForButtonData buttonData = buttonElement != null ? buttonElement.getButton() : null;
                    if (buttonData != null) {
                        if(buttonData.getType() == StreamActionConstants.BUTTON_TYPE_SELECTED){
                            buttonData.setType(StreamActionConstants.BUTTON_TYPE_NORMAL);
                        }else{
                            buttonData.setType(StreamActionConstants.BUTTON_TYPE_SELECTED);
                        }
                        setupThumbsDownButton(buttonElement);
                    }
                } catch (Exception e) {
                    TLog.error(TAG, "updateButtonState thumbsDown error: " + e.getMessage(), e);
                }
            }
        }

    }
}
