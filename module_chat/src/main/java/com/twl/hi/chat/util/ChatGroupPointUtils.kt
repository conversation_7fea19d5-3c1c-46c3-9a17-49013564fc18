package com.twl.hi.chat.util

import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.foundation.utils.PointUtils
import hi.kernel.Constants

/**
 * <AUTHOR>
 * @date 2022/6/15
 * description: 会话分组相关埋点
 */
object ChatGroupPointUtils {
    /**
     * 【沟通】消息列表顶部「会话分组」的添加/编辑点击
     */
    private const val POINT_NAME_ADD_EDIT_CLICK = "chatlist_add_edit_category_click"
    const val POINT_CHAT_LIST_TAG_OPERATION = "chatlist-tag-operation"
    const val POINT_CHAT_SETTING_TAG_OPERATION = "chat-setting-tag-operation"

    const val SOURCE_ADD_EDIT_HOMEPAGE = "homepage"
    const val SOURCE_ADD_EDIT_JUMPPAGE = "jumppage"
    const val SOURCE_ADD_EDIT_LIST_SELECT = "list_select"
    const val SOURCE_ADD_EDIT_SETTING_SELECT = "setting_select"


    private const val CHAT_TYPE_SINGLE = "single"
    private const val CHAT_TYPE_GROUP = "group"

    const val ICON_TYPE_ADD = "1"
    const val ICON_TYPE_EDIT = "2"
    const val ICON_TYPE_DELETE = "3"
    private const val ICON_TYPE_SORT = "4"

    const val TYPE_CHAT_GROUP_ADD = 1
    const val TYPE_CHAT_GROUP_CHANGE = 4

    fun pointChatGroupAddClick(source: String) {
        PointUtils.BuilderV4()
            .name(POINT_NAME_ADD_EDIT_CLICK)
            .params(Constants.SOURCE, source)
            .params(Constants.ICON_TYPE, ICON_TYPE_ADD)
            .point()
    }

    fun pointChatGroupEditClick() {
        PointUtils.BuilderV4()
            .name(POINT_NAME_ADD_EDIT_CLICK)
            .params(Constants.ICON_TYPE, ICON_TYPE_EDIT)
            .point()
    }


    /**
     * 【沟通】消息列表顶部会话分组栏 -「分组管理」更改分组成功保存
     */
    private const val POINT_NAME_CHAT_GROUP_SAVE = "chatlist_manage_category_save"
    fun pointChatGroupSave(
        iconType: String,
        chatGroupId: String? = null,
        source: String? = null,
        num: Int? = null
    ) {
        val params = PointUtils.BuilderV4()
            .name(POINT_NAME_CHAT_GROUP_SAVE)
            .params(Constants.ICON_TYPE, iconType)
        chatGroupId?.run {
            params.params(Constants.CATEGORY_ID, "$chatGroupId")
        }
        source?.run {
            params.params(Constants.SOURCE, source)
        }
        num?.run {
            params.params(Constants.CHAT_NUM, this)
        }
        params.point()
    }

    fun pointChatGroupSort() {
        pointChatGroupSave(ICON_TYPE_SORT)
    }


    /**
     * 沟通】消息列表顶部「分组管理」-「全部助手」点击
     */
    private const val POINT_NAME_ALL_HELPER = "chatlist_category_search_click"
    private const val SOURCE_ADD = "add"
    private const val SOURCE_EDIT = "edit"
    fun pointAllHelper(isAdd: Boolean) {
        PointUtils.BuilderV4()
            .name(POINT_NAME_ALL_HELPER)
            .params(
                Constants.SOURCE, if (isAdd) {
                    SOURCE_ADD
                } else {
                    SOURCE_EDIT
                }
            ).point()
    }

    /**
     *【沟通】消息列表右键点击对话框后出现「将消息分组至」入口，在出现的分组列表界面点击
     */
    private const val POINT_NAME_CHAT_GROUP_MOVE = "chatlist_rightlist_move_ctgry_click"
    fun pointChatGroupMove(chatType: String, chatId: Long, from: Long? = null, to: Long) {
        PointUtils.BuilderV4()
            .name(POINT_NAME_CHAT_GROUP_MOVE)
            .params(Constants.CHAT_TYPE, chatType)
            .params(Constants.CHAT_ID, "$chatId")
            .params(Constants.FROM, "$from")
            .params(Constants.TO, "$to")
            .point()
    }

    /**
     * 【沟通】F1界面消息列表顶部会话分组栏中的点击
     */
    private const val POINT_NAME_CHAT_GROUP_CLICK = "chatlist_category_f1_click"

    private const val ICON_TYPE_ALL = "1"
    private const val ICON_TYPE_CUSTOM = "2"
    private const val ICON_TYPE_MANAGE = "3"

    fun pointChatGroupItemClick(chatGroupId: String? = null, isAll: Boolean = false) {
        val iconType = when {
            chatGroupId == null -> ICON_TYPE_MANAGE
            isAll -> ICON_TYPE_ALL
            else -> ICON_TYPE_CUSTOM
        }
        val point = PointUtils.BuilderV4()
            .name(POINT_NAME_CHAT_GROUP_CLICK)
            .params(Constants.ICON_TYPE, iconType)
        if (iconType == ICON_TYPE_CUSTOM) {
            point.params(Constants.CATEGORY_ID, "$chatGroupId")
        }
        point.point()
    }


    @JvmStatic
    fun pointChatGroupChangeSave(source: String, mid: String, type: Int, operationType: Int) =
        PointUtils.BuilderV4()
            .name(
                if (source == SOURCE_ADD_EDIT_LIST_SELECT) {
                    POINT_CHAT_LIST_TAG_OPERATION
                } else {
                    POINT_CHAT_SETTING_TAG_OPERATION
                }
            )
            .params(Constants.CHAT_ID, mid)
            .params(
                Constants.CHAT_TYPE, if (type == MessageConstants.MSG_SINGLE_CHAT) {
                    CHAT_TYPE_SINGLE
                } else {
                    CHAT_TYPE_GROUP
                }
            )
            .params(Constants.TYPE, operationType)
            .point()

}