package com.twl.hi.chat.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.twl.hi.chat.model.ChatRecordAllChatMessage;
import com.twl.hi.foundation.api.response.SearchChatContentRecordResponse;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.Group;
import com.twl.hi.foundation.model.message.ChatMessage;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.List;

import lib.twl.common.util.LList;

public class ChatRecordAllForFileViewModel extends ChatRecordSearchBaseViewModel {
    private MutableLiveData<List<ChatRecordAllChatMessage>> fileLiveData = new MediatorLiveData<>();

    public ChatRecordAllForFileViewModel(Application application) {
        super(application);
    }

    public void searchAllChatRecord(String content) {
        searchContent(content);
    }

    @Override
    public void handleDataInChildThread(ApiData<SearchChatContentRecordResponse> data) {
        List<ChatMessage> msgs = data.resp.messages;
        if (LList.isEmpty(msgs)) {
            fileLiveData.postValue(null);
            return;
        }
        List<ChatRecordAllChatMessage> result = new ArrayList<>(msgs.size());
        for (ChatMessage chatMessage : msgs) {
            ChatRecordAllChatMessage chatRecordAllChatMessage = new ChatRecordAllChatMessage();
            chatRecordAllChatMessage.chatMessage = chatMessage;
            Contact contactById = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getSender().getSenderId());
            if (contactById != null) {
                chatRecordAllChatMessage.contactName = contactById.getShowName();
            }
            if (chatMessage.getType() == MessageConstants.MSG_GROUP_CHAT) {
                Group groupById = ServiceManager.getInstance().getGroupService().getOnlyGroupById(chatMessage.getChatId());
                if (groupById != null) {
                    chatRecordAllChatMessage.groupName = TextUtils.isEmpty(groupById.getGroupRemark()) ? groupById.getGroupName() : groupById.getGroupRemark();
                }
            }
            result.add(chatRecordAllChatMessage);
        }
        fileLiveData.postValue(result);
    }

    @Override
    public void onFailedData(ErrorReason reason) {
        fileLiveData.postValue(null);
    }

    public MutableLiveData<List<ChatRecordAllChatMessage>> getFileLiveData() {
        return fileLiveData;
    }

    public void resetSearchType() {
        mUserId = "";
        mChatId = "";
        mChatType = -1;
        beginDate = "";
        endDate = "";
    }


    /**
     * 上拉加载更多搜索内容
     */
    public void loadMoreAllContent() {
        loadMoreContent();
    }

    @Override
    public String getTab() {
        return "文件";
    }
}