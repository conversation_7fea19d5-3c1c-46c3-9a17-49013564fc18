package com.twl.hi.chat.widget

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.net.Uri
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.withClip
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.core.view.setPadding
import com.facebook.cache.common.CacheKey
import com.facebook.cache.common.SimpleCacheKey
import com.facebook.common.references.CloseableReference
import com.facebook.drawee.view.SimpleDraweeView
import com.facebook.imagepipeline.bitmaps.PlatformBitmapFactory
import com.facebook.imagepipeline.request.ImageRequestBuilder
import com.facebook.imagepipeline.request.Postprocessor
import com.twl.hi.chat.R
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.ImagePreview
import com.twl.hi.foundation.model.TextPreview
import lib.twl.common.ext.dp

class HyperlinkPreviewLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val backgroundRect = Path()
    private val previewImage: SimpleDraweeView
    private val previewText: View
    private val brandIcon: SimpleDraweeView
    private val titleText: TextView
    private val divider: View
    private val descriptionText: TextView

    init {
        LayoutInflater.from(context).inflate(R.layout.chat_layout_hyperlink_preview, this)
        previewImage = findViewById(R.id.preview_image)
        previewText = findViewById(R.id.preview_text)
        brandIcon = findViewById(R.id.logo)
        titleText = findViewById(R.id.title)
        divider = findViewById(R.id.divider)
        descriptionText = findViewById(R.id.description)

        val fontScale = ServiceManager.getInstance().settingService.fontScale
        titleText.setTextSize(TypedValue.COMPLEX_UNIT_PX, titleText.textSize * fontScale)
        descriptionText.setTextSize(TypedValue.COMPLEX_UNIT_PX, descriptionText.textSize * fontScale)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        when (MeasureSpec.getMode(widthMeasureSpec)) {
            MeasureSpec.EXACTLY -> super.onMeasure(widthMeasureSpec, heightMeasureSpec)
            MeasureSpec.AT_MOST -> {
                val maxWidth = MeasureSpec.getSize(widthMeasureSpec)
                val maxWidthMeasureSpec = MeasureSpec.makeMeasureSpec(maxWidth, MeasureSpec.EXACTLY)
                super.onMeasure(maxWidthMeasureSpec, heightMeasureSpec)
            }
            MeasureSpec.UNSPECIFIED -> {
                val requiredWidthMeasureSpec = MeasureSpec.makeMeasureSpec(requireWidth.toInt(), MeasureSpec.EXACTLY)
                super.onMeasure(requiredWidthMeasureSpec, heightMeasureSpec)
            }
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        backgroundRect.reset()
        backgroundRect.addRoundRect(
            0f,
            0f,
            width.toFloat(),
            height.toFloat(),
            roundRadius,
            roundRadius,
            Path.Direction.CCW
        )
    }

    override fun dispatchDraw(canvas: Canvas) {
        canvas.withClip(backgroundRect) {
            drawColor(Color.WHITE)
            super.dispatchDraw(canvas)
        }
    }

    fun setPreview(imagePreview: ImagePreview) {
        setPadding(0)
        isVisible = true
        previewText.isVisible = false
        previewImage.isVisible = true

        val imageRequest = ImageRequestBuilder
            .newBuilderWithSource(Uri.parse(imagePreview.url))
            .setPostprocessor(ScaleCropPostprocessor())
            .build()
        previewImage.setImageRequest(imageRequest)
    }

    fun setPreview(textPreview: TextPreview, maxTitleLines: Int, maxDescriptionLines: Int) {
        setPadding(12.dp.toInt())
        isVisible = true
        previewText.isVisible = true
        previewImage.isVisible = false
        titleText.maxLines = maxTitleLines
        descriptionText.maxLines = maxDescriptionLines

        brandIcon.setImageURI(textPreview.iconUrl)
        titleText.text = textPreview.title
        descriptionText.text = textPreview.description?.trim()
        divider.isGone = textPreview.description.isNullOrBlank()
        descriptionText.isGone = textPreview.description.isNullOrBlank()
    }

    private class ScaleCropPostprocessor : Postprocessor {
        private val width = requireWidth.toInt()
        private val height = (requireWidth / 2).toInt()

        override fun getName() = "PreviewCropPostprocessor"

        override fun process(
            sourceBitmap: Bitmap,
            bitmapFactory: PlatformBitmapFactory
        ): CloseableReference<Bitmap> {
            val scale = width * 1.0f / sourceBitmap.width
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            val paint = Paint(Paint.ANTI_ALIAS_FLAG)
            val matrix = Matrix()
            matrix.setScale(scale, scale)
            canvas.drawBitmap(sourceBitmap, matrix, paint)
            return bitmapFactory.createBitmap(bitmap)
        }

        override fun getPostprocessorCacheKey(): CacheKey {
            return SimpleCacheKey(name)
        }
    }

    companion object {
        private val roundRadius = 4.dp
        private val requireWidth = 260.dp
    }
}