package com.twl.hi.chat.message.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;

import com.twl.hi.chat.message.api.request.FavoriteCancelRequest;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.response.bean.FavoriteInterface;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 收藏页面Activity ViewModel
 */
public class FavoriteViewModel extends FoundationViewModel {

    protected MutableLiveData<List<FavoriteInterface>> favoritesLiveData = new MutableLiveData<>();
    protected MutableLiveData<List<FavoriteInterface>> favoritesSearchLiveData = new MutableLiveData<>();

    public FavoriteViewModel(Application application) {
        super(application);
    }

    public MutableLiveData<List<FavoriteInterface>> getFavoritesLiveData() {
        return favoritesLiveData;
    }

    public MutableLiveData<List<FavoriteInterface>> getFavoritesSearchLiveData() {
        return favoritesSearchLiveData;
    }

    /**
     * 取消收藏
     */
    public void cancelFavorite(String favorId) {
        FavoriteCancelRequest request = new FavoriteCancelRequest(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                if (!TextUtils.isEmpty(favorId)) {
                    refreshList(favorId);
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.favorId = favorId;
        HttpExecutor.execute(request);
    }

    public void refreshList(String favorId) {
        if (favoritesLiveData.getValue() != null && favoritesLiveData.getValue().size() > 0) {
            List<FavoriteInterface> beans = new ArrayList<>(favoritesLiveData.getValue());
            for (FavoriteInterface favoriteBean : beans) {
                if (TextUtils.equals(favoriteBean.getFavorId(), favorId)) {
                    beans.remove(favoriteBean);
                    break;
                }
            }
            favoritesLiveData.postValue(beans);
        }
        if (favoritesSearchLiveData.getValue() != null && favoritesSearchLiveData.getValue().size() > 0) {
            List<FavoriteInterface> beans = new ArrayList<>(favoritesSearchLiveData.getValue());
            for (FavoriteInterface favoriteBean : beans) {
                if (TextUtils.equals(favoriteBean.getFavorId(), favorId)) {
                    beans.remove(favoriteBean);
                    break;
                }
            }
            favoritesSearchLiveData.postValue(beans);
        }
    }

}