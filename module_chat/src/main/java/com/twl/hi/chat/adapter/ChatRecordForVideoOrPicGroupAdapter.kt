package com.twl.hi.chat.adapter

import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.twl.hi.chat.R
import com.twl.hi.chat.callback.PicAndVideoGroupListCallback
import com.twl.hi.chat.callback.PictureAndVideoRecordCheckContract
import com.twl.hi.foundation.model.message.MessageGroup
import lib.twl.common.adapter.BaseDataBindingAdapter
import lib.twl.common.adapter.BaseDataBindingViewHolder
import lib.twl.common.databinding.LibCommonViewRefreshRecyclerBinding

/**
 * Author : Xuweixiang .
 * Date   : On 2022/5/25
 * Email  : Contact <EMAIL>
 * Desc   : 聊天记录多媒体资源记录列表
 *
 */

private const val GRID_SPAN_ITEM_COUNT = 4
private const val GRID_SPAN_TITLE_COUNT = 1

class ChatRecordForVideoOrPicGroupAdapter(
    private val viewModel: PictureAndVideoRecordCheckContract,
    private val callback: PicAndVideoGroupListCallback
) : BaseDataBindingAdapter<MessageGroup, LibCommonViewRefreshRecyclerBinding>(
    R.layout.lib_common_view_refresh_recycler,
    null
) {

    fun notifyItemCheck(position: Int, groupPosition: Int) {
        (getViewByPosition(position, R.id.recycler) as? RecyclerView)?.run {
            adapter?.notifyItemChanged(groupPosition)
        }
    }

    override fun bind(
        helper: BaseDataBindingViewHolder<LibCommonViewRefreshRecyclerBinding>,
        binding: LibCommonViewRefreshRecyclerBinding,
        item: MessageGroup
    ) {
        val adapter = ChatRecordForVideoOrPicAdapter(viewModel, callback, item)
        binding.recycler.adapter = adapter
        binding.recycler.layoutManager = GridLayoutManager(mContext, GRID_SPAN_ITEM_COUNT).apply {
            spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return if (adapter.getItemViewType(position) == -1) {
                        GRID_SPAN_ITEM_COUNT
                    } else {
                        GRID_SPAN_TITLE_COUNT
                    }
                }
            }
        }
        adapter.setNewData(item.messages)
    }

    override fun getPointLevel(): String? {
        return "图片及视频"
    }
}