package com.twl.hi.chat.dialog

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import com.twl.hi.chat.R
import com.twl.hi.chat.databinding.ChatDialogRobotAlertBinding

class RobotAlertDialog(private val context: Context) {

    fun show(robotName: String?, content: String?, onConfirm: (closeRemind: Boolean) -> Unit) {
        val binding = ChatDialogRobotAlertBinding.inflate(LayoutInflater.from(context))
        val dialog = Dialog(context, R.style.common_dialog)
        dialog.setCanceledOnTouchOutside(true)
        dialog.setContentView(binding.root)

        val displayName = robotName ?: "机器人"
        binding.tvTitle.setText("确认将“", displayName, "”添加到当前群聊？")

        binding.tvContent.text = if (content.isNullOrBlank()) {
            "添加后，群成员都会收到机器人加群的通知。\n你可通过机器人与本群成员协作，机器人可向本群自动推送消息，也可与群成员进行简单的交互。"
        } else {
            content
        }
        binding.tvPositive.setOnClickListener {
            onConfirm(binding.remindClose.isChecked)
            dialog.dismiss()
        }
        binding.tvNegative.setOnClickListener {
            dialog.dismiss()
        }
        dialog.show()
    }
}