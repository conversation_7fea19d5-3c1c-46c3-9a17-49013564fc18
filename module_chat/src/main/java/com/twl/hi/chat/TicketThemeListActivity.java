package com.twl.hi.chat;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;

import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.chat.adapter.TicketThemeListAdapter;
import com.twl.hi.chat.api.response.bean.TicketThemeBean;
import com.twl.hi.chat.callback.TicketThemeListCallback;
import com.twl.hi.chat.databinding.ChatActivityTicketThemeListBinding;
import com.twl.hi.chat.databinding.ChatPopRedEnvelopeThemeBinding;
import com.twl.hi.chat.viewmodel.TicketThemeListViewModel;
import com.twl.hi.foundation.model.message.MessageConstants;

import lib.twl.common.ext.ViewExtKt;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.widget.HiPopupWindow;

public class TicketThemeListActivity extends FoundationVMActivity<ChatActivityTicketThemeListBinding, TicketThemeListViewModel> implements TicketThemeListCallback {

    private TicketThemeListAdapter mAdapter;
    private HiPopupWindow popupWindow;

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_activity_ticket_theme_list;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getViewModel().getThemeList();
        getDataBinding().titleBar.tvTitle.setText(R.string.chat_red_envelopes_theme);
        mAdapter = new TicketThemeListAdapter(this);
        GridLayoutManager manager = new GridLayoutManager(this, 2);
        manager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                switch (mAdapter.getItemViewType(position)) {
                    case TicketThemeListAdapter.TYPE_THEME_TITLE:
                        return 2;
                    case TicketThemeListAdapter.TYPE_THEME_ITEM:
                        return 1;
                }
                return 1;
            }
        });
        getDataBinding().recycler.setLayoutManager(manager);
        getDataBinding().recycler.setAdapter(mAdapter);
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) getDataBinding().topStatusBar.getLayoutParams();
        layoutParams.height = ViewExtKt.getStatusBarsHeight(this);
        getDataBinding().topStatusBar.setLayoutParams(layoutParams);
        getViewModel().getResultSuccess().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean result) {
                if (result) {
                    mAdapter.submitList(getViewModel().getThemes());
                }
            }
        });
    }

    @Override
    public void clickLeft(View view) {
        setResult(0);
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    public void onThemeClick(TicketThemeBean bean) {
        if (popupWindow != null && popupWindow.isShowing()) {
            return;
        }
        ChatPopRedEnvelopeThemeBinding binding = ChatPopRedEnvelopeThemeBinding.inflate(getLayoutInflater());
        binding.setCallback(this);
        binding.setTheme(bean);
        popupWindow = new HiPopupWindow.Builder(this).setContentView(binding.getRoot()).setWidth(ViewGroup.LayoutParams.WRAP_CONTENT).setShadow(getWindow(), 0.2f).build();
        popupWindow.showAtLocation(getDataBinding().getRoot(), Gravity.CENTER);
    }

    @Override
    public void onThemeSelected(TicketThemeBean bean) {
        Intent intent = new Intent();
        intent.putExtra(MessageConstants.RED_ENVELOPE_THEME_ID, bean.id);
        intent.putExtra(MessageConstants.RED_ENVELOPE_THEME_NAME, bean.themeName);
        intent.putExtra(MessageConstants.RED_ENVELOPE_THEME_COVER_URL, bean.coverUrl);
        intent.putExtra(MessageConstants.RED_ENVELOPE_THEME_TITLE, bean.title);
        setResult(Activity.RESULT_OK, intent);
        AppUtil.finishActivity(this);
    }
}