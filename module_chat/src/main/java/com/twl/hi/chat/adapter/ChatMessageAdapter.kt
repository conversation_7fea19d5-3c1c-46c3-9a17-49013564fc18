package com.twl.hi.chat.adapter

import android.animation.ValueAnimator
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.core.animation.doOnEnd
import androidx.core.graphics.withTranslation
import androidx.core.view.children
import androidx.core.view.doOnLayout
import androidx.core.view.isVisible
import androidx.databinding.ViewDataBinding
import androidx.databinding.ViewStubProxy
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.sankuai.waimai.router.Router
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.callback.ChatItemListener
import com.twl.hi.basic.util.ThemeUtils
import com.twl.hi.basic.views.InterceptConstraintLayout
import com.twl.hi.chat.BR
import com.twl.hi.chat.R
import com.twl.hi.chat.bindadapter.ChatBindingAdapter
import com.twl.hi.chat.databinding.ChatItemMessageAppCardReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageAppCardSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageAudioReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageAudioSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageChatShareReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageChatShareSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageExtensionCardReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageExtensionCardSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageFileReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageFileSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageGroupCardReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageGroupCardSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageHintBinding
import com.twl.hi.chat.databinding.ChatItemMessageImageCardReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageImageCardSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageLinkCallReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageLinkCallSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageLinkReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageLinkSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageMarkdownTextReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageMarkdownTextSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageNoTypeReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageNoTypeSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageNoneBinding
import com.twl.hi.chat.databinding.ChatItemMessageOnlineFileReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageOnlineFileSendBinding
import com.twl.hi.chat.databinding.ChatItemMessagePicReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessagePicSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageRedEnvelopReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageRedEnvelopSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageRichTextReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageRichTextSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageStickerReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageStickerSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageSystemCardReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageSystemCardSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageTaskCommentCardReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageTaskCommentCardSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageTextReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageTextSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageUserCardReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageUserCardSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageVideoMeetingCardReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageVideoMeetingCardSendBinding
import com.twl.hi.chat.databinding.ChatItemMessageVideoReceiveBinding
import com.twl.hi.chat.databinding.ChatItemMessageVideoSendBinding
import com.twl.hi.chat.databinding.ChatLayoutQuickReplyBinding
import com.twl.hi.chat.databinding.ChatViewMessageVideoMeetingCardBinding
import com.twl.hi.chat.viewmodel.ChatBaseViewModel
import com.twl.hi.chat.viewmodel.GroupChatBaseViewModel
import com.twl.hi.chat.widget.ChatMessageViewModel
import com.twl.hi.chat.widget.HyperlinkPreviewLayout
import com.twl.hi.chat.widget.MessageStub
import com.twl.hi.chat.widget.ZhishuPreviewLayout
import com.twl.hi.chat.widget.ZhishuPreviewLayout.ReceiverPreviewModel
import com.twl.hi.chat.util.ChatMemoryManager
import com.twl.hi.chat.widget.ZhishuPreviewLayout.SenderPreviewModel
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.media.IMeetingService
import com.twl.hi.foundation.media.MediaConstants
import com.twl.hi.foundation.model.AtMethod
import com.twl.hi.foundation.model.Hypertext
import com.twl.hi.foundation.model.ZhishuDocumentEntity
import com.twl.hi.foundation.model.ZhishuPermissionEntity
import com.twl.hi.foundation.model.message.ChatMessage
import com.twl.hi.foundation.model.message.ChatMessageWithAt
import com.twl.hi.foundation.model.message.MessageAndQuote
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.foundation.model.message.MessageForAction
import com.twl.hi.foundation.model.message.MessageForAppCard
import com.twl.hi.foundation.model.message.MessageForAudio
import com.twl.hi.foundation.model.message.MessageForChatShare
import com.twl.hi.foundation.model.message.MessageForEmpty
import com.twl.hi.foundation.model.message.MessageForFile
import com.twl.hi.foundation.model.message.MessageForGroupCard
import com.twl.hi.foundation.model.message.MessageForHint
import com.twl.hi.foundation.model.message.MessageForImageCard
import com.twl.hi.foundation.model.message.MessageForLink
import com.twl.hi.foundation.model.message.MessageForLinkCall
import com.twl.hi.foundation.model.message.MessageForMarkdownText
import com.twl.hi.foundation.model.message.MessageForOnlineFile
import com.twl.hi.foundation.model.message.MessageForPic
import com.twl.hi.foundation.model.message.MessageForRedEnvelope
import com.twl.hi.foundation.model.message.MessageForRichText
import com.twl.hi.foundation.model.message.MessageForSticker
import com.twl.hi.foundation.model.message.MessageForSystemCard
import com.twl.hi.foundation.model.message.MessageForTaskCommentCard
import com.twl.hi.foundation.model.message.MessageForText
import com.twl.hi.foundation.model.message.MessageForUnknown
import com.twl.hi.foundation.model.message.MessageForUserCard
import com.twl.hi.foundation.model.message.MessageForVideo
import com.twl.hi.foundation.model.message.MessageForVideoMeetingCard
import com.twl.hi.foundation.model.message.MessageWithHypertext
import com.twl.hi.foundation.model.message.QuoteMessageInfo
import com.twl.hi.foundation.model.message.Visitor
import com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard
import com.twl.hi.foundation.model.security.BusinessModuleEnum
import com.twl.hi.foundation.utils.Analytics
import hi.kernel.Constants
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import lib.twl.common.ext.dp
import lib.twl.common.ext.map
import lib.twl.common.ext.sp
import lib.twl.common.util.AppUtil
import lib.twl.common.util.ProcessHelper
import lib.twl.common.util.ToastUtils

private const val TAG = "ChatMessageAdapter"

private fun RecyclerView.findAdapterPosition(view: View, adapter: RecyclerView.Adapter<*>): Int {
    val viewHolder = findContainingViewHolder(view)
    return if (viewHolder?.bindingAdapter == adapter) {
        viewHolder.bindingAdapterPosition
    } else {
        RecyclerView.NO_POSITION
    }
}

private fun RecyclerView?.findMessageBindTo(view: View, adapter: ChatMessageAdapter): ChatMessage? {
    return findMessageBindWithPos(view, adapter)?.first
}

private fun RecyclerView?.findMessageBindWithPos(view: View, adapter: ChatMessageAdapter): Pair<ChatMessage, Int>? {
    val position = this?.findAdapterPosition(view, adapter) ?: RecyclerView.NO_POSITION
    return if (position in 0 until adapter.itemCount) {
        Pair(adapter.currentList[position].message, position)
    } else null
}

private inline fun RecyclerView.walkthrough(visit: (View) -> Unit) {
    for (i in 0 until childCount) {
        visit(getChildAt(i))
    }
}

private inline fun RecyclerView.findChild(predicate: (View) -> Boolean): View? {
    for (i in 0 until childCount) {
        val childView = getChildAt(i)
        if (predicate(childView)) {
            return childView
        }
    }
    return null
}

/**
 * 监听当前参加的会议的会议号来标记当前的会议卡片
 */
fun ChatViewMessageVideoMeetingCardBinding.observeButtonStr(message: MessageForVideoMeetingCard) {
    //获取当前会议号的LiveData
    val meetingNoLiveData = Router.getService(IMeetingService::class.java, MediaConstants.MEETING_ENGINE_LISTENER_KEY).currentMeetingNoLiveData

    meetingCardButtonStr = meetingNoLiveData.map { meetingNo ->
        message.videoMeetingInfo?.run {
            if (cardType == MessageForVideoMeetingCard.VideoMeetingCardType.TYPE_FINISH_MEETING) {
                "已结束"
            } else {
                if (meetingNo == confCode) {
                    "已加入"
                } else {
                    buttons?.takeIf { it.isNotEmpty() }?.get(0)?.text?.takeIf { it.isNotEmpty() } ?: "加入会议"
                }
            }
        } ?: run {
            "加入会议"
        }
    }
}

/**
 * 聊天页中的消息 Item 适配器
 */
class ChatMessageAdapter(
    lifecycleOwner: LifecycleOwner,
    private val chatItemListener: ChatItemListener,
    val chatViewModel: ChatBaseViewModel
) : ListAdapter<MessageAndQuote, ChatMessageViewHolder<*>>(
    object : DiffUtil.ItemCallback<MessageAndQuote>() {
        override fun areItemsTheSame(oldItem: MessageAndQuote, newItem: MessageAndQuote) =
            oldItem.message.mediaType == newItem.message.mediaType && oldItem.message.mid == newItem.message.mid
        override fun areContentsTheSame(oldItem: MessageAndQuote, newItem: MessageAndQuote) = oldItem == newItem
    }
) {
    var chatMessageViewModel: ChatMessageViewModel? = null
    var onItemLayoutChanged: View.OnLayoutChangeListener? = null
    private val messageBinder = MessageBindingVisitor(lifecycleOwner, chatViewModel, chatItemListener)
    private var recyclerView: RecyclerView? = null

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        this.recyclerView = recyclerView
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        this.recyclerView = null
    }

    override fun getItemViewType(position: Int): Int {
        val message = getItem(position).message
        if (message.isDeleted) {
            return Item.EMPTY.ordinal
        }
        val receiveMark = if (message.isSendByMe) SOURCE_SEND else SOURCE_RECEIVE
        val item = MESSAGE_MAPPING[message.javaClass] ?: Item.UNKNOWN
        return item.ordinal or receiveMark
    }

    override fun getItemId(position: Int): Long {
        return getItem(position).message.mid
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatMessageViewHolder<*> {
        // 检查内存状态，如果内存紧张则执行清理
        if (ChatMemoryManager.shouldPerformCleanup()) {
            TLog.warn(TAG, "Memory usage high, performing cleanup before creating ViewHolder")
            ChatMemoryManager.clearFrescoCache()
            System.gc()
        }

        val inflater = LayoutInflater.from(parent.context)
        val isReceive = viewType and SOURCE_MASK == SOURCE_RECEIVE
        val itemOrdinal = viewType and SOURCE_MASK.inv()
        val item = if (itemOrdinal >= Item.values().size) {
            TLog.error(TAG, "无效的 viewType：$viewType")
            Item.UNKNOWN
        } else {
            Item.values()[itemOrdinal]
        }

        val dataBinding = try {
            item.dataBindingFactory(inflater, parent, isReceive)
        } catch (e: OutOfMemoryError) {
            TLog.error(TAG, "OOM when creating ViewHolder, performing emergency cleanup", e)
            ChatMemoryManager.performMemoryCleanup()
            // 重试一次
            item.dataBindingFactory(inflater, parent, isReceive)
        }

        (dataBinding.root as? ViewGroup)?.let {
            it.clipChildren = false
            it.clipToPadding = false
        }
        TLog.debug(TAG, "onCreateViewHolder")
        return ChatMessageViewHolder(dataBinding) { view, position ->
            val message = getItem(position).message
            chatItemListener.onMessageContainerClick(view, message, position)
        }.also {
            it.itemView.addOnLayoutChangeListener { v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
                onItemLayoutChanged?.onLayoutChange(v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom)
            }
        }
    }

    override fun onBindViewHolder(holder: ChatMessageViewHolder<*>, position: Int) {
        val vdb = holder.dataBinding
        val item = getItem(position) ?: return
        (vdb.root as? InterceptConstraintLayout)?.let {
            it.isIntercept = chatViewModel.isInMsgMultipleSelect
        }
        val itemOrdinal = holder.itemViewType and SOURCE_MASK.inv()
        val itemEnum = if (itemOrdinal >= Item.values().size) {
            Item.UNKNOWN
        } else {
            Item.values()[itemOrdinal]
        }
        val isSendLayout = holder.itemViewType and SOURCE_MASK == SOURCE_SEND
        TLog.debug(TAG, "onBindViewHolder,position:$position,type:${itemEnum.messageType},sendLayout:$isSendLayout")
        if (item.message.isSendByMe == isSendLayout && itemEnum.messageType.isAssignableFrom(item.message.javaClass)) {
            messageBinder.bind(chatMessageViewModel, holder, item)
            vdb.executePendingBindings()
        } else {
            TLog.error(
                TAG,
                RuntimeException("无法绑定不相容的视图和消息"),
                "${item.message::class.java.simpleName} 无法与 ${holder.dataBinding.javaClass.name} 绑定"
            )
        }
    }

    override fun onViewRecycled(holder: ChatMessageViewHolder<*>) {
        super.onViewRecycled(holder)
        try {
            // 清理DataBinding生命周期
            holder.dataBinding.lifecycleOwner = null

            // 清理消息绑定资源
            messageBinder.recycle(holder)

            // 强制执行待处理的绑定以释放资源
            holder.dataBinding.executePendingBindings()

            TLog.debug(TAG, "ViewHolder recycled successfully: ${holder.javaClass.simpleName}")
        } catch (e: Exception) {
            TLog.error(TAG, "Error recycling ViewHolder", e)
        }
    }

    fun getVisibleMessagesWithPos(): List<Pair<ChatMessage, Int>> {
        return recyclerView?.children?.filter {
            val parentHeight = recyclerView?.height ?: -1
            if (parentHeight <= 0) return@filter false
            it.top in 0..parentHeight || it.bottom in 0..parentHeight || parentHeight in it.top..it.bottom
        }?.mapNotNull {
            recyclerView.findMessageBindWithPos(it, this)
        }?.toList() ?: emptyList()
    }

    fun findMessageBindAt(itemView: View?): ChatMessage? {
        return itemView?.let { recyclerView.findMessageBindTo(it, this) }
    }

    fun getMessageSeqAt(position: Int): Long {
        return if (position in 0 until itemCount) {
            getItem(position).message.seq
        } else 0
    }

    companion object {
        private const val SOURCE_SHIFT = 30
        private const val SOURCE_MASK = 0x3 shl SOURCE_SHIFT
        private const val SOURCE_RECEIVE = 1 shl SOURCE_SHIFT
        private const val SOURCE_SEND = 0
        private val MESSAGE_MAPPING = Item.values().associateBy { it.messageType }
    }
}

class ChatMessageViewHolder<T : ViewDataBinding>(
    val dataBinding: T,
    val onItemClickListener: (view: View, position: Int) -> Unit
) : RecyclerView.ViewHolder(dataBinding.root) {

    init {
        itemView.setOnClickListener {
            val position = bindingAdapterPosition
            if (position != RecyclerView.NO_POSITION) {
                onItemClickListener(it, position)
            }
        }
    }
}

internal class MessageBindingVisitor(
    private val lifecycleOwner: LifecycleOwner,
    private val viewModel: ChatBaseViewModel,
    private val listener: ChatItemListener
) : Visitor {
    private val lifecycleScope = lifecycleOwner.lifecycleScope
    private val fontScale = ServiceManager.getInstance().settingService.fontScale

    private lateinit var dataBinding: ViewDataBinding
    private lateinit var messageQuote: LiveData<QuoteMessageInfo>
    private var messageVm: ChatMessageViewModel? = null

    private fun <T> maybeObserveZhishuPreview(
        zhishuPreview: ViewStubProxy,
        message: T,
        previewFlow: Flow<ZhishuDocumentEntity?>?,
    ) where T : ChatMessage, T : MessageWithHypertext {
        (dataBinding.root.getTag(R.id.zhishu_preview) as? Job)?.cancel()
        val zhishuLinks = message.zhishuLinks
        val url = if (zhishuLinks.size == 1) zhishuLinks[0].referenceContent else null
        val preview = previewFlow ?: flowOf(null)
        val job = preview
            .onEach { showZhishuPreviewInReceive(zhishuPreview, message, url, it) }
            .launchIn(lifecycleScope)
        dataBinding.root.setTag(R.id.zhishu_preview, job)
    }

    private fun showZhishuPreviewInReceive(
        stub: ViewStubProxy, message: ChatMessage, url: String?, entity: ZhishuDocumentEntity?
    ) {
        if (entity == null || entity.status != 0 && entity.myPermission == 0) {
            if (stub.isInflated && stub.root.isVisible) {
                stub.root.isVisible = false
            }
            return
        }
        if (stub.isInflated.not()) {
            stub.viewStub?.inflate()
        }
        val view = stub.root as ZhishuPreviewLayout
        view.setPreview(ReceiverPreviewModel(
            entity.uniqueId, entity.previewUrl, "你", entity.myPermission
        ))
        view.setOnClickListener { v: View ->
            if (ServiceManager.getInstance().securityService.isForbidden(BusinessModuleEnum.ZHI_SHU)) {//直书被禁用
                ToastUtils.failure(R.string.no_permission_access)
                return@setOnClickListener
            }
            if (url != null) {
                AppUtil.startUri(v.context, url)
                Analytics.point("zhishu-card-operation") {
                    put("type", 1)
                    put("chat_type", if (message.type == 1) "single" else "group")
                    put("chat_id", message.chatId)
                    put("zhishu_id", entity.uniqueId)
                }
            }
        }
        view.setOnLongClickListener { v: View? ->
            listener.onMessageLongClick(v, message)
            true
        }
    }

    private fun <T> maybeObserveZhishuPreview(
        zhishuPreview: ViewStubProxy,
        message: T,
        previewFlow: Flow<ZhishuDocumentEntity?>?,
        permissionFlow: Flow<ZhishuPermissionEntity?>?
    ) where T : ChatMessage, T : MessageWithHypertext {
        (dataBinding.root.getTag(R.id.zhishu_preview) as? Job)?.cancel()
        val zhishuLinks = message.zhishuLinks
        val url = if (zhishuLinks.size == 1) zhishuLinks[0].referenceContent else null
        val preview = previewFlow ?: flowOf(null)
        val permission = permissionFlow ?: flowOf(null)
        val job = preview.combine(permission, ::Pair)
            .onEach { (a, b) -> showZhishuPreviewInSend(zhishuPreview, message, url, a, b) }
            .launchIn(lifecycleScope)
        dataBinding.root.setTag(R.id.zhishu_preview, job)
    }

    private fun showZhishuPreviewInSend(
        stub: ViewStubProxy, message: ChatMessage, url: String?, document: ZhishuDocumentEntity?, accessControl: ZhishuPermissionEntity?
    ) {
        if (document == null) {
            if (stub.isInflated && stub.root.isVisible) {
                stub.root.isVisible = false
            }
            return
        }
        val displayName = if (message.type == 2) {
            "本群成员"
        } else {
            val contact = ServiceManager.getInstance().contactService.getContactById(message.chatId)
            if (contact == null) "用户" else contact.chatShowName
        }
        val previewModel = SenderPreviewModel(
            document.uniqueId, document.previewUrl, displayName, accessControl?.permissions, accessControl?.assigned ?: 0, message
        )
        if (stub.isInflated.not()) {
            stub.viewStub?.inflate()
        }
        val view = stub.root as ZhishuPreviewLayout
        view.setPreview(previewModel)
        view.setOnClickListener { v: View ->
            if (ServiceManager.getInstance().securityService.isForbidden(BusinessModuleEnum.ZHI_SHU)) {//直书被禁用
                ToastUtils.failure(R.string.no_permission_access)
                return@setOnClickListener
            }
            if (url != null) {
                AppUtil.startUri(v.context, url)
                Analytics.point("zhishu-card-operation") {
                    put("type", 1)
                    put("chat_type", if (message.type == 1) "single" else "group")
                    put("chat_id", message.chatId)
                    put("zhishu_id", document.uniqueId)
                }
            }
        }
        view.setOnLongClickListener { v: View? ->
            listener.onMessageLongClick(v, message)
            true
        }
    }

    private fun maybeObserveHyperlinkPreview(hyperlinkPreview: ViewStubProxy, message: ChatMessage, flow: Flow<Hypertext?>?) {
        (dataBinding.root.getTag(R.id.hyperlink_preview) as? Job)?.cancel()
        val job = (flow ?: flowOf(null))
            .onEach { showHyperlinkPreview(hyperlinkPreview, message, it) }
            .launchIn(lifecycleScope)
        dataBinding.root.setTag(R.id.hyperlink_preview, job)
    }

    private fun showHyperlinkPreview(stub: ViewStubProxy, message: ChatMessage, entity: Hypertext?) {
        if (entity == null) {
            if (stub.isInflated && stub.root.isVisible) {
                stub.root.isVisible = false
            }
            return
        }
        if (stub.isInflated.not()) {
            stub.viewStub?.inflate()
        }
        val view = stub.root as HyperlinkPreviewLayout
        if (!entity.imagePreview?.url.isNullOrBlank()) {
            view.setPreview(entity.imagePreview!!)
        } else if (!entity.textPreview?.title.isNullOrBlank()) {
            view.setPreview(
                entity.textPreview!!,
                if (entity.type == 2) 1 else 2, // 普通链接的预览只展示一行标题，其它链接预览展示二行
                if (entity.type == 2) 2 else 5, // 普通链接的预览只展示二行描述，其它链接预览展示五行描述
            )
        } else {
            view.visibility = View.GONE
            return
        }
        view.setOnClickListener {
            listener.onInlineLinkClick(
                it, message, if (entity.destinationLink.isNullOrBlank()) entity.referenceContent else entity.destinationLink
            )
        }
        view.setOnLongClickListener {
            listener.onMessageLongClick(it, message)
            true
        }
    }

    private fun maybeShowQuickReply(quickReply: ViewStubProxy, message: ChatMessageWithAt) {
        message.updateAtStatusProcessed()
        val visible = message.atStatusProcessed.not()
        if (visible) {
            if (quickReply.isInflated.not()) {
                quickReply.viewStub?.inflate()
            }
            if (!quickReply.root.isVisible) {
                quickReply.root.isVisible = true
            }
            (quickReply.binding as? ChatLayoutQuickReplyBinding)?.run {
                msg = message
                callback = listener
                val emojiReply = when (message.atMeMethod) {
                    AtMethod.AT_USER -> true
                    else -> false
                }
                divider.isVisible = emojiReply
                tvReply.isVisible = emojiReply
                ChatBindingAdapter.showEmotionText(tvReceived, message.atQuickReplyText, (16 * fontScale).toInt())
            }
        } else {
            if (quickReply.isInflated && quickReply.root.isVisible) {
                quickReply.root.isVisible = false
            }
        }
    }

    override fun visit(message: MessageForText) {
        if (message.isSendByMe) {
            with((dataBinding as ChatItemMessageTextSendBinding).messageContent) {
                messageQuote.quote = <EMAIL>
                textLayout.fontScale = fontScale
                textLayoutTranslate.fontScale = fontScale
                maybeObserveZhishuPreview(zhishuPreview, message, messageVm?.zhishuPreviewFlow(message), messageVm?.zhishuPermissionFlow(message))
                maybeObserveHyperlinkPreview(hyperlinkPreview, message, messageVm?.hyperlinkPreviewFlow(message))
            }
        } else {
            with((dataBinding as ChatItemMessageTextReceiveBinding).messageContent) {
                messageQuote.quote = <EMAIL>
                tvFromText.fontScale = fontScale
                textLayoutTranslate.fontScale = fontScale
                maybeObserveZhishuPreview(zhishuPreview, message, messageVm?.zhishuPreviewFlow(message))
                maybeObserveHyperlinkPreview(hyperlinkPreview, message, messageVm?.hyperlinkPreviewFlow(message))
                maybeShowQuickReply(quickReplyStub, message)
            }
        }
    }

    override fun visit(message: MessageForRichText) {
        if (message.isSendByMe) {
            with((dataBinding as ChatItemMessageRichTextSendBinding).messageContent) {
                messageQuote.quote = <EMAIL>
                textLayout.fontScale = fontScale
                maybeObserveZhishuPreview(zhishuPreview, message, messageVm?.zhishuPreviewFlow(message), messageVm?.zhishuPermissionFlow(message))
                maybeObserveHyperlinkPreview(hyperlinkPreview, message, messageVm?.hyperlinkPreviewFlow(message))
            }
        } else {
            with((dataBinding as ChatItemMessageRichTextReceiveBinding).messageContent) {
                messageQuote.quote = <EMAIL>
                textLayout.fontScale = fontScale
                maybeObserveZhishuPreview(zhishuPreview, message, messageVm?.zhishuPreviewFlow(message))
                maybeObserveHyperlinkPreview(hyperlinkPreview, message, messageVm?.hyperlinkPreviewFlow(message))
                maybeShowQuickReply(quickReplyStub, message)
            }
        }
    }

    override fun visit(message: MessageForMarkdownText) {
        if (message.isSendByMe) {
            with((dataBinding as ChatItemMessageMarkdownTextSendBinding).messageContent) {
                messageQuote.quote = <EMAIL>
                textLayout.fontScale = fontScale
                textLayoutTranslate.fontScale = fontScale
                maybeObserveZhishuPreview(zhishuPreview, message, messageVm?.zhishuPreviewFlow(message), messageVm?.zhishuPermissionFlow(message))
                maybeObserveHyperlinkPreview(hyperlinkPreview, message, messageVm?.hyperlinkPreviewFlow(message))
            }
        } else {
            with((dataBinding as ChatItemMessageMarkdownTextReceiveBinding).messageContent) {
                messageQuote.quote = <EMAIL>
                textLayout.fontScale = fontScale
                textLayoutTranslate.fontScale = fontScale
                maybeObserveZhishuPreview(zhishuPreview, message, messageVm?.zhishuPreviewFlow(message))
                maybeObserveHyperlinkPreview(hyperlinkPreview, message, messageVm?.hyperlinkPreviewFlow(message))
                maybeShowQuickReply(quickReplyStub, message)
            }
        }
    }

    override fun visit(message: MessageForPic) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessagePicSendBinding)
                .messageContent.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessagePicReceiveBinding)
                .messageContent.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForUserCard) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageUserCardSendBinding)
                .userCardCl.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageUserCardReceiveBinding)
                .cardLl.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForVideo) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageVideoSendBinding)
                .messageContent.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageVideoReceiveBinding)
                .messageContent.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForAudio) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageAudioSendBinding)
                .audioLl.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageAudioReceiveBinding)
                .audioLl.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForVideoMeetingCard) {
        // 不支持回复
        val layoutVideoMeeting = if (message.isSendByMe) {
            (dataBinding as ChatItemMessageVideoMeetingCardSendBinding).layoutVideoMeeting
        } else {
            (dataBinding as ChatItemMessageVideoMeetingCardReceiveBinding).layoutVideoMeeting
        }
        layoutVideoMeeting.observeButtonStr(message)
    }

    override fun visit(message: MessageForFile) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageFileSendBinding)
                .messageContent.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageFileReceiveBinding)
                .messageContent.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForSticker) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageStickerSendBinding)
                .sdvStickerSend.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageStickerReceiveBinding)
                .sdvStickerReceive.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForChatShare) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageChatShareSendBinding)
                .messageContent.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageChatShareReceiveBinding)
                .messageContent.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForRedEnvelope) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageRedEnvelopSendBinding)
                .messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageRedEnvelopReceiveBinding)
                .messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForLink) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageLinkSendBinding)
                .layoutLink.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageLinkReceiveBinding)
                .layoutLink.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForImageCard) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageImageCardSendBinding)
                .layoutImageCard.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageImageCardReceiveBinding)
                .layoutImageCard.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForLinkCall) {
        val drawableRes: Int = if (message.linkType == Constants.VIDEO_CHAT_AUDIO) {
            R.drawable.ic_link_call_audio
        } else {
            R.drawable.ic_link_call_video
        }
        if (message.isSendByMe) {
            val binding = dataBinding as ChatItemMessageLinkCallSendBinding
            with(binding.messageContent) {
                messageQuote.quote = <EMAIL>
                tvToText.setCompoundDrawables(null, null, tvToText.getScaledDrawable(drawableRes), null)
            }
        } else {
            val binding = dataBinding as ChatItemMessageLinkCallReceiveBinding
            with(binding.layoutText) {
                messageQuote.quote = <EMAIL>
                tvFromText.setCompoundDrawables(null, null, tvFromText.getScaledDrawable(drawableRes), null)
            }
        }
    }

    private fun View.getScaledDrawable(drawableRes: Int): Drawable? {
        return context.getDrawable(drawableRes)?.also {
            val width = (it.intrinsicWidth * fontScale).toInt()
            val height = (it.intrinsicHeight * fontScale).toInt()
            it.setBounds(0, 0, width, height)
        }
    }

    override fun visit(message: MessageForOnlineFile) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageOnlineFileSendBinding)
                .userOnlineFileCl.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageOnlineFileReceiveBinding)
                .userOnlineFileCl.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForGroupCard) {
    }

    override fun visit(message: MessageForAppCard) {
    }

    override fun visit(message: MessageForAction) {
    }

    override fun visit(message: MessageForHint) {
    }

    override fun visit(message: MessageForExtensionCard) {
        if (message.isSendByMe) {
            (dataBinding as ChatItemMessageExtensionCardSendBinding)
                .messageContent.messageQuote.quote = messageQuote
        } else {
            (dataBinding as ChatItemMessageExtensionCardReceiveBinding)
                .messageContent.messageQuote.quote = messageQuote
        }
    }

    override fun visit(message: MessageForEmpty) {
    }

    override fun visit(message: MessageForTaskCommentCard) {
    }

    override fun visit(message: MessageForSystemCard) {
    }

    override fun visit(message: MessageForUnknown) {
    }

    fun bind(
        messageViewModel: ChatMessageViewModel?,
        holder: ChatMessageViewHolder<*>,
        item: MessageAndQuote
    ) {
        messageVm = messageViewModel
        dataBinding = holder.dataBinding
        dataBinding.setVariable(BR.msg, item.message)
        dataBinding.setVariable(BR.viewModel, viewModel)
        dataBinding.setVariable(BR.listener, listener)
        (viewModel as? GroupChatBaseViewModel)?.updateContactChatName(item.message)
        messageQuote = viewModel.getQuoteMessageInfoLiveData(item)
        item.message.accept(this)
        dataBinding.lifecycleOwner = lifecycleOwner
    }

    fun recycle(holder: ChatMessageViewHolder<*>) {
        try {
            // 取消协程任务
            (holder.dataBinding.root.getTag(R.id.zhishu_preview) as? Job)?.cancel()
            (holder.dataBinding.root.getTag(R.id.hyperlink_preview) as? Job)?.cancel()

            // 清理View标签
            holder.dataBinding.root.setTag(R.id.zhishu_preview, null)
            holder.dataBinding.root.setTag(R.id.hyperlink_preview, null)

            // 清理Fresco图片资源
            clearFrescoResources(holder.dataBinding.root)

            TLog.debug(TAG, "MessageBindingVisitor recycled successfully")
        } catch (e: Exception) {
            TLog.error(TAG, "Error in MessageBindingVisitor recycle", e)
        }
    }

    /**
     * 递归清理ViewGroup中的Fresco资源
     */
    private fun clearFrescoResources(view: View) {
        try {
            // 使用ChatMemoryManager的统一清理方法
            ChatMemoryManager.clearFrescoResourcesInView(view)
        } catch (e: Exception) {
            TLog.error(TAG, "Error clearing Fresco resources", e)
        }
    }

}

internal enum class Item(
    val messageType: Class<out ChatMessage>,
    val dataBindingFactory: (inflater: LayoutInflater, parent: ViewGroup, isReceive: Boolean) -> ViewDataBinding
) {
    UNKNOWN(ChatMessage::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageNoTypeReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageNoTypeSendBinding.inflate(inflater, parent, false)
    }),
    TEXT(MessageForText::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageTextReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageTextSendBinding.inflate(inflater, parent, false)
    }),
    PICTURE(MessageForPic::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessagePicReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessagePicSendBinding.inflate(inflater, parent, false)
    }),
    USER_CARD(MessageForUserCard::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageUserCardReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageUserCardSendBinding.inflate(inflater, parent, false)
    }),
    VIDEO(MessageForVideo::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageVideoReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageVideoSendBinding.inflate(inflater, parent, false)
    }),
    AUDIO(MessageForAudio::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageAudioReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageAudioSendBinding.inflate(inflater, parent, false)
    }),
    VIDEO_MEETING(MessageForVideoMeetingCard::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageVideoMeetingCardReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageVideoMeetingCardSendBinding.inflate(inflater, parent, false)
    }),
    FILE(MessageForFile::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageFileReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageFileSendBinding.inflate(inflater, parent, false)
    }),
    STICKER(MessageForSticker::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageStickerReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageStickerSendBinding.inflate(inflater, parent, false)
    }),
    CHAT_SHARE(MessageForChatShare::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageChatShareReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageChatShareSendBinding.inflate(inflater, parent, false)
    }),
    RED_ENVELOP(MessageForRedEnvelope::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageRedEnvelopReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageRedEnvelopSendBinding.inflate(inflater, parent, false)
    }),
    LINK(MessageForLink::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageLinkReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageLinkSendBinding.inflate(inflater, parent, false)
    }),
    RICH_TEXT(MessageForRichText::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageRichTextReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageRichTextSendBinding.inflate(inflater, parent, false)
    }),
    MARKDOWN_TEXT(MessageForMarkdownText::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageMarkdownTextReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageMarkdownTextSendBinding.inflate(inflater, parent, false)
    }),
    IMAGE(MessageForImageCard::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageImageCardReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageImageCardSendBinding.inflate(inflater, parent, false)
    }),
    LINK_CALL(MessageForLinkCall::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageLinkCallReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageLinkCallSendBinding.inflate(inflater, parent, false)
    }),
    ONLINE_FILE(MessageForOnlineFile::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageOnlineFileReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageOnlineFileSendBinding.inflate(inflater, parent, false)
    }),
    GROUP_CARD(MessageForGroupCard::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageGroupCardReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageGroupCardSendBinding.inflate(inflater, parent, false)
    }),
    APP_CARD(MessageForAppCard::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageAppCardReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageAppCardSendBinding.inflate(inflater, parent, false)
    }),
    HINT(MessageForHint::class.java, { inflater, parent, _ ->
        ChatItemMessageHintBinding.inflate(inflater, parent, false)
    }),
    EXTENSION_CARD(MessageForExtensionCard::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageExtensionCardReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageExtensionCardSendBinding.inflate(inflater, parent, false)
    }),
    TASK_COMMENT(MessageForTaskCommentCard::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageTaskCommentCardReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageTaskCommentCardSendBinding.inflate(inflater, parent, false)
    }),
    SYSTEM_CARD(MessageForSystemCard::class.java, { inflater, parent, isReceive ->
        if (isReceive) ChatItemMessageSystemCardReceiveBinding.inflate(inflater, parent, false)
        else ChatItemMessageSystemCardSendBinding.inflate(inflater, parent, false)
    }),
    EMPTY(MessageForEmpty::class.java, { inflater, parent, _ ->
        ChatItemMessageNoneBinding.inflate(inflater, parent, false)
    });
}

class MessageDividerDecorator(
    private val adapter: ChatMessageAdapter,
    private val itemVerticalMargin: Int = 10.dp.toInt()
) : RecyclerView.ItemDecoration() {

    private val fontScale = ServiceManager.getInstance().settingService.fontScale
    private val timeTextPadding = 8.dp
    private val timePaint = Paint(Paint.ANTI_ALIAS_FLAG)
        .apply {
            textSize = 12.sp * fontScale
            color = 0xFF9999A3.toInt()
        }
    private val highlightPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val animator = ValueAnimator.ofArgb(0x00E5E5E7, 0xFFE5E5E7.toInt())
        .apply {
            duration = 600L
            repeatCount = 1
            repeatMode = ValueAnimator.REVERSE
            interpolator = AccelerateDecelerateInterpolator()
            doOnEnd { highlightMid = 0 }
        }
    private var recyclerView: RecyclerView? = null
    private var highlightMid: Long = 0

    fun highlightMessage(mid: Long) {
        TLog.info(TAG, "需要高亮的消息：$mid, 当前高亮的消息：$highlightMid")
        if (highlightMid != mid) {
            highlightMid = mid
            animator.start()
            recyclerView?.postInvalidate()
        }
    }

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        super.getItemOffsets(outRect, view, parent, state)
        recyclerView = parent
        if (view.isVisible) {
            outRect.top += itemVerticalMargin
            outRect.bottom += itemVerticalMargin
            val position = parent.findAdapterPosition(view, adapter)
            if (isDrawTimePosition(position)) {
                outRect.top += (timeTextPadding * 2 + timePaint.fontSpacing).toInt()
            }
        }
    }

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(c, parent, state)
        parent.walkthrough {
            if (it.width > 0) {
                val position = parent.findAdapterPosition(it, adapter)
                if (isDrawTimePosition(position)) {
                    val timestamp = adapter.getTimeAt(position)
                    c.drawTimeInView(timestamp, it)
                }
            }
        }
        parent.findChild {
            val position = parent.findAdapterPosition(it, adapter)
            isHighlightPosition(position)
        }?.let {
            val currentColor = animator.animatedValue as Int
            highlightPaint.color = currentColor
            c.drawRect(it.x, it.y - itemVerticalMargin, it.x + it.width, it.y + it.height + itemVerticalMargin, highlightPaint)
            parent.postInvalidateOnAnimation()
        } ?: run {
            if (animator.isRunning) {
                TLog.info(TAG, "未找到高亮的消息，主动取消动画")
                animator.cancel()
            }
        }
    }

    private fun isHighlightPosition(position: Int): Boolean {
        if (highlightMid == 0L) {
            return false
        }
        if (position in 0 until adapter.itemCount) {
            return adapter.currentList[position].message.mid == highlightMid
        }
        return false
    }

    private fun isDrawTimePosition(position: Int): Boolean {
        if (position == 0) {
            return true
        }
        if (position in 1 until adapter.itemCount) {
            val previous = position - 1
            val previousTime = adapter.getTimeAt(previous)
            val currentTime = adapter.getTimeAt(position)
            if (currentTime - previousTime > MessageConstants.MSG_SHOW_TIME_INTERVAL) {
                return true
            }
        }
        return false
    }

    private fun ChatMessageAdapter.getTimeAt(index: Int): Long {
        return currentList[index].message.time
    }

    private fun Canvas.drawTimeInView(timestamp: Long, it: View) {
        val timeString = ChatBindingAdapter.formatMessageTime(timestamp)
        val textWidth = timePaint.measureText(timeString)
        val x = (it.width - textWidth) / 2
        val y = it.y - itemVerticalMargin - timeTextPadding
        drawText(timeString, x, y, timePaint)
    }
}

class SupplementMessageDecorator(
    private val adapter: ChatMessageAdapter,
    private val anchorMessageSeq: () -> Long,
    private val displayText: () -> String,
) : RecyclerView.ItemDecoration() {
    private val verticalPadding = 8.dp
    private val decorationPadding = 16.dp
    private val decoratorSize = 48.dp
    private val fontMetrics = Paint.FontMetricsInt()
    private val fontScale = ServiceManager.getInstance().settingService.fontScale
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        textSize = 13.sp * fontScale
        color = 0xFFB1B1B8.toInt()
    }
    private val viewBounds = Rect()
    private var recyclerView: RecyclerView? = null

    fun attachToRecyclerView(recyclerView: RecyclerView) {
        if (this.recyclerView == recyclerView) {
            return
        }
        detachFromRecyclerView()
        this.recyclerView = recyclerView
        recyclerView.addItemDecoration(this)
        textPaint.getFontMetricsInt(fontMetrics)
    }

    fun detachFromRecyclerView() {
        this.recyclerView?.removeItemDecoration(this)
        this.recyclerView = null
    }

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        super.getItemOffsets(outRect, view, parent, state)
        if (anchorMessageSeq() > 0 && displayText().isNotBlank()) {
            val currentItemPosition = parent.findAdapterPosition(view, adapter)
            val previousItemPosition = currentItemPosition - 1
            val previousSeq = adapter.getMessageSeqAt(previousItemPosition)
            val previousMatch = 0 < previousSeq && previousSeq <= anchorMessageSeq()
            val candidateSeq = adapter.getMessageSeqAt(currentItemPosition)
            val currentMatch = candidateSeq > anchorMessageSeq()
            if (previousMatch && currentMatch) {
                outRect.top = (verticalPadding * 2 + textPaint.fontSpacing).toInt()
            }
        }
    }

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(c, parent, state)
        val anchorSeq = anchorMessageSeq()
        if (anchorSeq > 0 && displayText().isNotBlank()) {
            parent
                .findChild {
                    val position = parent.findAdapterPosition(it, adapter)
                    val previousSeq = adapter.getMessageSeqAt(position - 1)
                    previousSeq in 1..anchorSeq
                            && adapter.getMessageSeqAt(position) > anchorSeq
                }?.let {
                    parent.getDecoratedBoundsWithMargins(it, viewBounds)
                    val textWidth = textPaint.measureText(displayText())
                    val x = (it.width - textWidth) / 2
                    val y = viewBounds.top + it.translationY + verticalPadding + textPaint.fontSpacing
                    c.drawText(displayText(), x, y, textPaint)
                    val decoratorY = y + (fontMetrics.bottom + fontMetrics.top) / 2
                    val stopX = x - decorationPadding
                    val startX = x - decorationPadding - decoratorSize
                    c.drawLine(startX, decoratorY, stopX, decoratorY, textPaint)
                    val startX2 = x + textWidth + decorationPadding
                    val stopX2 = x + textWidth + decorationPadding + decoratorSize
                    c.drawLine(startX2, decoratorY, stopX2, decoratorY, textPaint)
                }
        }
    }
}

class MessageAiSummaryDecorator(
    var showItemDecorator: Boolean,
    var textShow: String = "",
    val chatAdapter: ChatMessageAdapter,
    val callback: (() -> Unit)? = null
) : RecyclerView.ItemDecoration(), RecyclerView.OnItemTouchListener {
    var messageStub: MessageStub? = null
    private val fontScale = ServiceManager.getInstance().settingService.fontScale

    private val textPaint = Paint().apply {
        color = ProcessHelper.getContext().getColor(R.color.color_9999A3)
        textSize = 13.sp * fontScale
    }
    private val openSummaryPaint = Paint().apply {
        color = ThemeUtils.getThemeTextColorInt()
        textSize = 13.sp * fontScale
    }

    private val fixedText = ProcessHelper.getContext().getString(R.string.chat_open_message_ai_summary)

    private val textShowBounds = Rect()
    private val fixedTextBounds = Rect()

    var height = 0
    var remainingWidth = 0f
    var fixedTextWidth = 0
    var textShowWidth = 0
    var baseLine = 0f

    private var disallowIntercept = false
    private var anchorPosition = -1

    var touchStartTime = 0L
    var recyclerView: RecyclerView? = null

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(c, parent, state)
        val stub = messageStub
        if (showItemDecorator && stub != null) {
            textPaint.getTextBounds(textShow, 0, textShow.length, textShowBounds)
            openSummaryPaint.getTextBounds(fixedText, 0, fixedText.length, fixedTextBounds)
            textShowWidth = textShowBounds.right - textShowBounds.left
            fixedTextWidth = fixedTextBounds.right - fixedTextBounds.left
            remainingWidth = (parent.width - textShowWidth - fixedTextWidth) / 2.0f
            baseLine = height + 16.dp
            for (i in 0 until parent.childCount) {
                parent.getChildAt(i)?.also {
                    val position = parent.findAdapterPosition(it, chatAdapter)
                    val seq = chatAdapter.getMessageSeqAt(position)
                    val nextSeq = chatAdapter.getMessageSeqAt(position + 1)
                    val seqBetweenPreAndNextRange = stub.seq in (seq + 1) until nextSeq
                    val seqInEnd = (position == chatAdapter.itemCount - 1 && nextSeq == 0L && seq < stub.seq)
                    if (seq == stub.seq || seqBetweenPreAndNextRange || seqInEnd) {
                        c.drawText(textShow, remainingWidth, it.y + it.measuredHeight + baseLine, textPaint)
                        c.drawText(fixedText, remainingWidth + textShowWidth + 4.dp, it.y + it.measuredHeight + baseLine, openSummaryPaint)
                        return
                    }
                }
            }
        }
    }

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        super.getItemOffsets(outRect, view, parent, state)
        val stub = messageStub
        if (showItemDecorator && stub != null) {
            val position = parent.findAdapterPosition(view, chatAdapter)
            val seq = chatAdapter.getMessageSeqAt(position)
            val nextSeq = chatAdapter.getMessageSeqAt(position + 1)
            val seqBetweenPreAndNextRange = stub.seq in (seq + 1) until nextSeq
            val seqInEnd = (position == chatAdapter.itemCount - 1 && nextSeq == 0L && seq < stub.seq)
            if (seq == stub.seq || seqBetweenPreAndNextRange || seqInEnd) {
                anchorPosition = position
                val fontMetrics = Paint.FontMetrics()
                textPaint.getFontMetrics(fontMetrics)
                height = (fontMetrics.bottom - fontMetrics.top).toInt()
                outRect.set(0, 0, 0, height + 16.dp.toInt())
                return
            }
        }
    }

    fun attachToRecyclerView(recyclerView: RecyclerView) {
        if (recyclerView == this.recyclerView) {
            return
        }
        this.recyclerView?.let {
            it.removeItemDecoration(this)
            it.removeOnItemTouchListener(this)
        }
        this.recyclerView = recyclerView

        recyclerView.addItemDecoration(this)
        recyclerView.addOnItemTouchListener(this)
    }

    override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
        if (disallowIntercept) {
            return false
        }
        return when (e.action) {
            MotionEvent.ACTION_DOWN -> {
                touchStartTime = System.currentTimeMillis()
                return isTouchAnchorArea(rv, e)
            }

            else -> false
        }
    }

    private fun isTouchAnchorArea(rv: RecyclerView, e: MotionEvent): Boolean {
        val child = rv.findChild {
            rv.findAdapterPosition(it, chatAdapter) == anchorPosition
        }
        return child?.let {
            var intercept = false
            val x = e.x
            val y = e.y
            val startX = remainingWidth + textShowWidth
            val startY = it.y + it.measuredHeight
            if (x >= startX && x <= (startX + fixedTextWidth)) {
                if (y >= startY && y <= startY + baseLine + 16.dp) {
                    intercept = true
                }
            }
            intercept
        } ?: false
    }

    override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
        if (e.action == MotionEvent.ACTION_UP && isTouchAnchorArea(rv, e)) {
            if (System.currentTimeMillis() - touchStartTime <= 200) {
                callback?.invoke()
            }
        }
    }

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
        this.disallowIntercept = disallowIntercept
    }
}

class GroupChatShareDecorator(
    private val adapter: ChatMessageAdapter,
    private val miniSeq: Long,
    private val displayText: CharSequence
) : RecyclerView.ItemDecoration(), RecyclerView.OnItemTouchListener {
    private val verticalPadding = 10.dp
    private val horizontalPadding = 40.dp
    private val fontMetrics = Paint.FontMetrics()
    private val fontScale = ServiceManager.getInstance().settingService.fontScale
    private val textPaint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        textSize = 13.sp * fontScale
        color = 0xFF5D68E8.toInt()
    }
    private val indexOfMiniSeq: Int
        get() {
            if (adapter.getMessageSeqAt(0) == miniSeq) {
                return 0
            }
            return -1
        }

    private var recyclerView: RecyclerView? = null
    private var staticLayout: StaticLayout? = null
    private var disallowIntercept: Boolean = false
    var onClickListener: View.OnClickListener? = null

    fun attachToRecyclerView(recyclerView: RecyclerView) {
        if (this.recyclerView == recyclerView) {
            return
        }
        this.recyclerView?.let {
            it.removeItemDecoration(this)
            it.removeOnItemTouchListener(this)
        }
        this.recyclerView = recyclerView
        this.recyclerView?.let {
            it.addItemDecoration(this)
            it.addOnItemTouchListener(this)
        }
        recyclerView.doOnLayout {
            textPaint.getFontMetrics(fontMetrics)
            @Suppress("DEPRECATION")
            staticLayout = StaticLayout(
                displayText,
                textPaint,
                (it.width - horizontalPadding * 2).toInt(),
                Layout.Alignment.ALIGN_CENTER,
                1.0f,
                0f,
                true
            )
        }
    }

    fun detachFromRecyclerView() {
        this.recyclerView?.let {
            it.removeItemDecoration(this)
            it.removeOnItemTouchListener(this)
        }
        this.recyclerView = null
        this.staticLayout = null
        this.onClickListener = null
    }

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        super.getItemOffsets(outRect, view, parent, state)
        val index = indexOfMiniSeq
        if (index < 0) return
        if (parent.findAdapterPosition(view, adapter) == index) {
            val textHeight = staticLayout?.height ?: 0
            outRect.bottom = (verticalPadding + textHeight).toInt()
        }
    }

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(c, parent, state)
        val index = indexOfMiniSeq
        if (index < 0) return
        parent
            .findChild { parent.findAdapterPosition(it, adapter) == index }
            ?.let { view ->
                val x = view.x + horizontalPadding
                val y = view.y + view.height + verticalPadding + fontMetrics.bottom
                c.withTranslation(x, y) { staticLayout?.draw(c) }
            }
    }

    override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
        if (disallowIntercept) {
            return false
        }
        return when (e.actionMasked) {
            // TODO 延迟拦截触摸事件
            MotionEvent.ACTION_DOWN -> isTouchInHotspot(rv, e)
            else -> false
        }
    }

    override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
        if (e.actionMasked == MotionEvent.ACTION_UP) {
            if (isTouchInHotspot(rv, e)) {
                onClickListener?.onClick(rv)
            }
        }
    }

    private fun isTouchInHotspot(rv: RecyclerView, e: MotionEvent): Boolean {
        val index = indexOfMiniSeq
        if (index < 0) return false
        return rv
            .findChild { rv.findAdapterPosition(it, adapter) == index }
            ?.let { view ->
                val left = view.x + horizontalPadding
                val top = view.y + view.height
                val textWidth = staticLayout?.width ?: 0
                val textHeight = staticLayout?.height ?: 0
                val right = left + textWidth
                val bottom = top + textHeight + verticalPadding * 2
                e.x in left..right && e.y in top..bottom
            } ?: false
    }

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
        this.disallowIntercept = disallowIntercept
    }
}
