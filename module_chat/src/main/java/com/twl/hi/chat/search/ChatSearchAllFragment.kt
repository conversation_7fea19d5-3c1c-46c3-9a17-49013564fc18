package com.twl.hi.chat.search

import android.graphics.Rect
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.twl.hi.basic.MESSAGE_RECORD_SEARCH_PAGE
import com.twl.hi.basic.adapter.MyMultiTypeAdapter
import com.twl.hi.basic.api.response.QueryOpenAppLastedResult
import com.twl.hi.basic.dialog.DialogUtils
import com.twl.hi.basic.model.QuickTypeBean
import com.twl.hi.basic.model.SearchContact
import com.twl.hi.basic.model.SearchGroup
import com.twl.hi.basic.model.WebViewBean
import com.twl.hi.basic.util.FilePreviewUtil
import com.twl.hi.basic.util.Scene
import com.twl.hi.basic.util.handleContact
import com.twl.hi.basic.util.handleLocalContact
import com.twl.hi.chat.BR
import com.twl.hi.chat.ChatRecordAllSearchActivity
import com.twl.hi.chat.GroupChatActivity
import com.twl.hi.chat.R
import com.twl.hi.chat.SingleChatActivity
import com.twl.hi.chat.adapter.ChatRecordSearchLastAdapter
import com.twl.hi.chat.adapter.ChatSearchContactAdapter
import com.twl.hi.chat.adapter.ChatSearchHistoryAdapter
import com.twl.hi.chat.adapter.SearchAdapter
import com.twl.hi.chat.api.response.ChatRecordAllSearchResponse
import com.twl.hi.chat.api.response.ChatRecordAllSearchResponse.ChatRecordAllSearchBean
import com.twl.hi.chat.api.response.SearchHistoryResponse
import com.twl.hi.chat.api.response.bean.MailSearchBean
import com.twl.hi.chat.api.response.bean.PluginSearchBean
import com.twl.hi.chat.api.response.bean.ShiMoSearchBean
import com.twl.hi.chat.databinding.ChatFragmentChatSearchAllBinding
import com.twl.hi.chat.message.ArchiveActivity
import com.twl.hi.chat.message.FavoriteActivity
import com.twl.hi.chat.model.ChatRecordAllChatMessage
import com.twl.hi.chat.model.ChatRecordAllSearchServerModel
import com.twl.hi.chat.model.SearchAction
import com.twl.hi.chat.model.SeeAllOptions
import com.twl.hi.chat.search.callback.ChatSearchAllFragmentCallback
import com.twl.hi.chat.search.viewmodel.ChatSearchAllFragmentViewModel
import com.twl.hi.chat.util.ChatSearchUtils
import com.twl.hi.chat.widget.FlowLayoutManager
import com.twl.hi.chat.widget.LinesFlexboxLayoutManager
import com.twl.hi.export.email.router.EmailPageRouter
import com.twl.hi.export.organization.router.OrganizationPageRouter
import com.twl.hi.export.schedule.router.SchedulePageRouter
import com.twl.hi.export.webview.WebViewPageRouter
import com.twl.hi.export.workflow.router.WorkflowPagerRouter
import com.twl.hi.foundation.api.response.bean.ChatRecordAllSearchServerBean
import com.twl.hi.foundation.api.response.bean.ChatRecordDepartBean
import com.twl.hi.foundation.api.response.bean.ChatRecordGroupChatBean
import com.twl.hi.foundation.api.response.bean.ChatRecordLoadingBean
import com.twl.hi.foundation.api.response.bean.ChatRecordSingleChatBean
import com.twl.hi.foundation.api.response.bean.ChatRecordSummaryBean
import com.twl.hi.foundation.api.response.bean.ChatRecordTaskBean
import com.twl.hi.foundation.api.response.bean.FavoriteSearchBean
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.ScheduleBean
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.foundation.model.message.MessageForFile
import com.twl.hi.foundation.utils.PointUtils
import com.twl.hi.foundation.utils.PointUtils.BuilderV4
import com.twl.hi.foundation.utils.point.GlobalSearchPointRecord.globalSearchId
import com.twl.hi.foundation.utils.point.SearchPointUtil
import com.twl.hi.foundation.utils.point.uploadSearchResult
import com.twl.hi.select.bean.SearchItemFeedBack
import com.twl.hi.select.bean.SearchItemMoreComBean
import com.twl.hi.select.bean.SearchItemTitleBean
import com.twl.kzmp.dp
import com.twl.utils.SettingBuilder
import com.twl.utils.StringUtils
import com.twl.utils.jurisdiction.JurisdictionUtils
import hi.kernel.Constants
import kz.log.TLog
import lib.twl.common.callback.BaseCallback
import lib.twl.common.util.AppUtil
import lib.twl.common.util.CommonUtils
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.LList
import lib.twl.common.util.QMUIKeyboardHelper
import lib.twl.common.util.TimeDifferenceUtil
import lib.twl.common.util.TimeTag
import lib.twl.common.util.ToastUtils
import lib.twl.common.views.adapter.BaseQuickAdapter
import lib.twl.common.views.adapter.entity.MultiItemEntity
import lib.twl.common.views.adapter.entity.MultiItemEntityWithId
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2022/08/22 16:57
 *     description:搜索-全部tab
 */
private const val TAG = "ChatSearchAllFragment"

class ChatSearchAllFragment :
    ChatSearchBaseFragment<ChatFragmentChatSearchAllBinding, ChatSearchAllFragmentViewModel>(),
    ChatSearchAllFragmentCallback, MyMultiTypeAdapter.OnItemClickListener {

    private var mPos: Int = 0
    private var mEditContent = ""
    private var mDeleteHistoryDialog: DialogUtils? = null
    private var mDeleteRecordDialog: DialogUtils? = null
    private var adapterSingle: ChatSearchContactAdapter? = null
    private var adapterGroup: ChatRecordSearchLastAdapter? = null

    // 联系人一行 5 列
    private val contactsColumns = 5

    // 最多展示联系人数量
    private val maxShowContactCount = 17

    // 最多展示群聊数量
    private val maxShowGroupCount = 10

    // 联系人列表是否展开，默认折叠
    private var isContactsExpanded = false
    private var tempContacts = mutableListOf<MultiItemEntityWithId<String>>()

    private val mSearchPointUtil by lazy {
        SearchPointUtil()
    }
    private val mHistoryPointUtil by lazy {
        SearchPointUtil()
    }
    private val mLastSinglePointUtil by lazy {
        SearchPointUtil()
    }

    private val mLastGroupPointUtil by lazy {
        SearchPointUtil()
    }
    private val mConversationTitle by lazy {
        getString(R.string.chat_last_search)
    }
    private val mContactTitle by lazy {
        getString(R.string.chat_my_contact)
    }
    private val mGroupTitle by lazy {
        getString(R.string.group_chat)
    }
    private val mDepartmentTitle by lazy {
        getString(R.string.department_group)
    }
    private val mSummaryTitle by lazy {
        getString(R.string.summary)
    }
    private val mShiMoTitle by lazy {
        getString(R.string.chat_shi_mo)
    }
    private val mToDoTitle by lazy {
        getString(R.string.chat_quick_search_task)
    }
    private val mFavoriteTitle by lazy {
        getString(R.string.chat_add_favorite)
    }
    private val mPluginTitle by lazy {
        getString(R.string.chat_app)
    }
    private val mScheduleTitle by lazy {
        getString(R.string.chat_quick_search_schedule)
    }
    private val mFileTitle by lazy {
        getString(R.string.chat_quick_search_file)
    }
    private val mMailTitle by lazy {
        getString(R.string.chat_quick_search_mail)
    }

    private val mHistoryAdapter by lazy {
        ChatSearchHistoryAdapter(ChatSearchHistoryAdapter.OnItemClickListener { searchBean ->
            searchBean ?: return@OnItemClickListener
            val filterJson = try {
                JSONObject(searchBean.filterContent)
            } catch (e: Exception) {
                null
            }
            seeAll(
                when (searchBean.clickTab) {
                    SearchHistoryResponse.Result.TAB_CONTACT -> QuickTypeBean.TYPE_RECORD_CONTACT
                    SearchHistoryResponse.Result.TAB_GROUP -> QuickTypeBean.TYPE_RECORD_GROUP
                    SearchHistoryResponse.Result.TAB_CHAT_RECORD -> QuickTypeBean.TYPE_RECORD_CHAT_RECORD
                    SearchHistoryResponse.Result.TAB_DEPARTMENT -> QuickTypeBean.TYPE_RECORD_DEPARTMENT
                    SearchHistoryResponse.Result.TAB_TASK -> QuickTypeBean.TYPE_RECORD_TASK
                    SearchHistoryResponse.Result.TAB_FAVORITE -> QuickTypeBean.TYPE_RECORD_FAVORITE
                    SearchHistoryResponse.Result.TAB_APPLICATION -> QuickTypeBean.TYPE_RECORD_PLUGIN
                    SearchHistoryResponse.Result.TAB_SCHEDULE -> QuickTypeBean.TYPE_RECORD_SCHEDULE
                    SearchHistoryResponse.Result.TAB_FILE -> QuickTypeBean.TYPE_RECORD_CHAT_FILE
                    SearchHistoryResponse.Result.TAB_SHIMO -> QuickTypeBean.TYPE_RECORD_SHI_MO
                    SearchHistoryResponse.Result.TAB_EMAIL -> QuickTypeBean.TYPE_RECORD_EMAIL
                    else -> QuickTypeBean.TYPE_RECORD_ALL
                },
                filterJson
            )
            activityViewModel.updateSearch(searchBean.query)
        })
    }

    private val mSearchAdapter by lazy {
        with(this@ChatSearchAllFragment) {
            SearchAdapter(context, this, this, this)
        }
    }

    override fun getContentLayoutId() = R.layout.chat_fragment_chat_search_all

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel

    override fun showHintView() {
        mEditContent = ""
        globalSearchId = "NULL"
        dataBinding.llQuick.visibility = View.VISIBLE
        dataBinding.rv.visibility = View.GONE
        dataBinding.searchEmpty.visibility = View.GONE
        setHintExposePoint()
        viewModel.startLoadRecord()
    }

    override fun startSearch(searchString: String) {
        if (searchString == mEditContent) {
            return
        }
        mEditContent = searchString
        mSearchAdapter.setSearchContent(mEditContent)
        viewModel.searchStartTime = System.currentTimeMillis()
        setExposeEnabled(false)
        resetAndShowLoading()
        viewModel.requestSearchPart(searchString)
    }

    private fun resetAndShowLoading() {
        mSearchAdapter.clear()
        mSearchAdapter.notifyDataSetChanged()
        mSearchAdapter.addData(ChatRecordLoadingBean())
        mSearchAdapter.notifyItemInserted(0)
        if (dataBinding.llQuick.visibility == View.VISIBLE) {
            dataBinding.llQuick.visibility = View.GONE
        }
        dataBinding.searchEmpty.visibility = View.GONE
        dataBinding.rv.visibility = View.VISIBLE
    }

    private fun setExposeEnabled(isHint: Boolean) {
        mHistoryPointUtil.setExposeEnable(isHint)
        mLastSinglePointUtil.setExposeEnable(isHint)
        mLastGroupPointUtil.setExposeEnable(isHint)
        mSearchPointUtil.setExposeEnable(!isHint)
    }

    override fun getHintText() = ""

    companion object {
        @JvmStatic
        fun newInstance(pos: Int) = ChatSearchAllFragment().apply {
            val bundle = Bundle()
            bundle.putInt(Constants.FRAGMENT_POS, pos)
            arguments = bundle
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mPos = arguments?.getInt(Constants.FRAGMENT_POS) ?: 0
    }

    override fun onDelSearchHistory() {
        if (mDeleteHistoryDialog == null) {
            mDeleteHistoryDialog = DialogUtils.Builder(context)
                .setContent(R.string.chat_confirm_delete_all_search)
                .setPositive(resources.getString(R.string.confirm))
                .setPositiveListener {
                    mDeleteHistoryDialog?.dismiss()
                    viewModel.searchHistoryDel()
                    dataBinding.containerSearchHistory.visibility = View.GONE
                    viewModel.searchRecordLastDel()
                    dataBinding.llHistory.visibility = View.GONE
                    hideOrShowDelete()
                }
                .setNegative(resources.getString(R.string.cancel))
                .setNegativeListener { mDeleteHistoryDialog?.dismiss() }.build()
        }
        mDeleteHistoryDialog?.show()
    }

    override fun onDelSearchRecord() {
        if (mDeleteRecordDialog == null) {
            mDeleteRecordDialog = DialogUtils.Builder(context)
                .setContent(resources.getString(R.string.chat_confirm_delete_last_search))
                .setPositive(resources.getString(R.string.confirm))
                .setPositiveListener {
                    viewModel.searchRecordLastDel()
                    mDeleteRecordDialog?.dismiss()
                    dataBinding.llHistory.visibility = View.GONE
                    hideOrShowDelete()
                }
                .setNegative(resources.getString(R.string.cancel))
                .setNegativeListener { mDeleteRecordDialog?.dismiss() }
                .build()
        }
        mDeleteRecordDialog?.show()
    }

    override fun onClickFeedBack() {
        showFeedbackDialog(mEditContent, ChatSearchUtils.FeedBackSearchType.ALL)
    }

    override fun onItemClick(adapter: MyMultiTypeAdapter, view: View, position: Int) {
        QMUIKeyboardHelper.hideKeyboard(view)
        val itemClass = adapter.getItemClass(position) ?: return
        val item = adapter.getData(position)
        var id = ""
        var level = ""
        var status = "1"
        when (itemClass) {
            PluginSearchBean::class.java -> {
                handlePluginSearchClick((item as PluginSearchBean))
                id = item.appId
                level = "应用"
            }

            FavoriteSearchBean::class.java -> {
                handleFavoriteOverallSearchClick((item as FavoriteSearchBean))
                id = item.favorId.toString()
                level = "收藏"
            }

            ChatRecordSingleChatBean::class.java -> {
                val bean: ChatRecordSingleChatBean = item as ChatRecordSingleChatBean
                handleChatRecordContactSearchClick(bean)
                id = bean.chatId.toString()
                level = "联系人"
            }

            ChatRecordGroupChatBean::class.java -> {
                handleChatRecordGroupSearchClick((item as ChatRecordGroupChatBean))
                id = item.chatId.toString()
                level = "群聊"
            }

            ChatRecordDepartBean::class.java -> {
                handleChatRecordDepartmentSearchClick((item as ChatRecordDepartBean))
                id = item.chatId.toString()
                level = "部门"
            }

            MultiItemEntityWithId::class.java -> {
                handleChatRecordLastSearchClick((item as MultiItemEntityWithId<*>))
                level = "近期搜索的会话"
                id = item.id.toString()
            }

            ChatRecordTaskBean::class.java -> {
                handleChatRecordTaskSearchClick((item as ChatRecordTaskBean))
                id = item.taskId
                level = "任务"
            }

            ScheduleBean::class.java -> {
                handleChatRecordScheduleSearchClick((item as ScheduleBean))
                id = item.bookingId
                level = "日程"
            }

            ChatRecordSummaryBean::class.java -> {
                handleChatRecordSummarySearchClick((item as ChatRecordSummaryBean))
                id = item.chatId.toString()
                level = "聊天记录"
                status = "2"
            }

            ChatRecordAllChatMessage::class.java -> {
                handleChatRecordFileSearchClick((item as ChatRecordAllChatMessage));
                id = item.chatMessage.chatId.toString()
                level = "文件"
            }

            ShiMoSearchBean::class.java -> {
                handleChatRecordShiMoSearchClick(item as ShiMoSearchBean)
                id = item.fileUrl
                level = "石墨文档"
            }

            MailSearchBean::class.java -> {
                handleMailSearchClick(item as MailSearchBean)
                id = item.pointId
                level = item.pointLevel
            }
        }
        ChatSearchUtils.insertSearchHistory(mEditContent, "", "")
        PointUtils.BuilderV4()
            .name("search-result-click")
            .params("type", "1")
            .params("info", id)
            .params("status", status)
            .params("level", "全部_$level")
            .params("search_id", viewModel.currentSearchId)
            .params("rank", position + 1)
            .point()
    }


    override fun initFragment() {
        super.initFragment()
        initHistory()
        initRecord()
        initSearch()
        initObserver()
        initSearchContent()
        if (activityViewModel.searchContent.value?.text.isNullOrEmpty()) {
            viewModel.startLoadRecord()
        }
        mSearchPointUtil.setExposeListener(dataBinding.rv, mSearchAdapter)
        mHistoryPointUtil.setExposeListener(
            dataBinding.rvSearchHistory,
            mHistoryAdapter
        )
    }

    private fun setHintExposePoint() {
        setExposeEnabled(true)
        mHistoryPointUtil.updateSearchId("0")
        mLastSinglePointUtil.updateSearchId("0")
        mLastGroupPointUtil.updateSearchId("0")
    }

    private fun initHistory() {
        dataBinding.rvSearchHistory.layoutManager =
            LinesFlexboxLayoutManager(context, 1)
        dataBinding.rvSearchHistory.adapter = mHistoryAdapter
        dataBinding.rvSearchHistory.itemAnimator = null
        viewModel.getSearchStringRecord().observe(this, Observer {
            if (LList.isEmpty(it)) {
                dataBinding.containerSearchHistory.visibility = View.GONE
            } else {
                dataBinding.containerSearchHistory.visibility = View.VISIBLE
                mHistoryAdapter.submitList(it)
            }
            hideOrShowDelete()
        })
    }

    private fun initRecord() {
        initNewRecordView()
    }

    private fun initNewRecordView() {
        dataBinding.rvSingleRecord.layoutManager = GridLayoutManager(context, contactsColumns)
        adapterSingle = newContactAdapter()
        adapterSingle?.longClickListener = View.OnLongClickListener {
            CommonUtils.copyText(activity, (it as? TextView)?.text.toString())
            ToastUtils.success(R.string.copied)
            true
        }
        adapterSingle?.bindToRecyclerView(dataBinding.rvSingleRecord)
        dataBinding.rvSingleRecord.isNestedScrollingEnabled = false
        mLastSinglePointUtil.setExposeListener(dataBinding.rvSingleRecord, adapterSingle)
        dataBinding.rvGroupRecord.layoutManager = FlowLayoutManager()
        dataBinding.rvGroupRecord.addItemDecoration(object : RecyclerView.ItemDecoration() {

            private val margin = 6.dp

            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                outRect.right = margin * 2
                outRect.bottom = margin * 2
            }
        })
        adapterGroup = newAdapter(R.layout.chat_item_search_record_group)
        adapterGroup?.bindToRecyclerView(dataBinding.rvGroupRecord)
        dataBinding.rvGroupRecord.isNestedScrollingEnabled = false
        mLastGroupPointUtil.setExposeListener(dataBinding.rvGroupRecord, adapterGroup)
        <EMAIL>()
            .observe(this@ChatSearchAllFragment, Observer { multiItemEntities ->
                if (!LList.isEmpty(multiItemEntities)) {
                    dataBinding.llHistory.visibility = View.VISIBLE
                    val single = arrayListOf<MultiItemEntityWithId<String>>()
                    val group = arrayListOf<MultiItemEntityWithId<String>>()
                    for (m in multiItemEntities) {
                        if (m is SearchContact && single.size < maxShowContactCount) {
                            single.add(m)
                        } else if (m is SearchGroup && group.size < maxShowGroupCount) {
                            group.add(m)
                        }
                    }
                    tempContacts.clear()
                    tempContacts.addAll(single)
                    with(single) {
                        dataBinding.tvSingleTitle.visibility =
                            if (isEmpty()) View.GONE else View.VISIBLE
                        dataBinding.rvSingleRecord.visibility =
                            if (isEmpty()) View.GONE else View.VISIBLE
                        adapterSingle?.setNewData(buildContacts())
                    }
                    with(group) {
                        dataBinding.tvGroupTitle.visibility =
                            if (isEmpty()) View.GONE else View.VISIBLE
                        dataBinding.rvGroupRecord.visibility =
                            if (isEmpty()) View.GONE else View.VISIBLE
                        adapterGroup?.setNewData(this)
                    }
                } else {
                    dataBinding.llHistory.visibility = View.GONE
                }
            })
    }

    /**
     * 生成带操作按钮的联系人列表
     */
    private fun buildContacts(): MutableList<MultiItemEntityWithId<String>> {
        val contacts = mutableListOf<MultiItemEntityWithId<String>>()
        // 折叠时展示两行
        val foldVisibleContactCount = contactsColumns * 2
        // 1. 截取满足当前 expand 状态的联系人列表
        contacts.addAll(
            if (isContactsExpanded) tempContacts
            else {
                if (tempContacts.size > foldVisibleContactCount) {
                    tempContacts.subList(0, foldVisibleContactCount - 1)
                } else tempContacts
            }
        )
        // 2. 添加 action 按钮
        if (tempContacts.size > foldVisibleContactCount) {
            contacts.add(SearchAction(isContactsExpanded))
        }
        return contacts
    }

    private val adapterItemClickListener =
        BaseQuickAdapter.OnItemClickListener { adapter, view, position ->
            QMUIKeyboardHelper.hideKeyboard(view)
            val item = adapter.getItem(position)
            if (item is SearchAction) {
                isContactsExpanded = item.isExpanded.not()
                adapter.setNewData(buildContacts())
            } else if (item is MultiItemEntityWithId<*>) {
                recordToJumpChat(item)
                PointUtils.BuilderV4()
                    .name("search-result-click")
                    .params("type", "2")
                    .params("info", item.id)
                    .params("status", "1")
                    .params("level", "全部_${item.pointLevel}")
                    .params("search_id", viewModel.currentSearchId)
                    .params("rank", position + 1)
                    .point()
            }
        }

    private fun newContactAdapter() =
        ChatSearchContactAdapter(arrayListOf(), contactsColumns).apply {
            onItemClickListener = adapterItemClickListener
        }

    private fun newAdapter(layoutRes: Int) = ChatRecordSearchLastAdapter(layoutRes).apply {
        onItemClickListener = adapterItemClickListener
    }


    private fun initSearch() {
        mSearchAdapter.setGroupCacheInfo(viewModel)
        mSearchAdapter.setOnItemClickListener(this@ChatSearchAllFragment)
        dataBinding.rv.layoutManager = LinearLayoutManager(context)
        dataBinding.rv.itemAnimator = null
        dataBinding.rv.adapter = mSearchAdapter
    }

    private fun initObserver() {
        viewModel.externalScanResult.observe(this,
            Observer { temporaryTokenBean ->
                if (temporaryTokenBean == null) {
                    return@Observer
                }
                val webViewBean = WebViewBean()
                webViewBean.url = temporaryTokenBean.url
                webViewBean.style = temporaryTokenBean.style
                val bundle = Bundle()
                bundle.putSerializable(Constants.DATA_WEB_BEAN, webViewBean)
                AppUtil.startUri(
                    activity,
                    WebViewPageRouter.WEB_VIEW_ACTIVITY,
                    bundle
                )
            })
    }

    private fun initSearchContent() {
        viewModel.getServerSearch().observe(this, Observer { serverModel ->
            var isRendered = false
            var isHaveData = false
            serverModel ?: run {
                mSearchAdapter.clear()
                mSearchAdapter.notifyDataSetChanged()
                return@Observer
            }
            //服务端获取
            if (serverModel.key == mEditContent) {
                isRendered = true
                if (dataBinding.llQuick.visibility == View.VISIBLE) {
                    dataBinding.llQuick.visibility = View.GONE
                }
                if (serverModel.isHaveData || serverModel.haveCardData()) {
                    isHaveData = true
                    dataBinding.rv.visibility = View.VISIBLE
                    dataBinding.searchEmpty.visibility = View.GONE
                    mSearchPointUtil.updateSearchId(viewModel.currentSearchId)
                }
                TLog.info("ChatSearchAllFragmentViewModel", "receive part[${serverModel.part} | ${serverModel.sort}]")
                // 分别接收 1 / 2 / 3 / 4 四个分区的数据
                val items = sortData(serverModel)
                when (serverModel.part) {
                    1 -> handlePart1(serverModel, items)
                    4 -> handlePart4(serverModel, items)
                    else -> handleCommonLogic(serverModel, items)
                }
                tryRecordPart(serverModel.part, serverModel.key, serverModel.uniqueId)
                PointUtils.BuilderV4()
                    .name("perform-page-load")
                    .params("scenerio", 6)
                    .params(
                        "load_time",
                        (System.currentTimeMillis() - viewModel.searchStartTime).toString()
                    )
                    .point()
            }
            viewModel.states.uploadSearchResult(serverModel.uniqueId, isRendered, isHaveData)
        })

        activityViewModel.currentPos.observe(this, Observer {
            if (it == mPos) {
                setExposeEnabled(activityViewModel.searchContent.value?.text.isNullOrEmpty())
            } else {
                mHistoryPointUtil.setExposeEnable(false)
                mLastSinglePointUtil.setExposeEnable(false)
                mLastGroupPointUtil.setExposeEnable(false)
                mSearchPointUtil.setExposeEnable(false)
            }

            if (it == 0) {
                // removeAllView是为了重新曝光，否则FlowLayoutManager不会重新addview触发曝光
                dataBinding.rvGroupRecord.layoutManager?.removeAllViews()
            }
        })

        activityViewModel.backPage.observe(this) {
            if (mPos != activityViewModel.currentPos.value) {
                return@observe
            }
            //搜索历史
            mHistoryPointUtil.exposeInstantly(dataBinding.rvSearchHistory)
            //最近搜索模块
            mLastSinglePointUtil.exposeInstantly(dataBinding.rvSingleRecord)
            dataBinding.rvGroupRecord.layoutManager?.removeAllViews()
            //搜索结果
            mSearchPointUtil.exposeInstantly(dataBinding.rv)
        }
    }


    private fun tryRecordPart(part: Int, query: String, searchId: String) {
        val tag = String.format("%s%s_%s", TimeTag.GLOBAL_SEARCH_PART, part, query)
        if (TimeDifferenceUtil.getInstance().hasStarted(tag)) {
            val duration = TimeDifferenceUtil.getInstance().finish(tag)
            TLog.info(TAG, "render part -> %s, duration -> %s", part, duration)
            BuilderV4()
                .name("search-global-duration")
                .params("type", 2)
                .params("level", part)
                .params("search_id", searchId)
                .params("query", query)
                .params("duration", duration)
                .point()
        }
    }

    /**
     * 处理第一区数据
     */
    private fun handlePart1(serverModel: ChatRecordAllSearchServerModel, items: List<Any>) {
        if (serverModel.isHaveData || serverModel.haveCardData()) {
            var cardItemsCount = 0
            // 消息卡片特殊处理，只会在第一区返回，不参与排序，展示在最上面
            if (serverModel.haveCardData()) {
                val cardItems = fillSearchAdapterData(
                    serverModel.searchCard,
                    "",
                    -1,
                    mutableListOf(0),
                    0
                )
                mSearchAdapter.addData(cardItems)
                cardItemsCount += cardItems.size
            }
            // 添加数据并添加 loading
            mSearchAdapter.addData(items)
            mSearchAdapter.addData(ChatRecordLoadingBean())
            mSearchAdapter.notifyItemRangeInserted(0, cardItemsCount + items.size + 1)
        } else {
            handleCommonLogic(serverModel, items)
        }
    }

    /**
     * 分区数据处理
     */
    private fun handleCommonLogic(serverModel: ChatRecordAllSearchServerModel, items: List<Any>) {
        if (serverModel.isHaveData || serverModel.haveCardData()) {
            // 如果有数据则尝试插入到 loading 之前
            val insertIndex = mSearchAdapter.data.indexOfFirst { it is ChatRecordLoadingBean }
            if (mSearchAdapter.itemCount > 0) {
                mSearchAdapter.addData(mSearchAdapter.itemCount - 1, items)
            }
            if (insertIndex > -1) {
                TLog.info("ChatSearchAllFragmentViewModel", "itemCount -> %s, insertIndex -> %s, items.size -> %s", mSearchAdapter.itemCount, insertIndex, items.size)
                mSearchAdapter.notifyItemRangeInserted(mSearchAdapter.itemCount - 1, items.size)
            } else {
                TLog.info("ChatSearchAllFragmentViewModel", "err insertIndex -> %s", insertIndex)
            }
        } else {
            // 如果当前分区没有数据，则检查并添加 loading
            if (mSearchAdapter.itemCount == 0) {
                val insertIndex = mSearchAdapter.data.indexOfFirst { it is ChatRecordLoadingBean }
                if (insertIndex == -1) {
                    mSearchAdapter.addData(ChatRecordLoadingBean())
                    mSearchAdapter.notifyItemInserted(0)
                }
            }
        }
    }

    /**
     * 处理第四区数据
     */
    private fun handlePart4(serverModel: ChatRecordAllSearchServerModel, items: List<Any>) {
        if (serverModel.isHaveData || serverModel.haveCardData()) {
            // 如果有数据尝试插入数据
            handleCommonLogic(serverModel, items)
            // 因为第四分区是最后一个分区，所以加载完数据后可以移除 loading
            if (mSearchAdapter.itemCount > 0) {
                mSearchAdapter.data.removeLast()
                mSearchAdapter.notifyItemRemoved(mSearchAdapter.itemCount)
            }
            //添加底部"没有想要的结果"文案
            mSearchAdapter.addData(SearchItemFeedBack())
            mSearchAdapter.notifyItemInserted(mSearchAdapter.itemCount)
        } else {
            if (mSearchAdapter.itemCount <= 1) {
                // 若第四分区没有数据，且 adapter 为空，展示空数据提示
                dataBinding.rv.visibility = View.GONE
                dataBinding.searchEmpty.visibility = View.VISIBLE
                initSearchEmptyHintText(serverModel.key, dataBinding.searchEmpty, this)
            } else {
                // 若第四分区没有数据，且 adapter 不为空，移除 loading
                mSearchAdapter.data.removeLast()
                mSearchAdapter.notifyItemRemoved(mSearchAdapter.itemCount)
                mSearchAdapter.addData(SearchItemFeedBack())
                mSearchAdapter.notifyItemInserted(mSearchAdapter.itemCount)
            }
        }
    }

    private fun sortData(serverModel: ChatRecordAllSearchServerModel): List<Any> {
        //拦截标记，为ture时不在进行数据填充
        var counter = mutableListOf(0)
        val resultTypeCount = serverModel.resultTypeCount()
        val multipleItems = mutableListOf<Any>()
        serverModel.sort?.forEach { e ->
            val items: List<Any> = when (e) {
                ChatRecordAllSearchResponse.SEARCH_NAME_CONTACT -> fillSearchAdapterData(
                    serverModel.contact,
                    mContactTitle,
                    QuickTypeBean.TYPE_RECORD_CONTACT,
                    counter,
                    resultTypeCount
                )

                ChatRecordAllSearchResponse.SEARCH_NAME_GROUP -> fillSearchAdapterData(
                    serverModel.group,
                    mGroupTitle,
                    QuickTypeBean.TYPE_RECORD_GROUP,
                    counter,
                    resultTypeCount
                )

                ChatRecordAllSearchResponse.SEARCH_NAME_DEPARTMENTS -> fillSearchAdapterData(
                    serverModel.departments,
                    mDepartmentTitle,
                    QuickTypeBean.TYPE_RECORD_DEPARTMENT,
                    counter,
                    resultTypeCount
                )

                ChatRecordAllSearchResponse.SEARCH_NAME_MSGSUMMARY -> fillSearchAdapterData(
                    serverModel.msgSummary,
                    mSummaryTitle,
                    QuickTypeBean.TYPE_RECORD_CHAT_RECORD,
                    counter,
                    resultTypeCount
                )

                ChatRecordAllSearchResponse.SEARCH_NAME_SHIMO -> if (SettingBuilder.getInstance()
                        .getJurisdictionVisible(JurisdictionUtils.JURISDICTION_SHI_MO) == JurisdictionUtils.IDENTITY_VISIBLE
                ) {
                    fillSearchAdapterData(
                        serverModel.shimo,
                        mShiMoTitle,
                        QuickTypeBean.TYPE_RECORD_SHI_MO,
                        counter,
                        resultTypeCount
                    )
                } else emptyList()

                ChatRecordAllSearchResponse.SEARCH_NAME_TASK -> fillSearchAdapterData(
                    serverModel.task,
                    mToDoTitle,
                    QuickTypeBean.TYPE_RECORD_TASK,
                    counter,
                    resultTypeCount
                )

                ChatRecordAllSearchResponse.SEARCH_NAME_FAVOR -> fillSearchAdapterData(
                    serverModel.favor,
                    mFavoriteTitle,
                    QuickTypeBean.TYPE_RECORD_FAVORITE,
                    counter,
                    resultTypeCount
                )

                ChatRecordAllSearchResponse.SEARCH_NAME_PLUGIN -> fillSearchAdapterData(
                    serverModel.plugin,
                    mPluginTitle,
                    QuickTypeBean.TYPE_RECORD_PLUGIN,
                    counter,
                    resultTypeCount
                )

                ChatRecordAllSearchResponse.SEARCH_NAME_SCHEDULE -> fillSearchAdapterData(
                    serverModel.schedule,
                    mScheduleTitle,
                    QuickTypeBean.TYPE_RECORD_SCHEDULE,
                    counter,
                    resultTypeCount
                )

                ChatRecordAllSearchResponse.SEARCH_NAME_FILE -> fillSearchAdapterData(
                    serverModel.file,
                    mFileTitle,
                    QuickTypeBean.TYPE_RECORD_CHAT_FILE,
                    counter,
                    resultTypeCount
                )

                ChatRecordAllSearchResponse.SEARCH_NAME_MAIL -> fillSearchAdapterData(
                    serverModel.mail,
                    mMailTitle,
                    QuickTypeBean.TYPE_RECORD_EMAIL,
                    counter,
                    resultTypeCount
                )

                ChatRecordAllSearchResponse.SEARCH_NAME_SEARCH_CARD -> fillSearchAdapterData(
                    serverModel.searchCard,
                    "",
                    -1,
                    counter,
                    resultTypeCount
                )

                else -> emptyList<Any>()
            }
            multipleItems.addAll(items)
        }
        return multipleItems
    }

    private fun recordToJumpChat(multiItemEntity: MultiItemEntity) {
        if (multiItemEntity is SearchContact) {
            val contact = multiItemEntity.contact
            if (contact == null) {
                ToastUtils.failure("找不到联系人")
                return
            }
            viewModel.handleLocalContact(contact.userId,
                contact.isUserVisible,
                contact.chatVisible == 1,
                goChat = {
                    if (!TextUtils.isEmpty(mEditContent)) {
                        ExecutorFactory.execLocalTask {
                            ChatSearchUtils.insertSearchRecord(
                                mEditContent,
                                contact.userId,
                                MessageConstants.MSG_SINGLE_CHAT,
                                contact.userType == MessageConstants.MSG_CONTACT_TYPE_SYS
                            )
                        }
                    }
                    SingleChatActivity.start(activity, contact.userId)
                },
                goUserInfo = {
                    OrganizationPageRouter.jumpToUserInfoActivity(
                        activity,
                        contact.userId
                    )
                })
        } else if (multiItemEntity is SearchGroup) {
            val group = multiItemEntity.group
            if (!TextUtils.isEmpty(mEditContent)) {
                ExecutorFactory.execLocalTask {
                    ChatSearchUtils.insertSearchRecord(
                        mEditContent,
                        group.groupId,
                        MessageConstants.MSG_GROUP_CHAT
                    )
                }
            }
            GroupChatActivity.start(activity, group.groupId)
        }
    }

    private fun handleChatRecordContactSearchClick(contact: ChatRecordAllSearchServerBean) {
        val userId = contact.chatId
        if (StringUtils.isEquals(userId, MessageConstants.ID_COLLAPSED_CONVERSATION)) {
            ArchiveActivity.jumpToArchiveActivity(activity, ArchiveActivity.SEARCH)
        } else {
            val isSys = contact.userType == MessageConstants.MSG_CONTACT_TYPE_SYS
            viewModel.handleContact(
                contact.chatId,
                (contact as ChatRecordSingleChatBean).isChatVisible,
                goChat = {
                    if (!TextUtils.isEmpty(mEditContent)) {
                        ExecutorFactory.execLocalTask {
                            ChatSearchUtils.insertSearchRecord(
                                mEditContent,
                                userId,
                                MessageConstants.MSG_SINGLE_CHAT,
                                isSys
                            )
                        }
                    }
                    SingleChatActivity.start(activity, userId)
                },
                goUserInfo = {
                    OrganizationPageRouter.jumpToUserInfoActivity(activity, userId)
                }
            )
        }
    }

    /**
     * 邮箱搜索 item 点击
     */
    private fun handleMailSearchClick(mailSearchBean: MailSearchBean) {
        EmailPageRouter.jumpToViewEmailDetails(
            activity as FragmentActivity,
            mailSearchBean.mailId,
            false
        )
    }

    private fun handleChatRecordShiMoSearchClick(contact: ShiMoSearchBean) {
        val onlineFileBean = com.twl.hi.basic.model.WebViewBean()
        val stringBuilder =
            StringBuilder(SettingBuilder.getInstance().onlineFileMidUrl)
        stringBuilder.append("?redirectUrl=")
        stringBuilder.append(contact.fileUrl)
        onlineFileBean.url = stringBuilder.toString()
        val bundleOnLine = Bundle()
        bundleOnLine.putSerializable(Constants.DATA_WEB_BEAN, onlineFileBean)
        AppUtil.startUri(
            activity,
            WebViewPageRouter.WEB_VIEW_ACTIVITY,
            bundleOnLine
        )
    }

    private fun handleChatRecordGroupSearchClick(group: ChatRecordAllSearchServerBean) {
        val groupId = group.chatId
        if (!TextUtils.isEmpty(mEditContent)) {
            ExecutorFactory.execLocalTask {
                ChatSearchUtils.insertSearchRecord(
                    mEditContent,
                    groupId,
                    MessageConstants.MSG_GROUP_CHAT
                )
            }
        }
        if (!group.isJoined) {
            viewModel.requestGroupDetailInfo(groupId.toString(), object : BaseCallback<Boolean> {
                override fun onSuccess(t: Boolean?) {
                    if (t == true) {
                        jumpToGroupDetailPage(group)
                    } else {
                        ToastUtils.failure("当前群聊不可加入")
                    }
                }
            })
        } else {
            GroupChatActivity.start(activity, groupId)
        }
        if (group.isPublic == 1) {
            PointUtils.BuilderV3().name("search-public-click").point()
        }
    }

    private fun handleChatRecordSummarySearchClick(summary: ChatRecordSummaryBean) {
        // 防止重复请求
        activityViewModel.markFromSeeAll()
        (getActivity() as? ChatRecordAllSearchActivity)?.switchTab(QuickTypeBean.TYPE_RECORD_CHAT_RECORD)
        activityViewModel.updateSelSummaryBean(summary)
    }

    private fun handleChatRecordScheduleSearchClick(schedule: ScheduleBean) {
        SchedulePageRouter.jumpScheduleDetailPage(
            getActivity(),
            schedule.scheduleId,
            schedule.targetDate,
            Constants.CREATE_FROM_SEARCH
        )
    }

    private fun handleChatRecordTaskSearchClick(task: ChatRecordTaskBean) {
        WorkflowPagerRouter.jumpToTaskDetailEditActivity(activity, task.taskId, "")
    }

    private fun handleChatRecordFileSearchClick(fileMessage: ChatRecordAllChatMessage) {
        if (fileMessage.chatMessage.mediaType == MessageConstants.MSG_FILE) {
            val messageForFile = fileMessage.chatMessage as MessageForFile
            FilePreviewUtil.jumpToMsgFilePreviewPage(
                activity,
                messageForFile,
                MESSAGE_RECORD_SEARCH_PAGE
            )
        }
    }

    private fun handleChatRecordLastSearchClick(recentChat: MultiItemEntity) {
        recordToJumpChat(recentChat)
    }

    private fun handleChatRecordDepartmentSearchClick(department: ChatRecordAllSearchServerBean) {
        ChatSearchUtils.toJumpDepartment(
            activity,
            department.chatId,
            department.name
        )
    }

    private fun handleFavoriteOverallSearchClick(favoriteSearchBean: FavoriteSearchBean) {
        FavoriteActivity.start(
            activity,
            favoriteSearchBean.favorId
        )
    }

    private fun handlePluginSearchClick(pluginSearchBean: PluginSearchBean) {
        ServiceManager.getInstance().workbenchService.openWorkbenchPlugin(
            activity,
            pluginSearchBean.appId,
            mutableMapOf(
                "appName" to pluginSearchBean.name,
                "sceneCode" to Scene.Search.code.toString(),
                "openAppType" to
                        if (pluginSearchBean.isRobot) QueryOpenAppLastedResult.APP_TYPE_ROBOT.toString() else "0"
            )
        )
    }

    override fun seeAll(type: Int) {
        seeAll(type, null)
    }

    override fun seeAll(type: Int, filter: JSONObject?) {
        var level = ""
        globalSearchId = viewModel.currentSearchId
        when (type) {
            QuickTypeBean.TYPE_RECORD_CONTACT -> {
                //联系人
                level = "联系人"
            }

            QuickTypeBean.TYPE_RECORD_GROUP -> {
                //群组
                level = "群聊"
            }

            QuickTypeBean.TYPE_RECORD_DEPARTMENT -> {
                //部门
                level = "部门"
            }

            QuickTypeBean.TYPE_RECORD_CHAT_RECORD -> {
                level = "聊天记录"
                activityViewModel.markFromSeeAll()
            }

            QuickTypeBean.TYPE_RECORD_CHAT_FILE -> {
                level = "文件"
            }

            QuickTypeBean.TYPE_RECORD_SCHEDULE -> {
                level = "日程"
                activityViewModel.markFromSeeAll()
            }

            QuickTypeBean.TYPE_RECORD_TASK -> {
                level = "任务"
                activityViewModel.markFromSeeAll()
            }

            QuickTypeBean.TYPE_RECORD_FAVORITE -> {
                level = "收藏"
            }

            QuickTypeBean.TYPE_RECORD_PLUGIN -> {
                level = "应用"
            }

            QuickTypeBean.TYPE_RECORD_SHI_MO -> {
                level = "石墨文档"
                activityViewModel.markFromSeeAll()
            }

            QuickTypeBean.TYPE_RECORD_EMAIL -> {
                level = "邮箱"
            }

            else -> {
            }
        }
        activityViewModel.postSellAllType(SeeAllOptions(type, filter))
        PointUtils.BuilderV4()
            .name("search-result-click")
            .params("type", "1")
            .params("info", "查看更多")
            .params("status", "2")
            .params("level", "全部_$level")
            .params("search_id", viewModel.currentSearchId)
            .point()
    }

    private fun fillSearchAdapterData(
        bean: ChatRecordAllSearchBean<*>?,
        title: String,
        type: Int,
        counter: MutableList<Int>,
        resultTypeCount: Int
    ): List<Any> {
        val items = mutableListOf<Any>()
        if (bean != null && !LList.isEmpty(bean.list)) {
            items.add(SearchItemTitleBean(title))
            items.addAll(bean.list)
            items.add(
                SearchItemMoreComBean(
                    type,
                    bean.hasMore == 1,
                    ++counter[0] != resultTypeCount
                )
            )
        }
        return items
    }

    override fun getSearchType() = QuickTypeBean.TYPE_RECORD_ALL

    private fun hideOrShowDelete() {
        val historyVisible = dataBinding.containerSearchHistory.isVisible
        val recordVisible = dataBinding.llHistory.isVisible
        dataBinding.deleteAll.isVisible = historyVisible || recordVisible
    }

    override fun onSeeAll(options: SeeAllOptions) {
        //nothing
    }

    override fun applyFilter(filter: JSONObject) {

    }

    override fun getTab(): String {
        return ""
    }
}