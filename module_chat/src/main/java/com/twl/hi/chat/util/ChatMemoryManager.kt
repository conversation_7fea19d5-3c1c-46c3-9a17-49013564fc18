package com.twl.hi.chat.util

import android.app.ActivityManager
import android.content.ComponentCallbacks2
import android.content.Context
import android.content.res.Configuration
import android.os.Debug
import android.view.View
import android.view.ViewGroup
import com.facebook.drawee.backends.pipeline.PipelineDraweeController
import com.facebook.drawee.view.SimpleDraweeView
import com.techwolf.lib.tlog.TLog
import lib.twl.common.base.BaseApplication

/**
 * 聊天模块内存管理工具类
 * 用于监控和优化内存使用，防止OOM
 * 
 * Created by AI Assistant on 2025/07/03
 */
object ChatMemoryManager {
    
    private const val TAG = "ChatMemoryManager"
    
    // 内存警告阈值 (80%)
    private const val MEMORY_WARNING_THRESHOLD = 0.8f
    
    // 内存危险阈值 (90%)
    private const val MEMORY_CRITICAL_THRESHOLD = 0.9f
    
    /**
     * 检查当前内存使用情况
     */
    @JvmStatic
    fun checkMemoryUsage(): MemoryStatus {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()
        
        val status = when {
            memoryUsageRatio >= MEMORY_CRITICAL_THRESHOLD -> MemoryStatus.CRITICAL
            memoryUsageRatio >= MEMORY_WARNING_THRESHOLD -> MemoryStatus.WARNING
            else -> MemoryStatus.NORMAL
        }
        
        TLog.debug(TAG, "Memory usage: ${(memoryUsageRatio * 100).toInt()}% " +
                "(${usedMemory / 1024 / 1024}MB / ${maxMemory / 1024 / 1024}MB) - Status: $status")
        
        return status
    }
    
    /**
     * 获取详细的内存信息
     */
    @JvmStatic
    fun getMemoryInfo(): MemoryInfo {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        
        val context = BaseApplication.getApplication()
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        return MemoryInfo(
            maxMemory = maxMemory,
            totalMemory = totalMemory,
            freeMemory = freeMemory,
            usedMemory = usedMemory,
            availableMemory = memoryInfo.availMem,
            totalSystemMemory = memoryInfo.totalMem,
            isLowMemory = memoryInfo.lowMemory,
            threshold = memoryInfo.threshold
        )
    }
    
    /**
     * 执行内存清理
     */
    @JvmStatic
    fun performMemoryCleanup() {
        try {
            TLog.info(TAG, "Starting memory cleanup...")

            // 1. 清理Fresco缓存
            clearFrescoCache()

            // 2. 执行GC
            System.gc()

            // 3. 清理其他缓存（注意：不要调用clearApplicationUserData，这会清除所有应用数据）
            // 只清理内存缓存，不清理持久化数据

            TLog.info(TAG, "Memory cleanup completed")

        } catch (e: Exception) {
            TLog.error(TAG, "Error during memory cleanup", e)
        }
    }
    
    /**
     * 清理Fresco图片缓存
     */
    @JvmStatic
    fun clearFrescoCache() {
        try {
            com.facebook.drawee.backends.pipeline.Fresco.getImagePipeline().clearCaches()
            TLog.debug(TAG, "Fresco cache cleared")
        } catch (e: Exception) {
            TLog.error(TAG, "Error clearing Fresco cache", e)
        }
    }
    
    /**
     * 递归清理ViewGroup中的Fresco资源
     */
    @JvmStatic
    fun clearFrescoResourcesInView(view: View) {
        try {
            when (view) {
                is SimpleDraweeView -> {
                    val controller = view.controller
                    if (controller is PipelineDraweeController) {
                        controller.onDetach()
                    }
                    view.controller = null
                }
                is ViewGroup -> {
                    for (i in 0 until view.childCount) {
                        clearFrescoResourcesInView(view.getChildAt(i))
                    }
                }
            }
        } catch (e: Exception) {
            TLog.error(TAG, "Error clearing Fresco resources in view", e)
        }
    }
    
    /**
     * 检查是否需要执行内存清理
     */
    @JvmStatic
    fun shouldPerformCleanup(): Boolean {
        return checkMemoryUsage() != MemoryStatus.NORMAL
    }
    
    /**
     * 内存状态枚举
     */
    enum class MemoryStatus {
        NORMAL,     // 正常
        WARNING,    // 警告
        CRITICAL    // 危险
    }
    
    /**
     * 内存信息数据类
     */
    data class MemoryInfo(
        val maxMemory: Long,
        val totalMemory: Long,
        val freeMemory: Long,
        val usedMemory: Long,
        val availableMemory: Long,
        val totalSystemMemory: Long,
        val isLowMemory: Boolean,
        val threshold: Long
    ) {
        val usageRatio: Float
            get() = usedMemory.toFloat() / maxMemory.toFloat()
            
        val usagePercentage: Int
            get() = (usageRatio * 100).toInt()
    }
}
