package com.twl.hi.chat;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.*;
import android.util.Log;
import android.view.*;
import android.view.inputmethod.EditorInfo;
import android.widget.*;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.view.ViewKt;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.sankuai.waimai.router.Router;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.BottomListDialog;
import com.twl.hi.basic.PageConstantsKt;
import com.twl.hi.basic.databinding.ChatViewChatCommonWordsBinding;
import com.twl.hi.basic.databinding.PopChatLongClickBinding;
import com.twl.hi.basic.dialog.DialogUtils;
import com.twl.hi.basic.feature.scheduledmsg.ScheduledMsgContentModel;
import com.twl.hi.basic.feature.scheduledmsg.ScheduledMsgModel;
import com.twl.hi.basic.feature.slashcommand.SlashCommandHelper;
import com.twl.hi.basic.feature.slashcommand.ui.SlashCommandPopup;
import com.twl.hi.basic.helpers.ConversationAvatarPointerHelperKt;
import com.twl.hi.basic.model.ChatExpandableCommonWordsBean;
import com.twl.hi.basic.model.WebViewBean;
import com.twl.hi.basic.model.select.ForwardMessageBean;
import com.twl.hi.basic.model.select.ICloudSelectParams;
import com.twl.hi.basic.util.ThemeUtils;
import com.twl.hi.basic.views.LengthNoticeFilter;
import com.twl.hi.basic.views.group.LinearLayoutManagerWrapper;
import com.twl.hi.chat.adapter.ChatMessageAdapter;
import com.twl.hi.chat.adapter.ChatMsgOptionsAdapter;
import com.twl.hi.chat.callback.InputChatPageCallback;
import com.twl.hi.chat.callback.OnEmojiReplyCallback;
import com.twl.hi.chat.databinding.ChatInputBottomViewBinding;
import com.twl.hi.chat.databinding.ChatTitleBarChatBinding;
import com.twl.hi.chat.databinding.ChatViewReplyTipsBinding;
import com.twl.hi.chat.dialog.ScreenShotChatDialog;
import com.twl.hi.chat.feature.attachment.ChatAttachmentHelper;
import com.twl.hi.chat.model.ChatClickOptionBean;
import com.twl.hi.chat.model.SelectionEmojiItem;
import com.twl.hi.chat.util.ChatImageHelper;
import com.twl.hi.chat.viewmodel.ChatBaseViewModel;
import com.twl.hi.chat.widget.ChatMessageViewModel;
import com.twl.hi.chat.widget.funcview.ChatFuncView;
import com.twl.hi.chat.widget.funcview.ItemFunc;
import com.twl.hi.chat.widget.voice.HiVoiceView;
import com.twl.hi.emotion.EmotionHelper;
import com.twl.hi.emotion.EmotionPanelView;
import com.twl.hi.emotion.common.EmotionDataImp;
import com.twl.hi.emotion.common.EmotionDataManager;
import com.twl.hi.emotion.utils.EmojiDataManager;
import com.twl.hi.emotion.utils.EmojiHelper;
import com.twl.hi.emotion.widget.EmotionBasePopup;
import com.twl.hi.emotion.widget.EmotionSuggestPopup;
import com.twl.hi.export.audio.shorthand.IAudioShortHandService;
import com.twl.hi.export.audio.shorthand.router.AudioShortHandPageRouter;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.export.main.router.AppPageRouter;
import com.twl.hi.export.me.router.MePageRouter;
import com.twl.hi.export.organization.router.OrganizationPageRouter;
import com.twl.hi.export.select.bean.SelectBaseParams;
import com.twl.hi.export.select.bean.SelectConversationParams;
import com.twl.hi.export.select.bean.SelectOption;
import com.twl.hi.export.select.router.SelectPageRouter;
import com.twl.hi.export.webview.WebViewPageRouter;
import com.twl.hi.export.work.router.WorkPageRouter;
import com.twl.hi.foundation.MessageSummaryVisitor;
import com.twl.hi.foundation.SendMessageContent;
import com.twl.hi.foundation.api.callback.ValueCallBack;
import com.twl.hi.foundation.api.response.bean.ResourceBean;
import com.twl.hi.foundation.facade.AttendanceRepository;
import com.twl.hi.foundation.helper.GrayConfigGetHelper;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.logic.SystemGrayConfigService;
import com.twl.hi.foundation.model.*;
import com.twl.hi.foundation.model.emotion.EmotionCategory;
import com.twl.hi.foundation.model.emotion.EmotionItem;
import com.twl.hi.foundation.model.message.*;
import com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard;
import com.twl.hi.foundation.model.security.BusinessModuleEnum;
import com.twl.hi.foundation.utils.MLinkEmotionTextUtils;
import com.twl.hi.foundation.utils.MessageUtils;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.foundation.utils.RichTextEscapes;
import com.twl.hi.richtext.clipboard.ClipboardHelper;
import com.twl.hi.richtext.editor.InterceptEditTextView;
import com.twl.hi.richtext.editor.RichEditDialog;
import com.twl.hi.richtext.editor.RichEditView;
import com.twl.hi.richtext.editor.helper.AtHelper;
import com.twl.hi.richtext.editor.helper.DraftHelper;
import com.twl.hi.richtext.editor.helper.RichTextCmdHelper;
import com.twl.hi.richtext.editor.interceptor.PasteInterceptListener;
import com.twl.hi.richtext.editor.toolbar.ToolsManager;
import com.twl.hi.richtext.parser.MarkdownGenerator;
import com.twl.utils.GsonUtils;
import com.twl.utils.StringUtils;
import com.twl.utils.URLUtils;
import com.twl.utils.jurisdiction.JurisdictionUtils;
import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import hi.kernel.RequestCodeConstants;
import kotlin.Pair;
import kotlin.Unit;
import lib.twl.common.LBase;
import lib.twl.common.adapter.CommonAdapter;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.dialog.CommonDialog;
import lib.twl.common.ext.KotlinExtKt;
import lib.twl.common.kpswitch.util.KPSwitchConflictUtil;
import lib.twl.common.kpswitch.util.KeyboardUtil;
import lib.twl.common.kpswitch.widget.KPSwitchPanelLinearLayout;
import lib.twl.common.permission.PermissionAvoidManager;
import lib.twl.common.photoselect.PhotoSelectManager;
import lib.twl.common.util.*;
import lib.twl.common.util.selection.OperateWindow;
import lib.twl.common.util.selection.TextSelection;
import lib.twl.common.util.widget.HiPopupWindow;
import lib.twl.common.views.richeditor.RichConstants;
import lib.twl.common.views.time.DateTimePickerView;
import lib.twl.common.views.time.builder.DateTimePickerBuilder;
import org.json.JSONObject;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.twl.hi.richtext.parser.SpannableDecorator.decorateHyperlinks;
import static lib.twl.common.ext.ViewExtKt.getStatusBarsHeight;

/**
 * 带输入框的聊天页面
 * <p>
 * 子页面主要有：单聊&单聊回复、群聊&群聊回复页面
 */
public abstract class InputChatFragment<D extends ViewDataBinding, M extends ChatBaseViewModel> extends ChatBaseFragment<D, M>
        implements InputChatPageCallback {

    private static final String TAG = "InputChatFragment";

    private final Pattern HYPERLINK_PATTERN = MLinkEmotionTextUtils.compileHyperlinkPattern();
    private final Pattern HYPERTEXT_PATTERN = MLinkEmotionTextUtils.mergeCompileHypertextPattern();

    protected ScreenShotChatDialog mScreenShotChatDialog;

    /**
     * 消息右键操作popup
     */
    protected HiPopupWindow mLongClickPopupWindow;
    protected PopChatLongClickBinding mPopChatLongClickBinding;

    protected TextSelection mTextSelection;

    protected EmotionSuggestPopup mEmotionSuggestPopup;

    // 键盘状态跟踪和表情联想延迟显示
    private boolean mIsKeyboardShowing = false;
    private String mPendingEmotionText = null;
    private final Handler mEmotionHandler = new Handler(Looper.getMainLooper());
    private Runnable mEmotionSuggestRunnable = null;

    protected DialogUtils mDeleteChatDialog;
    protected DialogUtils mCancelInviteDialog;
    protected ChatMessageAdapter mMessageAdapter;
    protected HiVoiceView mHiVoiceView;
    CommonAdapter<ChatExpandableCommonWordsBean> mChatCommonWordsAdapter;

    /**
     * 底部funcview
     */
    private BottomListDialog mFileSelectBottomDialog;

    /**
     * 斜杠指令相关
     */
    private SlashCommandPopup mSlashCommandPopup = null;
    protected int mSlashCommandCount = -1;
    private int mAnchorHeight = -1;

    private DateTimePickerView mDateTimePickerView;
    private final StringBuilder mDealLaterTargetDate = new StringBuilder();
    private final StringBuilder mDealLaterEndTime = new StringBuilder();
    private final StringBuilder mScheduledMsgDate = new StringBuilder();
    private final StringBuilder mScheduledMsgTime = new StringBuilder();

    private DialogUtils mDialog;

    private String mInputHint = "";
    private int mInputWidth = 0;

    // 附件助手
    protected ChatAttachmentHelper attachmentHelper;

    protected PasteInterceptListener mPasteInterceptListener = pasteString -> {
        if (pasteString != null) {
            int length = pasteString.length();
            if (length > RichConstants.EMPTY_PASTE_MAX_LENGTH) {
                ToastUtils.failure("粘贴的文本过长，无法发送");
                return true;
            }

            boolean isEmpty = TextUtils.isEmpty(getEditTitleText().getText()) && TextUtils.isEmpty(getEditText().getText());
            //当标题和内容都为空，粘贴的字符串长度大于5000时，拦截粘贴操作，弹窗发送文件。
            if (isEmpty) {
                if (length > RichConstants.EMPTY_PASTE_AUTO_FILE_MAX_LENGTH) {
                    if (getViewModel().isScheduledMsgInSet()) {
                        ToastUtils.failure(R.string.tips_scheduled_msg_content_length);
                    } else {
                        showPasteInterceptDialog(pasteString);
                    }
                    return true;
                }
            }
        }
        return false;
    };

    protected final EmotionPanelView.onEmotionItemClickListener mOnEmotionClickListener = (item, packageId, isFromSuggest) -> {
        getViewModel().sendEmotionItemMessage(item, packageId, getViewModel().getChatId(), getViewModel().getType(), getViewModel().getReplyBean());
        EmotionDataManager.getInstance(getViewModel().getUserId()).addToRecentUseEmotion(item, getViewModel().getUserId());
        if (isFromSuggest) {
            getEditText().setText("");
        }
    };

    protected void showPasteInterceptDialog(String pasteString) {
        new DialogUtils.Builder(getContext())
                .setTitle("提示")
                .setContent("粘贴的文本过长，是否转为文档发送到当前会话？")
                .setNegative("取消")
                .setPositive("确定")
                .setAutoCloseAfterClick(true)
                .setPositiveListener(v -> {
                    dismissRichEditDialog();
                    getViewModel().sendTextFileMessage(pasteString, getViewModel().getChatId(), getViewModel().getType(), getViewModel().getReplyBean());
                })
                .build()
                .show();
    }


    private void dismissRichEditDialog() {
        Fragment fragmentByTag = ((FragmentActivity) activity).getSupportFragmentManager().findFragmentByTag(RichEditDialog.TAG);
        if (fragmentByTag instanceof DialogFragment) {
            ((DialogFragment) fragmentByTag).dismiss();
        }
    }

    @Override
    public boolean forceUpdateVisibleItem() {
        return true;
    }

    @Override
    protected void initFragment() {
        super.initFragment();

        initData();

        initMessageList();

        // 初始化聊天表情
        initChatEmotion();

        // 初始化常用语
        initChatCommonWords();

        // 键盘、输入框等初始化
        QMUIKeyboardHelper.setVisibilityEventListener(getActivity(), new KeyboardListener(this));
        initAttachmentHelper();
        initInputTitle();
        initInputContent();
        initKPSwitchPaneView();
        initFuncInputMoreView();
        initChatInputVoiceView();

        initMessageReply();

    }

    /**
     * 初始化软键盘
     */
    public abstract void initAttachmentHelper();

    private void initMessageReply() {
        // 消息回复引用条
        getViewModel().getShowReplyReference().observe(this, message -> {
            getLayoutReplyTipsBinding().getRoot().setVisibility(message == null ? View.GONE : View.VISIBLE);
            getLayoutReplyTipsBinding().setVariable(BR.msg, message);
        });
    }

    @Override
    public void onSendClick() {
        // 检查附件发送状态
        if (attachmentHelper != null && attachmentHelper.hasUploadingOrFailFile()) {
            return;
        }
        String msgTitle = getEditTitleText().getText() == null ? "" : getEditTitleText().getText().toString().trim();
        Editable finalEditableText = getEditText().getFinalEditableText();
        String msgContent = getSendMessageContent(finalEditableText);
        RichTextCmdHelper.addLocalRecentCommand(getEditText().getText());
        sendMessage(msgTitle, msgContent, AtHelper.getAtList(getEditText().getText()), msgTitle.length() + finalEditableText.length(),null);
    }


    private String getSendMessageContent(Editable editable) {
        JSONObject system = ServiceManager.getInstance().getSystemGrayConfigService().getSystemGrayConfig().getValue();
        boolean hasNoNewLine = system != null && system.has(SystemGrayConfigService.GraySettings.RICH_TEXT_NEW_FORMAT);

        StringBuilder content = new StringBuilder();
        new MarkdownGenerator(content).visit(
                decorateHyperlinks(
                        decorateHyperlinks(editable, HYPERLINK_PATTERN),
                        HYPERTEXT_PATTERN, "{\"type\":\"customUrl\"}"
                ),
                !hasNoNewLine,
                true
        );
        return content.toString();
    }

    @Override
    public boolean onSendLongClick() {
        // 进入定时消息设置状态
        startScheduledMsgSet();
        return true;
    }


    private void startScheduledMsgSet() {
        if (getViewModel().checkForScheduledMsgExisting()) {
            ToastUtils.failure(R.string.tips_scheduled_msg_limit);
            return;
        }
        if(!canSetScheduledMsg()){
            return;
        }
        hideNormalSendUI();
        getViewModel().updateScheduledMsgContent(getEditTitleText().getText().toString(), getEditText().getText().toString());
        getEditText().requestFocus();
        if (!QMUIKeyboardHelper.isKeyboardVisible(getActivity())) {
            QMUIKeyboardHelper.showKeyboard(getEditText(), false);
        }
    }

    protected boolean canSetScheduledMsg(){
        if(attachmentHelper != null && attachmentHelper.hasAttachment()){
            ToastUtils.ss("不支持发送文件定时消息");
            return false;
        }
        return true;
    }

    protected void hideNormalSendUI() {
        getChatInputBottomViewBinding().ivVoice.setVisibility(View.GONE);
        getChatInputBottomViewBinding().ivMore.setVisibility(View.GONE);
        getChatInputBottomViewBinding().ivMore.setImageResource(R.drawable.chat_ic_icon_chat_more);
        getChatInputBottomViewBinding().tvSend.setVisibility(View.GONE);
    }

    @Override
    public void onScheduleSendClick() {
        String inputMsgTitle = getEditTitleText().getText() == null ? "" : getEditTitleText().getText().toString().trim();
        Editable inputContent = getEditText().getFinalEditableText();
        String msgContent = getSendMessageContent(inputContent);
        sendScheduleMsgWithCheck(
                inputMsgTitle,
                inputContent.toString(),
                msgContent,
                AtHelper.getAtList(getEditText().getText())
        );
    }

    public boolean sendScheduleMsgWithCheck(String inputMsgTitle, String inputContent, String msgContent, List<String> atIdList) {
        if (getViewModel().checkForScheduledMsgExisting()) {
            ToastUtils.failure(R.string.tips_scheduled_msg_limit);
            return false;
        }
        if (getViewModel().checkForScheduledMsgExpired()) {
            ToastUtils.failure(R.string.tips_scheduled_msg_time_expired);
            return false;
        }
        if (StringUtils.isEmpty(inputContent.trim())) {
            ToastUtils.failure(R.string.tips_scheduled_msg_content_empty);
            return false;
        }
        if (inputContent.length() > RichConstants.EMPTY_PASTE_AUTO_FILE_MAX_LENGTH) {
            ToastUtils.failure(R.string.tips_scheduled_msg_content_length);
            return false;
        }
        sendScheduledMessage(inputMsgTitle, msgContent, atIdList);
        return true;
    }

    @Override
    public void onOpenRichEditClick() {
        SlashCommandHelper.setDisabledChatPointSource(getSlashPointSource());
        openRichTextEditDialog();
    }

    /**
     * 子类实现富文本编辑弹窗的打开
     */
    protected void openRichTextEditDialog() {
    }

    private void initData() {
        getViewModel().getDismissOptionsPopupLiveData().observe(this, dismissPop -> {
            if (dismissPop != null && dismissPop) {
                if (mLongClickPopupWindow != null && mLongClickPopupWindow.isShowing()) {
                    mLongClickPopupWindow.dismiss();
                }
            }
        });
        ServiceManager.getInstance().getMessageService().isInSendingMessage().observe(this, isInSending -> {
            if (isInSending) {
                clickCloseReply();
                ServiceManager.getInstance().getMessageService().setInSendingMessage(false);
            }
        });
        getViewModel().getScheduledMsgSendFinish().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean != null && aBoolean) {
                    clickCloseReply();
                    resetInputMessageView();
                    exitScheduledMsgSet();
                }
            }
        });
        getViewModel().getScheduledMsgResetModel().observe(this, new Observer<ScheduledMsgModel>() {
            @Override
            public void onChanged(ScheduledMsgModel scheduledMsgModel) {
                try {
                    ScheduledMsgContentModel scheduledMsgContentModel = GsonUtils.getGson().fromJson(scheduledMsgModel.getMsgContent(), ScheduledMsgContentModel.class);
                    if (scheduledMsgModel.getMsgType() == MessageConstants.MSG_STICKER) {
                        resetInputContentForSticker(scheduledMsgContentModel.getSid());
                    } else {
                        resetInputContentForText(scheduledMsgContentModel.getTitle(), scheduledMsgContentModel.getText(), scheduledMsgContentModel.getAts());
                    }
                    if (scheduledMsgModel.getReplyId() > 0) {
                        ExecutorFactory.execLocalTask(() -> {
                            ChatMessage replyMessage = ServiceManager.getInstance().getMessageService().getChatMessage(scheduledMsgModel.getReplyId());
                            ExecutorFactory.execMainTask(() -> showReplyTips(replyMessage, scheduledMsgModel.getReplySnapshot(), false));
                        });
                    }
                } catch (Exception e) {
                    TLog.error(TAG, "onScheduledMsgResetClick error", e.getMessage());
                }
            }
        });
    }

    private void initKPSwitchPaneView() {
        KeyboardUtil.attach(activity, getPanelRoot(), isShowing -> {
            destroyTextSelection(mTextSelection);
            updateKeyboardShowing(isShowing);
            if (isShowing) {
                smoothToBottom();
                // 此处延时400ms主要是为了解决部分机型上异常重绘导致弹窗覆盖在键盘上的问题
                ExecutorFactory.execMainTaskDelay(() -> handleCommandPopupResume(true), 400);
            } else {
                handleCommandPopupResume(false);
            }
        });
        KPSwitchConflictUtil.attach(getPanelRoot(), getEditText(), (v, switchToPanel) -> {
                    if (!switchToPanel) {
                        getEditText().requestFocus();
                    }
                    resetEmotionVoiceIcon();
                    if (!getViewModel().isScheduledMsgInSet()) {
                        resetVoiceIcon();
                    }
                    int id = v.getId();
                    if (id == R.id.iv_emotion) {
                        if (!getSubPanelEmotion().isInitialized()) {
                            initChatEmotionPanelView();
                        } else {
                            refreshChatEmotionView();
                        }
                        getChatInputBottomViewBinding().ivEmotion.setImageResource(switchToPanel ? (ThemeUtils.useNewTheme ? R.drawable.chat_ic_icon_chat_emotion_selected : R.drawable.chat_ic_icon_chat_emotion_pressed) : R.drawable.chat_ic_icon_chat_emotion);
                        new PointUtils.BuilderV4()
                                .name("chat-mobile-function-click")
                                .params("type", 2)
                                .point();
                    } else if (id == R.id.iv_more) {
                        getChatInputBottomViewBinding().ivMore.setImageResource(switchToPanel ? (ThemeUtils.useNewTheme ? R.drawable.chat_ic_icon_chat_more_selected : R.drawable.chat_ic_icon_chat_more_blue) : R.drawable.chat_ic_icon_chat_more);
                        new PointUtils.BuilderV4()
                                .name("chat-mobile-function-click")
                                .params("type", 3)
                                .point();
                        if (switchToPanel) {
                            //判断是否有截图
                            showScreenShotSelectDialog();
                        }
                    } else if (id == R.id.tv_common_words) {
                        setCommonWordStyle(switchToPanel);
                        new PointUtils.BuilderV4()
                                .name("chat-mobile-function-click")
                                .params("type", 4)
                                .point();
                        if (switchToPanel) {
                            getViewModel().getCommonWordsList();
                        }
                    }
                },
                new KPSwitchConflictUtil.SubPanelAndTrigger(getViewChatCommonWordsSubSpanBinding().getRoot(), getChatInputBottomViewBinding().tvCommonWords),
                new KPSwitchConflictUtil.SubPanelAndTrigger(getSubPanelEmotion(), getChatInputBottomViewBinding().ivEmotion),
                new KPSwitchConflictUtil.SubPanelAndTrigger(getSubPanelFunc(), getChatInputBottomViewBinding().ivMore));
    }

    private void initChatEmotion() {
        ServiceManager.getInstance().getProfileService().getEmotionChange().observe(this, emotionChanged -> {
            if (emotionChanged != null && emotionChanged && getSubPanelEmotion() != null && getSubPanelEmotion().isInitialized()) {
                refreshChatEmotionView();
                ServiceManager.getInstance().getProfileService().setEmotionChange(false);
            }
        });
        //重新刷新最近使用emoji缓存
        new EmotionDataImp().requestUpdateRecentEmojiData(getViewModel().getUserId());
    }

    protected void initMessageList() {

        mMessageAdapter = new ChatMessageAdapter(getViewLifecycleOwner(), this, getViewModel());

        getMessageListRecyclerView().addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    resetEmotionVoiceIcon();
                    KPSwitchConflictUtil.hidePanelAndKeyboard(getPanelRoot());
                }
            }
        });
        getMessageListRecyclerView().getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                pointChatPageDrawTimeFromMain();
                pointChatPageDrawTimeFromPush();
                getMessageListRecyclerView().getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
    }

    /**
     * 会话属性相关数据初始化
     */
    protected void initChatPropsData() {
        // 置顶消息
        getViewModel().queryStickyTopMsgInfo(this);
        // 定时消息
        getViewModel().initChatScheduledMsg(this, this::updateSendTvUI);
    }

    @Override
    public void clickLeft(View view) {
        QMUIKeyboardHelper.hideKeyboard(getEditText());
        activity.setResult(Activity.RESULT_OK);
        AppUtil.finishActivity(activity);
    }

    @Override
    public void clickRight(View view) {
        stopAudioPlay();
        QMUIKeyboardHelper.hideKeyboard(getEditText());
    }

    @Override
    public void onStop() {
        super.onStop();
        getViewModel().saveDraft(getEditTitleText().getText(), getEditText().getFinalEditableText());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        SlashCommandHelper.resetRecentCommandList();
        if (getDataBinding() != null) {
            QMUIKeyboardHelper.hideKeyboard(getEditText());
        }
        EmotionDataManager.getInstance(getViewModel().getUserId()).clearAllCache();
        getViewModel().addScheduledMsg();

        if (attachmentHelper != null) {
            attachmentHelper.destroy();
            attachmentHelper = null;
        }
    }

    private void initChatCommonWords() {
        ServiceManager.getInstance().getSettingService().getComWordsShowLiveData().observe(this, integer -> {
            if (integer != null) {
                boolean show = integer == JurisdictionUtils.IDENTITY_VISIBLE;
                getViewModel().commonWordsSwitchOn.set(show);
                getViewModel().getCommonWordsList();
                getViewChatCommonWordsSubSpanBinding().setCallback(InputChatFragment.this);
                getViewChatCommonWordsSubSpanBinding().setEmpty(getViewModel().isCommonWordsEmpty);
            }
        });
        getViewModel().getCommonWordsLiveData().observe(this, commonWordsBeans -> {
            boolean isEmpty = LList.isEmpty(commonWordsBeans);
            getViewModel().isCommonWordsEmpty.set(isEmpty);
            if (commonWordsBeans != null) {
                mChatCommonWordsAdapter.submitList(commonWordsBeans);
                getViewChatCommonWordsSubSpanBinding().rvCommonWords.postDelayed(() -> getViewChatCommonWordsSubSpanBinding().rvCommonWords.scrollToPosition(0), 200);
            }
        });

        mChatCommonWordsAdapter = new CommonAdapter<>(new DiffUtil.ItemCallback<ChatExpandableCommonWordsBean>() {
            @Override
            public boolean areItemsTheSame(@NonNull ChatExpandableCommonWordsBean oldItem, @NonNull ChatExpandableCommonWordsBean newItem) {
                return TextUtils.equals(oldItem.getWordsId(), newItem.getWordsId());
            }

            @Override
            public boolean areContentsTheSame(@NonNull ChatExpandableCommonWordsBean oldItem, @NonNull ChatExpandableCommonWordsBean newItem) {
                return false;
            }
        });
        mChatCommonWordsAdapter.setLayoutId(R.layout.chat_item_common_words);
        mChatCommonWordsAdapter.setVariableId(BR.item);
        mChatCommonWordsAdapter.addCallback(BR.callback, this);
        getViewChatCommonWordsSubSpanBinding().rvCommonWords.setLayoutManager(new LinearLayoutManagerWrapper(getContext()));
        getViewChatCommonWordsSubSpanBinding().rvCommonWords.setAdapter(mChatCommonWordsAdapter);
    }

    private void destroyTextSelection(TextSelection textSelection) {
        if (textSelection != null) {
            textSelection.destroy();
        }
    }

    protected void resetVoiceIcon() {
        getChatInputBottomViewBinding().ivVoice.setVisibility(View.VISIBLE);
        getChatInputBottomViewBinding().ivVoice.setImageResource(R.drawable.chat_ic_icon_chat_voice);
        getChatInputBottomViewBinding().btnVoice.setVisibility(View.GONE);
        getChatInputBottomViewBinding().inputContainer.setVisibility(View.VISIBLE);
    }

    /**
     * 初始化聊天输入表情相关view
     */
    protected void initChatEmotionPanelView() {
        EmotionDataManager emotionDataManager = EmotionDataManager.getInstance(getViewModel().getUserId());
        emotionDataManager.resetSpanCount(KeyboardUtil.getValidPanelHeight(activity.getApplicationContext()));
        EmotionPanelView emotionPanelView = getSubPanelEmotion();
        emotionPanelView.setInputView(getEditText());
        emotionPanelView.setUserId(getViewModel().getUserId());
        emotionPanelView.setEmotionCategories(emotionDataManager.getAllEmotionCategories(getViewModel().getUserId()));
        emotionPanelView.setOnEmotionClickListener(mOnEmotionClickListener);
        emotionPanelView.init();
        //刷新数据
        emotionDataManager.addDataChangeListener(() -> {
            List<EmotionCategory> allEmotionList = emotionDataManager.getAllEmotionCategories(getViewModel().getUserId());
            emotionPanelView.notifyRefresh(allEmotionList);
        });
        emotionPanelView.setInitialized(true);
    }

    /**
     * 初始化聊天中输入更多相关view
     */
    private void initFuncInputMoreView() {
        getSubPanelFunc().setFunItemClickListener(itemFunc -> {
            switch (itemFunc.getType()) {
                case ItemFunc.PHOTO:
                    PhotoSelectManager.jumpForGalleryFromChatResult((FragmentActivity) activity, (fileList, originalEnable) ->
                            getViewModel().handleMediaFilesSend(
                                    fileList,
                                    getViewModel().getChatId(),
                                    getViewModel().getType(),
                                    getViewModel().getReplyBean()
                            ));
                    break;
                case ItemFunc.CAMERA:
                    PhotoSelectManager.jumpForCameraResult((FragmentActivity) activity, cameraFilePath -> {
                        //上传图片
                        if (cameraFilePath != null) {
                            List<File> files = new ArrayList<>();
                            files.add(cameraFilePath);
                            getViewModel().handleMediaFilesSend(files, getViewModel().getChatId(), getViewModel().getType(), getViewModel().getReplyBean());
                        }
                    });
                    break;
                case ItemFunc.FILE:
                    if (getViewModel().isShowICloud()) {
                        showFileSelectOptionsDialog();
                    } else {
                        startSelectLocalFile();
                    }
                    break;
                case ItemFunc.SCHEDULED_MSG:
                    startScheduledMsgSet();
                    break;
                case ItemFunc.USER_CARD:
                    handleFuncUserCardClick();
                    break;
                default:
                    if (getFuncItemExtraClickListener() != null) {
                        getFuncItemExtraClickListener().onFuncItemExtraClick(itemFunc.getType());
                    }
                    break;
            }
            if (itemFunc.getNewPointCode() > 0) {
                new PointUtils.BuilderV4()
                        .name("chat-mobile-function-click")
                        .params("type", itemFunc.getNewPointCode())
                        .point();
            }
        });
        getSubPanelFunc().notifyDataChange(getViewModel().getFuncItemList());
    }

    /**
     * 处理名片点击
     */
    protected void handleFuncUserCardClick() {

        ConversationSelectBean conversationSelectBean = getConversationSelectSourceBean();
        if (conversationSelectBean == null) {
            return;
        }

        SelectConversationParams params = new SelectConversationParams()
                .setOptionId(SelectOption.OPTION_SEND_CARD)
                .<SelectConversationParams>setTitle(getResources().getString(R.string.select_conversation))
                .<SelectConversationParams>setOrgVisible(SelectBaseParams.VISIBLE)
                .setCreateGroupVisible(SelectBaseParams.INVISIBLE)
                .setShowRightTitleVisible(SelectBaseParams.INVISIBLE)
                .<SelectConversationParams>setSearchVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setOtherVisible(SelectBaseParams.VISIBLE)
                .setShowFileHelper(SelectBaseParams.INVISIBLE)
                .setSourceConversation(conversationSelectBean)
                .setGroupCardSource(1);

        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        AppUtil.startUri(activity, SelectPageRouter.SELECT_CONVERSATION_ACTIVITY, bundle, ActivityAnimType.UP_GLIDE);
        resetEmotionVoiceIcon();
        KPSwitchConflictUtil.hidePanelAndKeyboard(getPanelRoot());
    }

    /**
     * 初始化输入语音相关的view
     */
    @SuppressLint("ClickableViewAccessibility")
    private void initChatInputVoiceView() {
        getChatInputBottomViewBinding().btnVoice.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    TLog.info(TAG, "btnVoice onTouch,ACTION_DOWN");
                    IAudioShortHandService service = Router.getService(
                            IAudioShortHandService.class,
                            AudioShortHandPageRouter.AUDIO_SHORTHAND_SERVICE);
                    if (service != null && service.getShortHanding()) {
                        ToastUtils.ss("AI速记正在录音，无法发送语音消息");
                        return true;
                    }
                    getChatInputBottomViewBinding().btnVoice.setBackgroundResource(R.drawable.bg_et_input_press);
                    if (mHiVoiceView == null) {
                        mHiVoiceView = new HiVoiceView(activity);
                        mHiVoiceView.setOnRecordCallBack((path, totalTimeMillSecond, voiceText, isVoiceText) ->
                                getViewModel().sendAudio(
                                        path,
                                        totalTimeMillSecond / 1000,
                                        getViewModel().getChatId(),
                                        getViewModel().getType(),
                                        getViewModel().getReplyBean(),
                                        voiceText,
                                        isVoiceText
                                ));
                    }
                    mHiVoiceView.responseTouchEvent(event);
                    break;
                case MotionEvent.ACTION_MOVE:
                    if (mHiVoiceView != null) {
                        mHiVoiceView.responseTouchEvent(event);
                    }
                    break;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    TLog.info(TAG, "btnVoice onTouch,ACTION_UP");
                    getChatInputBottomViewBinding().btnVoice.setBackgroundResource(R.drawable.bg_et_input);
                    if (mHiVoiceView != null) {
                        mHiVoiceView.responseTouchEvent(event);
                    }
                    break;
                default:
                    break;
            }
            return true;
        });
    }

    protected void showFileSelectOptionsDialog() {
        if (mFileSelectBottomDialog == null) {
            mFileSelectBottomDialog = new BottomListDialog.Builder(activity)
                    .setData(getViewModel().getFileSelectItemList())
                    .setCanceledOnTouchOutside(true)
                    .setSpanCount(2)
                    .setItemLayout(R.layout.item_bottom_new)
                    .setOnBottomItemClickListener((view, pos, bottomBean) -> {
                        switch (bottomBean.type) {
                            case ItemFunc.FILE:
                                startSelectLocalFile();
                                mFileSelectBottomDialog.dismiss();
                                break;
                            case ItemFunc.ZHISHU_FILE:
                                startSelectZhishuFile();
                                mFileSelectBottomDialog.dismiss();
                                break;
                            default:
                                break;
                        }
                    })
                    .create();
        }
        mFileSelectBottomDialog.show();
    }

    private void startSelectZhishuFile() {
        if (ServiceManager.getInstance().getSecurityService().isForbidden(BusinessModuleEnum.ZHI_SHU)) {
            ToastUtils.failure(R.string.no_permission_access);
            return;
        }
        Bundle paramsBundle = new Bundle();
        paramsBundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, new ICloudSelectParams()
                .setMultiSelect(true)
                .setActionType(ICloudSelectParams.ACTION_TYPE_SEND)
                .setChatId(getViewModel().getChatId())
                .setChatType(getViewModel().getType())
        );
        AppUtil.startFragmentUriForResult(
                this,
                WorkPageRouter.I_CLOUD_SELECT_ACTIVITY,
                RequestCodeConstants.REQUEST_CODE_SELECT_I_CLOUD,
                paramsBundle
        );
    }

    protected void setupSlashCommand() {
        ViewKt.doOnLayout(getCommandPopupAtView(), view -> {
            mAnchorHeight = view.getMeasuredHeight();
            return null;
        });
        getViewModel().getGroupSlashCmdCount().observe(this, integer -> {
            if (mSlashCommandCount == -1 && integer > 0) {
                showKeyboardBySlashCommand(integer);
            }
            mSlashCommandCount = integer;
        });
        getViewModel().getRecentSlashCmdLiveData().observe(this, SlashCommandHelper::updateRecentCommandList);
        getEditText().addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                handleSlashCmdPopupShow(s, getViewModel().getChatId());
            }
        });
        getViewModel().requestRecentCommands();
    }

    protected boolean canShowCommandPop(Editable newEditable) {
        if (mSlashCommandCount <= 0) {
            return false;
        }
        return SlashCommandHelper.canShowCommandPopByKeyword(newEditable);
    }

    protected void handleSlashCmdPopupShow(Editable newEditable, String chatId) {
        if (!canShowCommandPop(newEditable)) {
            dismissCommandPopup();
            return;
        }
        if (mSlashCommandPopup == null) {
            mSlashCommandPopup = new SlashCommandPopup(getActivity(), slashCommandVO -> RichTextCmdHelper.insertSlashCommand(getEditText(), slashCommandVO));
            mSlashCommandPopup
                    .updateAnchorView(getCommandPopupAtView())
                    .updateAnchorHeight(mAnchorHeight)
                    .updatePointSource(getSlashPointSource())
                    .setCommandRobotInfo(getViewModel().mCommandRobotInfo);
        }
        mSlashCommandPopup.showOrUpdate(this, newEditable.toString(), chatId, getViewModel().getType() == MessageConstants.MSG_GROUP_CHAT);
    }

    protected int getSlashPointSource() {
        return SlashCommandHelper.SOURCE_CHAT_INPUT;
    }

    protected void dismissCommandPopup() {
        if (mSlashCommandPopup != null && mSlashCommandPopup.isShow()) {
            mSlashCommandPopup.dismiss();
        }
    }

    protected void handleCommandPopupResume(boolean isKeyboardShowing) {
        if (isKeyboardShowing) {
            handleSlashCmdPopupShow(getEditText().getEditableText(), getViewModel().getChatId());
        } else {
            dismissCommandPopup();
        }
    }

    protected void updateKeyboardShowing(boolean isKeyboardShowing) {
        if (mSlashCommandPopup != null) {
            mSlashCommandPopup.updateKeyboardShowing(isKeyboardShowing);
        }
    }

    protected void initInputTitle() {
        getEditTitleText().addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                getEditTitleText().setVisibility(TextUtils.isEmpty(s) ? View.GONE : View.VISIBLE);
                if (getViewModel().isScheduledMsgInSet()) {
                    getViewModel().updateScheduledMsgTitle(s.toString());
                }
            }
        });

        getEditTitleText().setFilters(new InputFilter[]{new LengthNoticeFilter(5000)});

        if (getEditTitleText() instanceof InterceptEditTextView) {
            ((InterceptEditTextView) getEditTitleText()).setPasteInterceptListener(mPasteInterceptListener);
        }
    }

    private void initInputContent() {
        getEditText().addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String text = s.toString();
                if (count > 0) {
                    // 延迟显示表情联想弹框，等待键盘完全弹出
                    scheduleEmotionSuggestPopup(text);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (getViewModel().isScheduledMsgInSet()) {
                    getViewModel().updateScheduledMsgContent(s.toString().trim());
                    if (s.toString().isEmpty()) {
                        getEditText().setLimitHint(mInputHint);
                    }
                }
                updateSendTvUI();
                if (StringUtils.isNotEmpty(s.toString())) {
                    getEditText().setHint("");
                }
            }
        });

        getEditText().addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                if (v.getWidth() == mInputWidth) {
                    return;
                }
                mInputWidth = v.getWidth();
                if (getEditText().getText() != null && StringUtils.isNotEmpty(getEditText().getText().toString())) {
                    return;
                }
                getEditText().setLimitHint(mInputHint);
            }
        });

        new ToolsManager(getEditText(), getViewModel().getType() == MessageConstants.MSG_GROUP_CHAT).registerStyle();

        getEditText().setFilters(new InputFilter[]{new LengthNoticeFilter(10000)});

        getEditText().setPasteInterceptListener(mPasteInterceptListener);
    }

    public void updateSendTvUI() {
        if (getViewModel().isScheduledMsgInSet()) {
            getChatInputBottomViewBinding().ivVoice.setVisibility(View.GONE);
            getChatInputBottomViewBinding().tvSend.setVisibility(View.GONE);
            getChatInputBottomViewBinding().ivMore.setVisibility(View.GONE);
        } else {
            boolean canSend = canSend();
            getChatInputBottomViewBinding().tvSend.setVisibility(canSend ? View.VISIBLE : View.GONE);
            getChatInputBottomViewBinding().ivMore.setVisibility(!canSend ? View.VISIBLE : View.GONE);
        }
    }

    protected boolean canSend(){
        boolean canSend = hasInputContent();
        if(attachmentHelper == null){
            return canSend;
        }else{
            return canSend || LList.isNotEmpty(attachmentHelper.getAttachmentList());
        }
    }

    protected boolean hasInputContent(){
        return getEditText().getText() != null && !TextUtils.isEmpty(getEditText().getText().toString().trim());
    }

    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        if (actionId == EditorInfo.IME_ACTION_SEND && !TextUtils.isEmpty(v.getText().toString())) {
            onSendClick();
            return true;
        }
        return false;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
    }

    /**
     * 延迟显示表情联想弹框，等待键盘完全弹出
     */
    private void scheduleEmotionSuggestPopup(String text) {
        // 取消之前的延迟任务
        if (mEmotionSuggestRunnable != null) {
            mEmotionHandler.removeCallbacks(mEmotionSuggestRunnable);
        }

        mPendingEmotionText = text;

        // 如果键盘已经显示，延迟一小段时间再显示表情弹框
        // 如果键盘还没显示，等待键盘显示后再处理
        if (mIsKeyboardShowing) {
            mEmotionSuggestRunnable = () -> showEmotionSuggestPopupForText(mPendingEmotionText);
            mEmotionHandler.postDelayed(mEmotionSuggestRunnable, 200); // 延迟200ms确保键盘完全显示
        } else {
            // 键盘还没显示，等待键盘显示事件
            mEmotionSuggestRunnable = () -> showEmotionSuggestPopupForText(mPendingEmotionText);
            mEmotionHandler.postDelayed(mEmotionSuggestRunnable, 500); // 延迟500ms等待键盘弹出
        }
    }

    /**
     * 为指定文本显示表情联想弹框
     */
    private void showEmotionSuggestPopupForText(String text) {
        if (text == null || text.isEmpty()) {
            return;
        }

        List<EmotionItem> items = EmotionDataManager.getInstance(getViewModel().getUserId()).getSuggestedEmotions(text, getViewModel().getUserId());
        initEmotionSuggestPopup(items);
    }

    private void initEmotionSuggestPopup(List<EmotionItem> items) {
        if (items != null && items.size() > 0) {
            if (mEmotionSuggestPopup == null) {
                mEmotionSuggestPopup = new EmotionSuggestPopup(activity, EmotionBasePopup.DIRECTION_TOP, mOnEmotionClickListener);
                mEmotionSuggestPopup.create(QMUIDisplayHelper.dp2px(activity, 100), items);
            } else {
                mEmotionSuggestPopup.updateData(QMUIDisplayHelper.dp2px(activity, 100), items);
            }
            mEmotionSuggestPopup.show(getEditText());
        }
    }

    public void pointChatPageDrawTimeFromPush() {
        long pushJumpTime = TimeDifferenceUtil.getInstance().finish(TimeTag.PUSH_JUMP);
        if (pushJumpTime > 0) {
            new PointUtils.BuilderV4().name("perform-page-load")
                    .params("scenerio", 10)
                    .params("type", LBase.getApplication().isColdLaunch() ? "2" : "1")
                    .params("load_time", pushJumpTime + "")
                    .point();
        }
    }

    public void pointChatPageDrawTimeFromMain() {
        long loadMessageTime = TimeDifferenceUtil.getInstance().finish(TimeTag.LOAD_MESSAGE);
        if (loadMessageTime > 0) {
            new PointUtils.BuilderV4().name("perform-page-load")
                    .params("chat_type", getViewModel().getType())
                    .params("scenerio", 1)
                    .params("load_time", loadMessageTime + "")
                    .point();
        }
    }

    /**
     * 刷新表情面板
     */
    protected void refreshChatEmotionView() {
        EmotionDataManager emotionDataManager = EmotionDataManager.getInstance(getViewModel().getUserId());
        getSubPanelEmotion().notifyRefresh(emotionDataManager.getLatestAllEmotionList(getViewModel().getUserId()));
        getSubPanelEmotion().getEmotionPageAdapter().setRecentlyChanged(false);
    }

    /**
     * 处理消息回复
     */
    protected void handleMessageReply(ChatClickOptionBean chatClickOptionBean, ChatMessage replyMessage) {
        if (replyMessage.getStatus() <= MessageConstants.MSG_STATE_FAILURE) {
            return;
        }
        String senderId = replyMessage.getSender().getSenderId();
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(senderId);
        if (contact != null && contact.getStatus() == Contact.USER_STATUS_RESIGN) {
            ToastUtils.failure(getString(R.string.chat_msg_user_resigned));
        } else {
            showReplyTips(replyMessage, getReplyContent(chatClickOptionBean), true);
            if (!QMUIKeyboardHelper.isKeyboardVisible(getActivity())) {
                QMUIKeyboardHelper.showKeyboard(getEditText(), true);
            }
            resetVoiceIcon();
            resetEmotionVoiceIcon();
        }
    }

    private String getReplyContent(ChatClickOptionBean chatClickOptionBean) {
        TextSelection textSelection = chatClickOptionBean.textSelection;
        if (textSelection != null && textSelection.hasSelected() && !textSelection.isAllSelected()) {
            String replyContent = textSelection.getSelectionContent();
            return (!textSelection.isSelectFromStart() ? "..." : "") +
                    replyContent +
                    (!textSelection.isSelectToEnd() ? "..." : "");
        }
        return null;
    }

    /**
     * 处理消息转发
     */
    protected void handleMessageForward(ChatClickOptionBean chatClickOptionBean, ChatMessage chatMessage) {
        if (chatClickOptionBean.textSelection != null && !chatClickOptionBean.textSelection.isAllSelected()) {
            handleTextMessageForward(chatClickOptionBean, chatMessage);
        } else if (chatClickOptionBean.handleTranslated) {
            handleTranslatedMessageForward(chatMessage);
        } else {
            handleSingleMessageForward(chatMessage);
        }
    }

    /**
     * 处理文件/图片/视频等存到直书
     */
    protected void handleSaveToZhishu(ChatMessage chatMessage) {
        if (ServiceManager.getInstance().getSecurityService().isForbidden(BusinessModuleEnum.ZHI_SHU)) {
            ToastUtils.failure(R.string.no_permission_access);
            return;
        }
        ICloudSelectParams cloudSelectParams = new ICloudSelectParams().setMultiSelect(false).setActionType(ICloudSelectParams.ACTION_TYPE_SAVE);
        switch (chatMessage.getMediaType()) {
            case MessageConstants.MSG_PIC:
                cloudSelectParams.setCategory(ResourceBean.CATEGORY_PIC);
                cloudSelectParams.setContent(((MessageForPic) chatMessage).getRealUrl());
                break;
            case MessageConstants.MSG_VIDEO:
                cloudSelectParams.setCategory(ResourceBean.CATEGORY_VIDEO);
                cloudSelectParams.setContent(((MessageForVideo) chatMessage).getVideoInfo());
                break;
            case MessageConstants.MSG_FILE:
                cloudSelectParams.setCategory(ResourceBean.CATEGORY_FOLDER_FILE);
                cloudSelectParams.setContent(((MessageForFile) chatMessage).getFileInfo());
                break;
            default:
                break;
        }
        cloudSelectParams.setMessageId(chatMessage.getMid());
        Bundle bundleICloud = new Bundle();
        bundleICloud.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, cloudSelectParams);
        AppUtil.startUri(activity, WorkPageRouter.I_CLOUD_SELECT_ACTIVITY, bundleICloud, ActivityAnimType.UP_GLIDE);
    }

    private void handleTextMessageForward(ChatClickOptionBean chatClickOptionBean, ChatMessage chatMessage) {
        SelectConversationParams params = new SelectConversationParams()
                .setSendMessageType(SendMessageContent.TYPE_FORWARD_MESSAGE_TEXT)
                .<SelectConversationParams>setTitle(getResources().getString(R.string.select_contact))
                .setOptionId(SelectOption.OPTION_FORWARD_MESSAGE)
                .<SelectConversationParams>setOrgVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setSearchVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setOtherVisible(SelectBaseParams.VISIBLE)
                .setMaxSelectCount(50);
        List<Hypertext> titledLinks = chatMessage instanceof MessageWithHypertext
                ? ((MessageWithHypertext) chatMessage).getTitledLinks()
                : Collections.emptyList();
        String msgContent = MessageUtils.cleanAtStartEnd(chatClickOptionBean.textSelection.getSelectionContent());
        params.sendMessageContent.content = msgContent;
        params.sendMessageContent.msgShowContent = MLinkEmotionTextUtils.replaceHyperlink(msgContent, titledLinks);
        Bundle bundleSelect = new Bundle();
        bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        AppUtil.startFragmentUriForResult(
                this,
                SelectPageRouter.SELECT_CONVERSATION_ACTIVITY,
                RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_CHAT_FORWARD,
                bundleSelect
        );
    }

    private void handleTranslatedMessageForward(ChatMessage chatMessage) {
        SelectConversationParams params = new SelectConversationParams()
                .setSendMessageType(SendMessageContent.TYPE_FORWARD_MESSAGE_TEXT)
                .<SelectConversationParams>setTitle(getResources().getString(R.string.select_contact))
                .setOptionId(SelectOption.OPTION_FORWARD_MESSAGE)
                .<SelectConversationParams>setOrgVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setSearchVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setOtherVisible(SelectBaseParams.VISIBLE)
                .setMaxSelectCount(50);
        List<Hypertext> titledLinks = chatMessage instanceof MessageWithHypertext
                ? ((MessageWithHypertext) chatMessage).getTitledLinks()
                : Collections.emptyList();
        if (chatMessage instanceof MessageForMarkdownText) {
            MessageForMarkdownText mdMsg = (MessageForMarkdownText) chatMessage;
            boolean noTitle = TextUtils.isEmpty(mdMsg.getTranslateTitle());
            String msgContent = RichTextEscapes.toPlainText(mdMsg.getTranslateContent()).toString();
            if (!mdMsg.getAtIds().isEmpty()) {
                msgContent = MLinkEmotionTextUtils.replaceContactRemark(msgContent, mdMsg.getAtIds());
            }
            String mdText = noTitle ? msgContent : mdMsg.getTitle() + (TextUtils.isEmpty(msgContent) ? "" : ("\n" + msgContent));
            params.sendMessageContent.content = mdText;
            params.sendMessageContent.msgShowContent = MLinkEmotionTextUtils.replaceHyperlink(mdText, titledLinks);
        } else if (chatMessage instanceof MessageForText) {
            MessageForText textMsg = (MessageForText) chatMessage;
            String msgContent = textMsg.getTranslateContent();
            if (!textMsg.getAtIds().isEmpty()) {
                msgContent = MLinkEmotionTextUtils.replaceContactRemark(msgContent, textMsg.getAtIds());
            }
            params.sendMessageContent.content = msgContent;
            params.sendMessageContent.msgShowContent = MLinkEmotionTextUtils.replaceHyperlink(msgContent, titledLinks);
        } else if (chatMessage instanceof MessageForAudio) {
            String text = ((MessageForAudio) chatMessage).getAudioInfo().getTranslateContent();
            params.sendMessageContent.content = text;
            params.sendMessageContent.msgShowContent = text;
        }
        Bundle bundleSelect = new Bundle();
        bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        AppUtil.startFragmentUriForResult(
                this,
                SelectPageRouter.SELECT_CONVERSATION_ACTIVITY,
                RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_CHAT_FORWARD,
                bundleSelect
        );
    }

    /**
     * 单个消息转发处理
     */
    protected void handleSingleMessageForward(ChatMessage chatMessage) {
        String msgContent = new MessageSummaryVisitor().blockingGetSummary(chatMessage, false);
        String msgRichTitle = "";
        if (chatMessage instanceof MessageForRichText) {
            String title = ((MessageForRichText) chatMessage).getTitle();
            if (!TextUtils.isEmpty(title)) {
                msgRichTitle = title;
            }
        } else if (chatMessage instanceof MessageForMarkdownText) {
            String title = ((MessageForMarkdownText) chatMessage).getTitle();
            if (!TextUtils.isEmpty(title)) {
                msgRichTitle = title;
            }
        }
        if (chatMessage instanceof MessageForUserCard) {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(((MessageForUserCard) chatMessage).getUserId());
            msgContent = SendMessageContent.getCardString(contact);
        }

        ForwardMessageBean forwardMessageBean = new ForwardMessageBean();
        forwardMessageBean.msgId = chatMessage.getMid();
        forwardMessageBean.mediaType = chatMessage.getMediaType();
        SelectConversationParams params = new SelectConversationParams()
                .setSendMessageType(SendMessageContent.TYPE_FORWARD_SINGLE)
                .<SelectConversationParams>setTitle(getResources().getString(R.string.select_contact))
                .setOptionId(SelectOption.OPTION_FORWARD_MESSAGE)
                .<SelectConversationParams>setOrgVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setSearchVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setOtherVisible(SelectBaseParams.VISIBLE)
                .setSourceConversation(getConversationSelectSourceBean())
                .setMaxSelectCount(50);
        params.sendMessageContent.content = forwardMessageBean;
        params.sendMessageContent.msgRichTitle = msgRichTitle;
        params.sendMessageContent.msgShowContent = msgContent;
        Bundle bundleSelect = new Bundle();
        bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        AppUtil.startFragmentUriForResult(
                this,
                SelectPageRouter.SELECT_CONVERSATION_ACTIVITY,
                RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_CHAT_FORWARD,
                bundleSelect
        );
    }

    /**
     * 多个消息转发处理
     *
     * @param selectedMsgList 选中的消息列表
     * @param type            合并转发类型,1:逐条转发,2:合并转发
     */
    protected void handleMultiMessageForward(List<ChatMessage> selectedMsgList, int type) {
        int forwardType;
        ArrayList<Long> selectedMsgIdList = null;
        if (type == Constants.FORWARD_MERGE) {
            forwardType = SendMessageContent.TYPE_FORWARD_MULTIPLE_BY_MERGE;
        } else {
            forwardType = SendMessageContent.TYPE_FORWARD_MULTIPLE_ITEM_BY_ITEM;
            selectedMsgIdList = new ArrayList<>(selectedMsgList.size());
            for (int i = 0; i < selectedMsgList.size(); i++) {
                selectedMsgIdList.add(selectedMsgList.get(i).getMid());
            }
        }

        SelectConversationParams params = new SelectConversationParams()
                .setSendMessageType(forwardType)
                .<SelectConversationParams>setTitle(getResources().getString(R.string.select_contact))
                .<SelectConversationParams>setOrgVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setSearchVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setOtherVisible(SelectBaseParams.VISIBLE)
                .setMaxSelectCount(50);
        if (type == Constants.FORWARD_MERGE) {
            params.sendMessageContent.content = getViewModel().makeChatShareInfo(selectedMsgList);
        } else {
            params.sendMessageContent.content = selectedMsgIdList;
        }
        params.sendMessageContent.msgShowContent = SendMessageContent.getForwardShowTitle(getViewModel().getLinkNames(selectedMsgList), type);

        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        AppUtil.startFragmentUriForResult(
                this,
                SelectPageRouter.SELECT_CONVERSATION_ACTIVITY,
                RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_MULTIPLE_FORWARD,
                bundle
        );
    }

    /**
     * 展示回复消息提示
     *
     * @param replyMessage    回复消息
     * @param replyContent    选取的回复消息内容
     * @param appendReplyUser 输入框是否展示回复消息发送人
     */
    protected void showReplyTips(ChatMessage replyMessage, String replyContent, boolean appendReplyUser) {
        getViewModel().updateReplyDraft(new Pair<>(replyMessage, replyContent));
    }

    protected void showDeletedSingleMessageDialog(Long id) {
        mDeleteChatDialog = new DialogUtils.Builder(activity)
                .setContent(isMatchDelMsgForever() ? getDelMsg() : getResources().getString(R.string.chat_delete_message_tips))
                .setCancelable(true)
                .setPositive(R.string.sure)
                .setPositiveListener(v -> {
                    List<Long> ids = Collections.singletonList(id);
                    beforeDeleteMsg(ids);
                    getViewModel().deleteMessages(ids);
                    mDeleteChatDialog.dismiss();
                })
                .setNegative(R.string.cancel)
                .setNegativeListener(v -> mDeleteChatDialog.dismiss())
                .build();
        mDeleteChatDialog.show();
    }

    /**
     * 当前用户是否可以彻底删除消息(删除后，群内其他用户也不可见)，如果是需要走不同的文案
     * @return
     */
    private boolean isMatchDelMsgForever() {
        JSONObject system = ServiceManager.getInstance().getSystemGrayConfigService().getSystemGrayValue();
        return system != null && system.has(SystemGrayConfigService.GraySettings.MSG_DEL_AUTH_CONFIG);
    }

    /**
     * 获取配置的文案
     * @return
     */
    private String getDelMsg() {
        JSONObject system = ServiceManager.getInstance().getSystemGrayConfigService().getSystemGrayValue();
        String value = GrayConfigGetHelper.getSingleValue(system, SystemGrayConfigService.GraySettings.MSG_DEL_AUTH_CONFIG, SystemGrayConfigService.GraySettings.MSG_DEL_AUTH_CONFIG_TOAST);
        return TextUtils.isEmpty(value) ? getResources().getString(R.string.chat_delete_message_tips) : value;
    }

    public void beforeDeleteMsg(List<Long> ids) {

    }

    /**
     * 发送中/发送失败的消息删除确认弹窗
     */
    protected void showMsgDeleteConfirmDialog(ChatMessage chatMessage) {
        mDeleteChatDialog = new DialogUtils.Builder(activity)
                .setContent(R.string.chat_cancel_message_tips)
                .setCancelable(true)
                .setPositive(R.string.delete)
                .setPositiveListener(v -> {
                    if (chatMessage.isSending()) {
                        getViewModel().cancelSendingMsg(chatMessage);
                    } else if (chatMessage.isFailed()) {
                        beforeDeleteMsg(Collections.singletonList(chatMessage.getMid()));
                        getViewModel().deleteLocalMessage(chatMessage);
                    } else {
                        List<Long> ids = Collections.singletonList(chatMessage.getMid());
                        beforeDeleteMsg(ids);
                        getViewModel().deleteMessages(ids);
                    }
                    mDeleteChatDialog.dismiss();
                })
                .setNegative(R.string.cancel)
                .setNegativeListener(v -> mDeleteChatDialog.dismiss())
                .build();
        mDeleteChatDialog.show();
    }

    protected void showDeletedMultipleMessagesDialog(List<Long> onlineMessageIds) {
        mDeleteChatDialog = new DialogUtils.Builder(activity)
                .setContent(isMatchDelMsgForever() ? getDelMsg() : getResources().getString(R.string.chat_delete_message_tips))
                .setCancelable(true)
                .setPositive(R.string.sure)
                .setPositiveListener(v -> {
                    beforeDeleteMsg(onlineMessageIds);
                    getViewModel().deleteMessages(onlineMessageIds);
                    resetMultipleStatus();
                    mDeleteChatDialog.dismiss();
                })
                .setNegative(R.string.cancel)
                .setNegativeListener(v -> mDeleteChatDialog.dismiss())
                .build();
        mDeleteChatDialog.show();
    }

    @Override
    public void resetMultipleStatus() {

    }

    public void annotateMessageWithTime(ChatMessage chatMessage) {
        ChatMessage message = getViewModel().getMessageNeedTimeAnnotation().get();
        if (message == null || message.getMid() != chatMessage.getMid()) {
            getViewModel().setMessageNeedTimeAnnotation(chatMessage);
        } else {
            getViewModel().setMessageNeedTimeAnnotation(null);
        }
    }

    @Override
    public void onChatCommonWordsAdd() {
        AppUtil.startFragmentUriForResult(
                this,
                MePageRouter.WORK_COMMON_WORDS_ADD_OR_EDIT_ACTIVITY,
                RequestCodeConstants.REQUEST_CODE_CHAT_COMMON_WORDS_PUT_REQUEST_CODE,
                new Bundle(),
                ActivityAnimType.DEFAULT
        );
    }

    @Override
    public void onChatCommonWordsSet() {
        AppUtil.startFragmentUriForResult(
                this,
                MePageRouter.WORK_COMMON_WORDS_SETTING_ACTIVITY,
                RequestCodeConstants.REQUEST_CODE_CHAT_COMMON_WORDS_PUT_REQUEST_CODE,
                new Bundle(),
                ActivityAnimType.DEFAULT
        );
    }

    @Override
    public void onChatCommonWordsItemClick(ChatExpandableCommonWordsBean commonWordsBean) {
        new PointUtils.BuilderV4().name("chat-commonword-send").point();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != Activity.RESULT_OK) {
            return;
        }
        if (requestCode == RequestCodeConstants.REQUEST_CODE_CHAT_COMMON_WORDS_PUT_REQUEST_CODE) {
            getViewModel().getCommonWordsList();
        } else if (requestCode == 1016) {
            if (data != null) {
                long mid = data.getLongExtra("mid", 0L);
                if (mid > 0) {
                    getViewModel().requestChangeTranslateMessage(mid);
                }
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        KPSwitchPanelLinearLayout layout = getPanelRoot();
        // 部分手机在常用语面板打开时去添加新的常用语然后跳转到新的编辑activity后，常用语面板会关闭，所以重新回到聊天页onResume时根据面板的关闭状态来给常用语按钮刷新样式
        View commonWordsPanel = getViewChatCommonWordsSubSpanBinding().getRoot();
        setCommonWordStyle(layout != null && layout.getVisibility() == View.VISIBLE && commonWordsPanel.getVisibility() == View.VISIBLE);
    }

    public void showKeyboardBySlashCommand(int newCount) {
        if (newCount > 0) {
            TLog.info(TAG, "showKeyboardBySlashCommand," + getEditText().getText());
            if (SlashCommandHelper.canShowCommandPopByKeyword(getEditText().getEditableText())) {
                getEditText().requestFocus();
                QMUIKeyboardHelper.showKeyboard(getEditText(), true);
            }
        }
    }

    protected void resetEmotionVoiceIcon() {
        getChatInputBottomViewBinding().ivEmotion.setImageResource(R.drawable.chat_ic_icon_chat_emotion);
        getChatInputBottomViewBinding().ivMore.setImageResource(R.drawable.chat_ic_icon_chat_more);
        setCommonWordStyle(false);
    }

    /**
     * 设置常用语按钮的样式
     *
     * @param open true 打开了常用语面板 false 关闭了常用语面板
     */
    protected void setCommonWordStyle(boolean open) {
        getChatInputBottomViewBinding().tvCommonWords.setBackgroundResource(open ? (ThemeUtils.useNewTheme ? R.drawable.chat_ic_icon_common_words_selected : R.drawable.chat_ic_icon_common_words_blue) : R.drawable.chat_ic_icon_common_words);
        getChatInputBottomViewBinding().tvCommonWords.setTextColor(open ? ThemeUtils.getThemeTextColorInt() : BaseApplication.getApplication().getColor(R.color.color_9999A3));
    }

    /**
     * 弹出撤回确认弹窗
     */
    protected void showWithdrawDialog(ChatMessage chatMessage) {
        CommonDialog.Builder builder = new CommonDialog.Builder(activity);
        builder.setLayoutId(R.layout.dialog_quite_confirm)
                .add(R.id.tv_sure)
                .add(R.id.tv_cancel)
                .setDisplayTextById(R.id.tv_content, getResources().getString(R.string.chat_are_u_sure_withdraw))
                .setDisplayTextById(R.id.tv_sure, getResources().getString(R.string.chat_withdraw_positive))
                .setCancelable(true)
                .setCanceledOnTouchOutside(true)
                .setGravity(Gravity.BOTTOM)
                .setOnItemClickListener((dialog, view) -> {
                    if (view.getId() == R.id.tv_sure) {
                        getViewModel().revertMessage(chatMessage);
                    }
                    dialog.dismiss();
                });
        builder.create().show();
    }

    @Override
    public void onTextSelectionShow(TextSelection textSelection, ChatMessage chatMessage, PopChatLongClickBinding binding, boolean richClose) {
        new PointUtils.BuilderV4()
                .name("chat-msg-right-click")
                .params("chat_id", chatMessage.getChatId())
                .params("msg_id", String.valueOf(chatMessage.getMid()))
                .point();
        if (this.mTextSelection != null && this.mTextSelection != textSelection) {
            this.mTextSelection.destroy();
        }
        if (mLongClickPopupWindow != null) {
            mLongClickPopupWindow.dismiss();
        }
        this.mTextSelection = textSelection;
        if (richClose) {
            getViewModel().expandMessage(chatMessage);
        }
    }

    @Override
    public void onTextSelectionUpDate(TextSelection textSelection, ChatMessage chatMessage, PopChatLongClickBinding chatLongClickBinding, View anchor) {
        if (textSelection != null && textSelection.getTextView() != null) {
            setPopOptionData(this, chatMessage, true, chatLongClickBinding, textSelection);
            setPopEmojiData(chatMessage, chatLongClickBinding, textSelection, this);
            chatLongClickBinding.rvOption.setVisibility(View.VISIBLE);
            chatLongClickBinding.rvAllEmoji.setVisibility(View.GONE);
            chatLongClickBinding.setHideEmoji(chatMessage.getMediaType() == MessageConstants.MSG_MESSAGE_EXTENSION_CARD || isSysChatType() || chatMessage.isFailed() || chatMessage.isSending());
            HiPopupWindow.PopCalculateParam popCalculateParam = HiPopupWindow.calculatePopWindowPos(textSelection.getTextView(), chatLongClickBinding.getRoot(), textSelection.getOperateWindow().getWindow());
            adjustEmojiPosition(popCalculateParam.isNeedShowUp, chatLongClickBinding);
            updateTextSelectionPopupToCenter(textSelection, chatLongClickBinding, anchor);
        }
    }

    /**
     * 填充长按弹窗数据
     */
    protected List<ChatClickOptionBean> setPopOptionData(InputChatPageCallback callback, ChatMessage chatMessage, boolean showCheckAll, PopChatLongClickBinding popChatLongClickBinding, TextSelection textSelection) {
        ChatMsgOptionsAdapter optionCommonAdapter = new ChatMsgOptionsAdapter(callback);
        List<ChatClickOptionBean> chatClickOptions = getViewModel().getChatClickOptions(chatMessage, showCheckAll, textSelection, AttendanceRepository.getServerTime(true));
        if (chatClickOptions.size() < 6) { //消息处于异常或者中间状态 比如 发送失败 和 发送中，选项会比较少，这里做特殊处理
            popChatLongClickBinding.rvOption.setLayoutManager(new GridLayoutManager(getContext(), chatClickOptions.size(), LinearLayoutManager.VERTICAL, false));
        } else {
            popChatLongClickBinding.rvOption.setLayoutManager(new GridLayoutManager(getContext(), 6, LinearLayoutManager.VERTICAL, false));
        }
        optionCommonAdapter.submitList(chatClickOptions);
        popChatLongClickBinding.rvOption.setAdapter(optionCommonAdapter);
        return chatClickOptions;
    }

    /**
     * 展示消息长按弹窗
     */
    @SuppressLint("ClickableViewAccessibility")
    protected void showMsgOptionsPopup(View messageView, ChatMessage chatMessage, boolean showCheckAll, InputChatPageCallback inputChatPageCallback, OnEmojiReplyCallback onEmojiReplyCallback) {
        if (mLongClickPopupWindow != null && mLongClickPopupWindow.isShowing()) {
            mLongClickPopupWindow.dismiss();
        }

        PopChatLongClickBinding binding = PopChatLongClickBinding.inflate(getLayoutInflater());
        mLongClickPopupWindow = new HiPopupWindow.Builder(activity)
                .setHeight(QMUIDisplayHelper.getDimenInPixel(R.dimen.message_options_pop_default_height))
                .setAnimationStyle(R.style.pop_anim_style_alpha)
                .setContentView(binding.getRoot())
                .build();
        mPopChatLongClickBinding = binding;
        binding.getRoot().setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN && mLongClickPopupWindow != null && mLongClickPopupWindow.isShowing()) {
                mLongClickPopupWindow.dismiss();
            }
            return false;
        });

        mLongClickPopupWindow.setCallBack(() -> getViewModel().updateMessageInOpting(null));

        getViewModel().updateMessageInOpting(chatMessage);

        List<ChatClickOptionBean> options = setPopOptionData(inputChatPageCallback, chatMessage, showCheckAll, binding, null);

        //设置快捷表情回复的显示和隐藏
        if (shouldShowEmoji(chatMessage)) {
            setPopEmojiData(chatMessage, binding, null, onEmojiReplyCallback);
            binding.setHideEmoji(false);
        } else {
            binding.setHideEmoji(true);
        }

        HiPopupWindow.PopCalculateParam popCalculateParam = HiPopupWindow.calculatePopWindowPos(messageView, binding.getRoot(), mLongClickPopupWindow.getPopupWindow());
        binding.rvOption.setVisibility(View.VISIBLE);
        binding.rvAllEmoji.setVisibility(View.GONE);

        adjustEmojiPosition(popCalculateParam.isNeedShowUp, binding);
        updatePopupToCenter(messageView, options.size(), popCalculateParam);

        mLongClickPopupWindow.showAtLocation(messageView, Gravity.NO_GRAVITY, popCalculateParam.location[0], popCalculateParam.location[1]);

        new PointUtils.BuilderV4()
                .name("chat-msg-right-click")
                .params("chat_id", chatMessage.getChatId())
                .params("msg_id", String.valueOf(chatMessage.getMid()))
                .point();
    }

    @Override
    public void onMsgOptionItemClick(View view, ChatClickOptionBean chatClickOptionBean) {
        if (chatClickOptionBean.textSelection != null) {
            if (chatClickOptionBean.name == R.string.select_all) {
                chatClickOptionBean.textSelection.selectAllText();
                return;
            }
        } else {
            if (mLongClickPopupWindow != null) {
                mLongClickPopupWindow.dismiss();
            }
        }
        int newPoint = 0;
        ChatMessage chatMessage = chatClickOptionBean.chatMessage;
        if (chatClickOptionBean.name == R.string.chat_urgent) {
            handleMessageUrgent(chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_cancel_urgent) {
            showMsgUrgentDialog(chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_later_deal) {
            newPoint = 11;
            showDealLaterTimeSelectDialog(chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_reply_message) {
            newPoint = 3;
            handleMessageReply(chatClickOptionBean, chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_add_emotion) {
            newPoint = 13;
            handleMessageEmotionAdd(chatMessage);
        } else if (chatClickOptionBean.name == R.string.copy) {
            newPoint = 1;
            handleMessageCopy(chatClickOptionBean, chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_withdrawn) {
            newPoint = 5;
            showWithdrawDialog(chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_shield) {
            newPoint = 8;
            showShieldDialog(chatMessage);
        } else if (chatClickOptionBean.name == R.string.forward) {
            newPoint = 2;
            handleMessageForward(chatClickOptionBean, chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_add_favorite) {
            newPoint = 4;
            getViewModel().addFavorite(chatMessage.getMid());
        } else if (chatClickOptionBean.name == R.string.save_to_zhishu) {
            newPoint = 18;
            handleSaveToZhishu(chatMessage);
        } else if (chatClickOptionBean.name == R.string.open_in_zhishu) {
            newPoint = 18;
            handleOpenInZhishu(chatMessage);
        } else if (chatClickOptionBean.name == R.string.multiple_check) {
            newPoint = 9;
            handleMessageMultipleSelect(view, chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_create_new_schedule) {
            newPoint = 10;
            handleScheduleCreate(chatMessage);
        } else if (chatClickOptionBean.name == R.string.delete) {
            newPoint = 12;
            if (chatMessage.isFailed() || chatMessage.isSending()) {
                showMsgDeleteConfirmDialog(chatMessage);
            } else {
                showDeletedSingleMessageDialog(chatMessage.getMid());
            }
        } else if (chatClickOptionBean.name == R.string.chat_mark) {
            getViewModel().markMessage(chatMessage);
            newPoint = 6;
        } else if (chatClickOptionBean.name == R.string.chat_mark_cancel) {
            newPoint = 7;
            showMsgCancelMarkDialog(chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_msg_sticky_top) {
            getViewModel().requestMsgStickyTop(chatMessage);
            newPoint = 23;
        } else if (chatClickOptionBean.name == R.string.chat_msg_sticky_top_cancel) {
            handleMessageStickyTopCancel(chatMessage);
            newPoint = 24;
        } else if (chatClickOptionBean.name == R.string.chat_trans_text) {
            newPoint = 14;
            MessageForAudio messageForAudio = (MessageForAudio) chatMessage;
            handleAudioTransText(messageForAudio);
        } else if (chatClickOptionBean.name == R.string.chat_trans_text_close) {
            newPoint = 15;
            MessageForAudio messageForAudio = (MessageForAudio) chatMessage;
            handleAudioTransTextClose(messageForAudio);
        } else if (chatClickOptionBean.name == R.string.chat_message_translate) {
            newPoint = 25;
            handleMessageTranslate(chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_message_translate_close) {
            newPoint = 26;
            handleMessageTranslateClose(chatMessage);
        } else if (chatClickOptionBean.name == R.string.chat_message_translate_language) {
            Intent intent = new Intent(requireContext(), TranslateLanguageActivity.class);
            intent.putExtra("mid", chatMessage.getMid());
            startActivityForResult(intent, 1016);
        }
        if (newPoint > 0) {
            new PointUtils.BuilderV4()
                    .name("chat-message-function-click")
                    .params("type", newPoint)
                    .params("msg_id", chatMessage.getMid())
                    .point();
        }
        if (chatClickOptionBean.textSelection != null) {
            chatClickOptionBean.textSelection.destroy();
        }
    }

    private void handleMessageTranslate(ChatMessage chatMessage) {
        getViewModel().requestTranslateMessage(chatMessage.getMid());
    }

    private void handleMessageTranslateClose(ChatMessage chatMessage) {
        getViewModel().closeTranslateMessage(chatMessage.getMid());
    }

    private void updatePopupToCenter(View anchorView, int itemNum, HiPopupWindow.PopCalculateParam popCalculateParam) {
        if (itemNum < 6) {
            int perItemWidth = QMUIDisplayHelper.getScreenWidth(getContext()) / 6;
            mLongClickPopupWindow.getPopupWindow().setWidth((int) (perItemWidth * itemNum + KotlinExtKt.getDp(40)));
        } else {
            mLongClickPopupWindow.getPopupWindow().setWidth(QMUIDisplayHelper.getScreenWidth(getContext()));
        }
        int windowWidth = mLongClickPopupWindow.getPopupWindow().getWidth();
        final int anchorLoc[] = new int[2];
        // 获取锚点View在屏幕上的左上角坐标位置
        anchorView.getLocationOnScreen(anchorLoc);
        int centerViewX = (anchorLoc[0] + anchorView.getWidth() / 2) - windowWidth / 2;
        if (centerViewX > 0) {
            popCalculateParam.location[0] = centerViewX;
        }
    }

    protected void updateTextSelectionPopupToCenter(TextSelection textSelection, PopChatLongClickBinding chatLongClickBinding, View anchor) {
        PopupWindow popupWindow = textSelection.getOperateWindow().getWindow();
        int width = QMUIDisplayHelper.getScreenWidth(getContext());
        int itemNum = chatLongClickBinding.rvOption.getAdapter().getItemCount();
        if (itemNum < 6) {
            int perItemWidth = QMUIDisplayHelper.getScreenWidth(getContext()) / 6;
            width = (int) (perItemWidth * itemNum + KotlinExtKt.getDp(40));
        }
        final int[] originalPosition = new int[2];
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) ((ViewGroup) chatLongClickBinding.getRoot().getParent()).getLayoutParams();
        originalPosition[0] = layoutParams.x;
        originalPosition[1] = layoutParams.y;
        final int anchorLoc[] = new int[2];
        // 获取锚点View在屏幕上的左上角坐标位置
        anchor.getLocationOnScreen(anchorLoc);
        int centerViewX = (anchorLoc[0] + anchor.getWidth() / 2) - width / 2;
        if (centerViewX > 0) {
            originalPosition[0] = centerViewX;
        }
        int popWidth = textSelection.isOptWindowSizeSpecified() ? textSelection.getOperateWindow().getWindow().getWidth() : width;
        popupWindow.update(originalPosition[0], originalPosition[1], popWidth, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    /**
     * 调整右键面板emoji表情区域的位置，如果右键面板弹出位置在消息上方，emoji表情就需要在面板下方，反之在面板上方
     *
     * @param isNeedShowUp true 右键面板在消息上方
     * @param binding      右键面板对应的PopUpWindow
     */
    protected void adjustEmojiPosition(boolean isNeedShowUp, PopChatLongClickBinding binding) {
        FrameLayout.LayoutParams layoutParam = (FrameLayout.LayoutParams) binding.llOption.getLayoutParams();
        layoutParam.gravity = isNeedShowUp ? Gravity.BOTTOM : Gravity.TOP;

        if (binding.llOption.getChildCount() > 2) {
            if (isNeedShowUp) {
                if (binding.llOption.getChildAt(0).getId() == R.id.rv_default_reply_emojis) {
                    binding.llOption.removeViewInLayout(binding.rvDefaultReplyEmojis);
                    binding.llOption.removeViewInLayout(binding.flContainer);
                    binding.llOption.addView(binding.flContainer, 0);
                    binding.llOption.addView(binding.rvDefaultReplyEmojis);
                }
            } else {
                if (binding.llOption.getChildAt(0).getId() == R.id.fl_container) {
                    binding.llOption.removeViewInLayout(binding.rvDefaultReplyEmojis);
                    binding.llOption.removeViewInLayout(binding.flContainer);
                    binding.llOption.addView(binding.rvDefaultReplyEmojis, 0);
                    binding.llOption.addView(binding.flContainer);
                }
            }
        }
        binding.llOption.setLayoutParams(layoutParam);
    }

    /**
     * 消息长按是否展示表情回复
     */
    protected boolean shouldShowEmoji(ChatMessage chatMessage) {
        return !isSysChatType()
                && !chatMessage.isFailed() && !chatMessage.isSending()
                && !(chatMessage instanceof MessageForSystemCard)
                && !(chatMessage instanceof MessageForTaskCommentCard)
                && !(chatMessage instanceof MessageForImageCard)
                && !(chatMessage instanceof MessageForAppCard)
                && !(chatMessage instanceof MessageForRedEnvelope)
                && !(chatMessage instanceof MessageForLinkCall);
    }

    /**
     * 设置emoji
     */
    protected void setPopEmojiData(ChatMessage chatMessage, PopChatLongClickBinding popChatLongClickBinding, TextSelection textSelection, OnEmojiReplyCallback callback) {

        // 默认回复emoji列表
        List<EmotionItem> defaultEmojiList = EmotionHelper.getDefaultReplyEmojiList();
        List<SelectionEmojiItem> result = new ArrayList<>();
        if (LList.isNotEmpty(defaultEmojiList)) {
            for (EmotionItem emotionItem : defaultEmojiList) {
                try {
                    result.add(new SelectionEmojiItem(emotionItem.clone(), textSelection));
                } catch (Exception ignored) {
                }
            }
        }
        CommonAdapter<SelectionEmojiItem> emojiItemsAdapter = new CommonAdapter<>(R.layout.chat_item_layout_default_emoji, BR.bean);
        emojiItemsAdapter.addCallback(BR.callback, this);
        emojiItemsAdapter.addVariable(BR.chatMessage, chatMessage);
        emojiItemsAdapter.submitList(result);
        popChatLongClickBinding.rvDefaultReplyEmojis.setAdapter(emojiItemsAdapter);

        // 全部回复emoji列表
        RecyclerView replyAllEmojiRV = popChatLongClickBinding.rvAllEmoji;
        final List<EmotionItem> items = EmojiDataManager.getAllEmojiList();
        List<SelectionEmojiItem> selectionEmojiItems = new ArrayList<>();
        for (EmotionItem emotionItem : items) {
            selectionEmojiItems.add(new SelectionEmojiItem(emotionItem, textSelection));
        }
        CommonAdapter<SelectionEmojiItem> adapter = new CommonAdapter<>(R.layout.chat_item_layout_emoji, BR.bean);
        adapter.addCallback(BR.callback, callback);
        adapter.addVariable(BR.chatMessage, chatMessage);
        adapter.submitList(selectionEmojiItems);
        replyAllEmojiRV.setAdapter(adapter);
    }

    @Override
    public void onCancelGroupInvite(String groupId, String userIds, long msgId) {
        if (TextUtils.isEmpty(userIds) || TextUtils.isEmpty(groupId)) {
            TLog.info(TAG, "params is null ,failed to cancelGroupInvite");
            return;
        }
        DialogUtils.Builder builder = new DialogUtils.Builder(getContext());
        mCancelInviteDialog = builder.setCancelable(true)
                .setTitle(getString(R.string.chat_cancel_confirm))
                .setContent(getString(R.string.chat_cancel_invite_confirm))
                .setNegative(R.string.cancel)
                .setPositive(R.string.confirm)
                .setCancelListener(DialogInterface::dismiss)
                .setNegativeListener(v -> {
                    mCancelInviteDialog.dismiss();
                    mCancelInviteDialog = null;
                })
                .setPositiveListener(v -> {
                    mCancelInviteDialog.dismiss();
                    mCancelInviteDialog = null;
                    getViewModel().requestCancelGroupInvite(groupId, userIds, msgId);
                }).build();
        mCancelInviteDialog.show();
    }

    @Override
    public void onSingleImageClick(String imageUrl) {
        ChatImageHelper.jumpToMediaViewer(getActivity(), imageUrl);
    }

    @Override
    public void onAvatarClick(View view, ChatMessage chatMessage) {
        GroupRobotEntity robot = getViewModel().getGroupRobot(chatMessage);
        if (robot != null) {
            if (robot.getType() == Constants.ROBOT_TYPE_SHIP) {
                return;
            }
            ChatPageRouter.jumpToRobotProfilePage(view.getContext(), robot.getRobotId());
            new PointUtils.BuilderV4()
                    .name("chatbot-card-expose")
                    .params("source", 2)
                    .params("robot_id", robot.getRobotId())
                    .point();
            return;
        }
        Contact contact = getViewModel().getContact(chatMessage);
        if (contact == null) {
            return;
        }
        Bundle bundle = new Bundle();
        ConversationAvatarPointerHelperKt.conversionAvatarClickedPoint(contact, chatMessage);
        bundle.putString(PageConstantsKt.PAGE_FROM, PageConstantsKt.CONVERSATION_PAGE_AVATAR);
        if (contact.getUserType() == MessageConstants.MSG_CONTACT_TYPE_SYS) {
            return;
        }
        bundle.putString(BundleConstants.BUNDLE_DATA_LONG, contact.getUserId());
        AppUtil.getDefaultUriRequest(view.getContext(), OrganizationPageRouter.USER_INFO_ACTIVITY, bundle, ActivityAnimType.DEFAULT).setIntentFlags(Intent.FLAG_ACTIVITY_NEW_TASK).start();
    }

    /**
     * 点击机器人名称
     *
     * @param robotId
     */
    @Override
    public void onCheckGroupRobot(String robotId) {
        ChatPageRouter.jumpToRobotProfilePage(activity, robotId);
        new PointUtils.BuilderV4()
                .name("chatbot-card-expose")
                .params("source", 1)
                .params("robot_id", robotId)
                .point();
    }

    /**
     * 撤销邀请机器人
     */
    @Override
    public void onCancelRobotInvite(String groupId, String robotId, long msgId) {
        DialogUtils.Builder builder = new DialogUtils.Builder(getContext());
        mCancelInviteDialog = builder.setCancelable(true)
                .setTitle(getString(R.string.chat_cancel_confirm))
                .setContent(getString(R.string.chat_cancel_invite_confirm))
                .setNegative(R.string.cancel)
                .setPositive(R.string.confirm)
                .setCancelListener(dialog -> dialog.dismiss())
                .setNegativeListener(v -> {
                    mCancelInviteDialog.dismiss();
                    mCancelInviteDialog = null;
                })
                .setPositiveListener(v -> {
                    mCancelInviteDialog.dismiss();
                    mCancelInviteDialog = null;
                    getViewModel().cancelInviteGroupRobot(groupId, robotId, msgId);
                }).build();
        mCancelInviteDialog.show();
    }

    protected void popupEmojiSwitch(View view, SelectionEmojiItem selectionEmojiItem, EmotionItem emotionItem) {
        boolean showEmoji = emotionItem.getDrawableId() == R.drawable.emotion_ic_reply_emoji_collapse;
        //切换数据
        emotionItem.setDrawableId(showEmoji ? R.drawable.emotion_ic_reply_emoji_expand : R.drawable.emotion_ic_reply_emoji_collapse);
        //手动触发UI变更
        ImageView ivEmoji = view.findViewWithTag(getString(R.string.chat_tag_iv_emoji));
        ivEmoji.setImageResource(showEmoji ? R.drawable.emotion_ic_reply_emoji_expand : R.drawable.emotion_ic_reply_emoji_collapse);
        if (selectionEmojiItem.textSelection != null) {
            OperateWindow operateWindow = selectionEmojiItem.textSelection.getOperateWindow();
            if (operateWindow != null) {
                View allEmojiView = operateWindow.getContentView().findViewById(R.id.rv_all_emoji);
                if (allEmojiView != null) {
                    allEmojiView.setVisibility(showEmoji ? View.VISIBLE : View.GONE);
                }
                View option = operateWindow.getContentView().findViewById(R.id.rv_option);
                if (option != null) {
                    option.setVisibility(showEmoji ? View.GONE : View.VISIBLE);
                }
            }
        } else {
            if (mLongClickPopupWindow != null && mPopChatLongClickBinding != null) {
                mPopChatLongClickBinding.rvAllEmoji.setVisibility(showEmoji ? View.VISIBLE : View.GONE);
                mPopChatLongClickBinding.rvOption.setVisibility(showEmoji ? View.GONE : View.VISIBLE);
            }
        }
    }

    @Override
    public void onViewCalendar() {
        AppUtil.getDefaultUriRequest(getActivity(), AppPageRouter.MAIN_TAB_ACTIVITY, null, ActivityAnimType.ALPHA_FAST)
                .setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                .putExtra(BundleConstants.BUNDLE_URL, BundleConstants.SCHEME_TO_VIEW_CALENDAR)
                .start();
    }


    public void sendMessage(String title, String originalContent, List<String> atIds, int originalLength,String pbExtension) {
        boolean hasInputContent = !TextUtils.isEmpty(title) || !TextUtils.isEmpty(originalContent);
        if (attachmentHelper != null && attachmentHelper.hasAttachment()) {
            // 使用回调版本确保消息顺序
            attachmentHelper.sendAndGetPBExtension(hasInputContent, extension -> {
                TLog.debug("sendMessage", "pbExtension = " + extension);
                if (hasInputContent) {
                    sendMessageInner(title, originalContent, atIds, originalLength, extension);
                }
                updateSendTvUI();
                return Unit.INSTANCE;
            });
        } else {
            sendMessageInner(title, originalContent, atIds, originalLength, pbExtension);
        }
    }

    /**
     * 聊天消息发送
     */
    private void sendMessageInner(String title, String originalContent, List<String> atIds, int originalLength,String pbExtension) {
        if (TextUtils.isEmpty(title) && TextUtils.isEmpty(originalContent)) {
            TLog.error(TAG, "尝试发送空消息");
            return;
        }
        TLog.debug(TAG, "发送消息,title:" + title + ",content:" + originalContent+" , pbExtension = "+pbExtension);
        final String content = originalContent.replaceAll(RichConstants.RICH_LINE_SPLIT_CHAR + "", "");
        EmotionItem emotionItem = EmotionHelper.containSingleEmoji(content);
        if (TextUtils.isEmpty(title) && emotionItem != null && emotionItem.getId() > 0) {
            getViewModel().sendEmotionItemMessage(emotionItem, emotionItem.getPackageId(), getViewModel().getChatId(), getViewModel().getType(), getViewModel().getReplyBean());
            resetInputMessageView();
        } else {
            //转义后的最大长度，如果服务端没有配置，则默认为15000个字符。
            int maxLength = ProcessHelper.getUserPreferences().getInt(Constants.KEY_MARKDOWN_TEXT_MAX_LENGTH, 15000);
            int totalLength = title.length() + content.length();
            TLog.info(TAG, "消息发送,original: " + originalLength + ",max:" + maxLength + ",total:" + totalLength);
            if (originalLength > RichConstants.EMPTY_PASTE_AUTO_FILE_MAX_LENGTH || totalLength > maxLength) {
                getViewModel().sendTextFileMessage(title + "\n" + RichTextEscapes.toPlainText(content), getViewModel().getChatId(), getViewModel().getType(), getViewModel().getReplyBean());
                resetInputMessageView();
            } else {
                getViewModel().sendMarkdownMessage(title, content, getViewModel().getChatId(), atIds, getViewModel().getType(), getViewModel().getReplyBean(), null,pbExtension);
                resetInputMessageView();
            }
        }
    }

    /**
     * 重置消息草稿及相关输入UI
     */
    private void resetInputMessageView() {
        getViewModel().removeDraft();
        getEditTitleText().setText("");
        getEditText().setText("");
    }

    /**
     * 定时消息发送
     */
    protected void sendScheduledMessage(String title, String originalContent, List<String> atIds) {
        final String content = originalContent.replaceAll(RichConstants.RICH_LINE_SPLIT_CHAR + "", "");
        ScheduledMsgModel scheduledMsgModel = getViewModel().getEditingScheduledMsgModel().getValue();
        getViewModel().sendScheduledMsg(scheduledMsgModel,
                title, content,
                getViewModel().getChatId(),
                atIds,
                getViewModel().getType(),
                getViewModel().getReplyBean()
        );
    }

    private static class KeyboardListener implements QMUIKeyboardHelper.KeyboardVisibilityEventListener {
        private WeakReference<InputChatFragment> mFragmentWeakReference;

        public KeyboardListener(InputChatFragment fragment) {
            mFragmentWeakReference = new WeakReference<>(fragment);
        }

        @Override
        public boolean onVisibilityChanged(boolean isOpen, int heightDiff) {
            InputChatFragment<?, ?> inputChatFragment = mFragmentWeakReference.get();
            if (inputChatFragment == null) {
                return false;
            }

            // 更新键盘状态
            inputChatFragment.mIsKeyboardShowing = isOpen;

            if (isOpen) {
                // 键盘弹出时，如果有待显示的表情联想，延迟显示
                if (inputChatFragment.mPendingEmotionText != null && !inputChatFragment.mPendingEmotionText.isEmpty()) {
                    if (inputChatFragment.mEmotionSuggestRunnable != null) {
                        inputChatFragment.mEmotionHandler.removeCallbacks(inputChatFragment.mEmotionSuggestRunnable);
                    }
                    inputChatFragment.mEmotionSuggestRunnable = () -> inputChatFragment.showEmotionSuggestPopupForText(inputChatFragment.mPendingEmotionText);
                    inputChatFragment.mEmotionHandler.postDelayed(inputChatFragment.mEmotionSuggestRunnable, 300); // 键盘弹出后延迟300ms显示
                }
            } else {
                // 键盘关闭时，隐藏表情弹框并清除待显示的文本
                if (inputChatFragment.mEmotionSuggestPopup != null && inputChatFragment.mEmotionSuggestPopup.isShowing()) {
                    inputChatFragment.mEmotionSuggestPopup.dismiss();
                }
                inputChatFragment.mPendingEmotionText = null;
                if (inputChatFragment.mEmotionSuggestRunnable != null) {
                    inputChatFragment.mEmotionHandler.removeCallbacks(inputChatFragment.mEmotionSuggestRunnable);
                    inputChatFragment.mEmotionSuggestRunnable = null;
                }
            }
            return false;
        }
    }

    protected boolean detectAndReplaceRichImage(String markdownText, ValueCallBack<String> callBack) {
        Matcher matcher = URLUtils.MARKDOWN_IMAGE_SYNTAX_REG.matcher(markdownText);
        Map<String, String> urlMappings = new HashMap<>();
        while (matcher.find()) {
            String url = matcher.group(2);
            if (!TextUtils.isEmpty(url)) {
                if (url.startsWith("<") && url.endsWith(">")) {
                    url = url.substring(1, url.length() - 1);
                }
                urlMappings.put(url, matcher.group());
            }
        }
        if (urlMappings.isEmpty()) {
            ClipboardHelper.copyRichText(requireContext(), markdownText);
            return true;
        }
        getViewModel().findImageUrl(urlMappings.keySet(), replaceMapping -> {
            String result = markdownText;
            for (Map.Entry<String, String> entry : replaceMapping.entrySet()) {
                String originalUrl = entry.getKey();
                String newUrl = entry.getValue();
                String originalMarkdown = urlMappings.get(originalUrl);
                if (originalMarkdown == null) {
                    continue;
                }
                String newMarkdown = String.format("![image](%s)", newUrl);
                result = result.replace(originalMarkdown, newMarkdown);
            }
            callBack.onValueCallback(result);
        });
        return false;
    }

    /**
     * 稍后处理选择日期&时间
     */
    protected void showDealLaterTimeSelectDialog(ChatMessage chatMessage) {
        mDateTimePickerView = new DateTimePickerBuilder(activity, (changeDayCalendar, timePickerModel) -> {
            mDealLaterTargetDate.setLength(0);
            mDealLaterTargetDate.append(changeDayCalendar.toString());
            mDealLaterEndTime.setLength(0);
            mDealLaterEndTime.append(timePickerModel.getPickerViewText());
        }).setLayoutRes(R.layout.chat_pickerview_change_minute, v -> {
                    View tvJump = v.findViewById(R.id.tv_jump);
                    View tvCancel = v.findViewById(R.id.tv_cancel);
                    View tvSubmit = v.findViewById(R.id.tv_submit);
                    tvJump.setOnClickListener(jumpTv -> {
                        mDateTimePickerView.dismiss();
                        getViewModel().laterDealCreate(chatMessage.getMid(), "", "");
                    });
                    tvCancel.setOnClickListener(cancelTv -> mDateTimePickerView.dismiss());
                    tvSubmit.setOnClickListener(submitTv -> {
                        mDateTimePickerView.dismiss();
                        getViewModel().laterDealCreate(chatMessage.getMid(), mDealLaterTargetDate.toString(), mDealLaterEndTime.toString());
                    });
                }).isDialog(true)
                .setItemVisibleCount(5)
                .setLayoutGravity(Gravity.BOTTOM)
                .setDividerColor(Color.TRANSPARENT)
                .setTextColorCenter(Color.BLACK) //设置选中项文字颜色
                .setContentTextSize(18)
                .build();
        mDateTimePickerView.show();
    }


    /**
     * 消息加急操作弹窗
     */
    protected void showMsgUrgentDialog(ChatMessage chatMessage) {
        mDialog = new DialogUtils.Builder(activity)
                .setCancelable(true)
                .setCanceledOnTouchOutside(true)
                .setContent(R.string.chat_shining_cancel_tips)
                .setPositive(R.string.sure)
                .setPositiveListener(v -> {
                    getViewModel().shiningCancel(chatMessage.getUrgencyId());
                    mDialog.dismiss();
                })
                .setNegative(R.string.cancel)
                .setNegativeListener(v -> mDialog.dismiss())
                .build();
        if (!mDialog.isShowing()) {
            mDialog.show();
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void copyAudioTransText(View view, ChatMessage chatMessage) {
        if (mLongClickPopupWindow != null && mLongClickPopupWindow.isShowing()) {
            mLongClickPopupWindow.dismiss();
        }

        PopChatLongClickBinding viewBinding = PopChatLongClickBinding.inflate(getLayoutInflater());
        mLongClickPopupWindow = new HiPopupWindow.Builder(activity)
                .setAnimationStyle(R.style.pop_anim_style_alpha)
                .setContentView(viewBinding.getRoot())
                .build();
        viewBinding.getRoot().setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN && mLongClickPopupWindow != null && mLongClickPopupWindow.isShowing()) {
                mLongClickPopupWindow.dismiss();
            }
            return false;
        });

        view.setBackgroundColor(Color.parseColor("#A5C2FA"));
        mLongClickPopupWindow.getPopupWindow().setOnDismissListener(() -> view.setBackgroundColor(Color.TRANSPARENT));
        mLongClickPopupWindow.setCallBack(() -> getViewModel().updateMessageInOpting(null));
        getViewModel().updateMessageInOpting(chatMessage);

        ChatMsgOptionsAdapter optionCommonAdapter = new ChatMsgOptionsAdapter(this);
        List<ChatClickOptionBean> chatClickOptionBeans = new ArrayList<>();
        chatClickOptionBeans.add(new ChatClickOptionBean(R.string.copy, R.drawable.ic_icon_chat_bubbles_copy, chatMessage, null));
        optionCommonAdapter.submitList(chatClickOptionBeans);
        viewBinding.rvOption.setLayoutManager(new LinearLayoutManager(getContext()));
        viewBinding.rvOption.setAdapter(optionCommonAdapter);
        viewBinding.rvOption.setVisibility(View.VISIBLE);
        viewBinding.rvAllEmoji.setVisibility(View.GONE);
        viewBinding.setHideEmoji(true);

        HiPopupWindow.PopCalculateParam popCalculateParam = HiPopupWindow.calculatePopWindowPos(view, viewBinding.getRoot(), mLongClickPopupWindow.getPopupWindow());
        updatePopupToCenter(view, chatClickOptionBeans.size(), popCalculateParam);

        mLongClickPopupWindow.showAtLocation(view, Gravity.NO_GRAVITY, popCalculateParam.location[0], popCalculateParam.location[1]);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void showTranslatedMessageMenu(View view, ChatMessage chatMessage) {
        if (mLongClickPopupWindow != null && mLongClickPopupWindow.isShowing()) {
            mLongClickPopupWindow.dismiss();
        }

        PopChatLongClickBinding viewBinding = PopChatLongClickBinding.inflate(getLayoutInflater());
        mLongClickPopupWindow = new HiPopupWindow.Builder(activity)
                .setAnimationStyle(R.style.pop_anim_style_alpha)
                .setContentView(viewBinding.getRoot())
                .build();
        viewBinding.getRoot().setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN && mLongClickPopupWindow != null && mLongClickPopupWindow.isShowing()) {
                mLongClickPopupWindow.dismiss();
            }
            return false;
        });

        view.setBackgroundColor(Color.parseColor("#A5C2FA"));
        mLongClickPopupWindow.getPopupWindow().setOnDismissListener(() -> view.setBackgroundColor(Color.TRANSPARENT));
        mLongClickPopupWindow.setCallBack(() -> getViewModel().updateMessageInOpting(null));
        getViewModel().updateMessageInOpting(chatMessage);

        List<ChatClickOptionBean> chatClickOptions = new ArrayList<>();
        chatClickOptions.add(new ChatClickOptionBean(R.string.copy, R.drawable.ic_icon_chat_bubbles_copy, chatMessage, null, true));
        chatClickOptions.add(new ChatClickOptionBean(R.string.forward, R.drawable.ic_icon_chat_bubbles_share, chatMessage, null, true));
        chatClickOptions.add(new ChatClickOptionBean(R.string.chat_message_translate_close, R.drawable.icon_translate_cancel, chatMessage, null));
        chatClickOptions.add(new ChatClickOptionBean(R.string.chat_message_translate_language, R.drawable.icon_translate_language, chatMessage, null));
        ChatMsgOptionsAdapter optionCommonAdapter = new ChatMsgOptionsAdapter(this);
        optionCommonAdapter.submitList(chatClickOptions);
        viewBinding.rvOption.setLayoutManager(new GridLayoutManager(getContext(), chatClickOptions.size(), LinearLayoutManager.VERTICAL, false));
        viewBinding.rvOption.setAdapter(optionCommonAdapter);
        viewBinding.rvOption.setVisibility(View.VISIBLE);
        viewBinding.rvAllEmoji.setVisibility(View.GONE);
        viewBinding.setHideEmoji(true);

        HiPopupWindow.PopCalculateParam popCalculateParam = HiPopupWindow.calculatePopWindowPos(view, viewBinding.getRoot(), mLongClickPopupWindow.getPopupWindow());
        updatePopupToCenter(view, chatClickOptions.size(), popCalculateParam);

        mLongClickPopupWindow.showAtLocation(view, Gravity.NO_GRAVITY, popCalculateParam.location[0], popCalculateParam.location[1]);
    }

    protected void handleAudioTransText(MessageForAudio messageForAudio) {
        messageForAudio.setUnfoldText(true);
        getChatMessageViewModel().addAudioTextOpenId(messageForAudio.getMid());
        List<MessageAndQuote> chatMessages = mMessageAdapter.getCurrentList();
        for (int i = 0; i < chatMessages.size(); i++) {
            ChatMessage message = chatMessages.get(i).getMessage();
            if (message.getMid() == messageForAudio.getMid() && message instanceof MessageForAudio) {
                mMessageAdapter.notifyItemChanged(i);
                break;
            }
        }
        getViewModel().requestTransText(messageForAudio.getMid());
    }

    protected void handleAudioTransTextClose(MessageForAudio messageForAudio) {
        messageForAudio.setUnfoldText(false);
        getChatMessageViewModel().removeAudioTextOpenId(messageForAudio.getMid());
        List<MessageAndQuote> chatMessages = mMessageAdapter.getCurrentList();
        for (int i = 0; i < chatMessages.size(); i++) {
            ChatMessage message = chatMessages.get(i).getMessage();
            if (message.getMid() == messageForAudio.getMid() && message instanceof MessageForAudio) {
                mMessageAdapter.notifyItemChanged(i);
                break;
            }
        }
    }

    protected void handleMessageCopy(ChatClickOptionBean chatClickOptionBean, ChatMessage chatMessage) {
        ValueCallBack<String> callBack = result -> {
            ClipboardHelper.copyRichText(requireContext(), result);
            ToastUtils.success(getResources().getString(R.string.copy_success));
        };

        boolean copied = true;
        if (chatClickOptionBean.textSelection != null
                && (!chatClickOptionBean.textSelection.isAllSelected()
                || chatMessage.getMediaType() == MessageConstants.MSG_MESSAGE_EXTENSION_CARD)) {
            CharSequence selected = chatClickOptionBean.textSelection.getSelectionText();
            String markdownText = ClipboardHelper.toMarkdownText((Spanned) selected);
            if (chatMessage instanceof MessageForRichText) {
                copied = detectAndReplaceRichImage(markdownText, callBack);
            } else if (chatMessage instanceof MessageForExtensionCard) {
                ClipboardHelper.copyPlainText(requireContext(), markdownText);
            } else {
                ClipboardHelper.copyRichText(requireContext(), markdownText);
            }
        } else if (chatClickOptionBean.handleTranslated) {
            if (chatMessage instanceof MessageForMarkdownText) {
                MessageForMarkdownText mdMsg = (MessageForMarkdownText) chatMessage;
                boolean noTitle = TextUtils.isEmpty(mdMsg.getTranslateTitle());
                String msgContent = mdMsg.getTranslateContent();
                if (!mdMsg.getAtIds().isEmpty()) {
                    msgContent = MLinkEmotionTextUtils.replaceContactRemark(msgContent, mdMsg.getAtIds());
                }
                String mdText = noTitle ? msgContent : mdMsg.getTitle() + (TextUtils.isEmpty(msgContent) ? "" : ("\n" + msgContent));
                ClipboardHelper.copyRichText(activity, mdText);
            } else if (chatMessage instanceof MessageForText) {
                MessageForText textMsg = (MessageForText) chatMessage;
                String msgContent = textMsg.getTranslateContent();
                if (!textMsg.getAtIds().isEmpty()) {
                    msgContent = MLinkEmotionTextUtils.replaceContactRemark(msgContent, textMsg.getAtIds());
                }
                CommonUtils.copyText(activity, msgContent);
            } else if (chatMessage instanceof MessageForAudio) {
                CommonUtils.copyText(activity, ((MessageForAudio) chatMessage).getAudioInfo().getTranslateContent());
            }
        } else {
            if (chatMessage instanceof MessageForMarkdownText) {
                MessageForMarkdownText mdMsg = (MessageForMarkdownText) chatMessage;
                boolean noTitle = TextUtils.isEmpty(mdMsg.getTitle());
                String msgContent = mdMsg.getContent();
                if (!mdMsg.getAtIds().isEmpty()) {
                    msgContent = MLinkEmotionTextUtils.replaceContactRemark(msgContent, mdMsg.getAtIds());
                }
                String mdText = noTitle ? msgContent : mdMsg.getTitle() + (TextUtils.isEmpty(msgContent) ? "" : ("\n" + msgContent));
                ClipboardHelper.copyRichText(activity, mdText);
            } else if (chatMessage instanceof MessageForRichText) {
                MessageForRichText richMsg = (MessageForRichText) chatMessage;
                boolean noTitle = TextUtils.isEmpty(richMsg.getTitle());
                String msgContent = richMsg.getContent();
                if (!richMsg.getAtIds().isEmpty()) {
                    msgContent = MLinkEmotionTextUtils.replaceContactRemark(msgContent, richMsg.getAtIds());
                }
                String mdText = noTitle ? msgContent : richMsg.getTitle() + "\n" + msgContent;
                copied = detectAndReplaceRichImage(mdText, callBack);
            } else if (chatMessage instanceof MessageForText) {
                MessageForText textMsg = (MessageForText) chatMessage;
                String msgContent = textMsg.getContent();
                if (!textMsg.getAtIds().isEmpty()) {
                    msgContent = MLinkEmotionTextUtils.replaceContactRemark(msgContent, textMsg.getAtIds());
                }
                CommonUtils.copyText(activity, msgContent);
            } else if (chatMessage instanceof MessageForAudio) {
                CommonUtils.copyText(activity, ((MessageForAudio) chatMessage).getAudioInfo().getTransText());
            } else {
                CommonUtils.copyText(activity, chatMessage.getContent());
            }
        }
        if (copied) {
            ToastUtils.success(getResources().getString(R.string.copy_success));
        }
    }

    protected void showShieldDialog(ChatMessage chatMessage) {
    }

    protected void handleOpenInZhishu(ChatMessage chatMessage) {
        if (ServiceManager.getInstance().getSecurityService().isForbidden(BusinessModuleEnum.ZHI_SHU)) {
            ToastUtils.failure(R.string.no_permission_access);
            return;
        }
        Bundle bundle = new Bundle();
        WebViewBean webViewBean = new WebViewBean();
        webViewBean.setUrl(appendUrlWithExtraParams(getViewModel().getZhishuSaveUrl(chatMessage)));
        bundle.putSerializable(Constants.DATA_WEB_BEAN, webViewBean);
        AppUtil.startUri(getActivity(), WebViewPageRouter.WEB_VIEW_ACTIVITY, bundle);
    }

    protected void handleMessageMultipleSelect(View view, ChatMessage chatMessage) {
        resetEmotionVoiceIcon();
        KPSwitchConflictUtil.hidePanelAndKeyboard(getPanelRoot());
        clickCloseReply();
        multipleMessage(view, chatMessage);
    }

    /**
     * 多选消息
     */
    protected void multipleMessage(View view, ChatMessage chatMessage) {
        //标题栏
        ChatTitleBarChatBinding multipleTitleBar = getMultipleTitleBar();

        multipleTitleBar.ivBack.setVisibility(View.GONE);
        multipleTitleBar.tvBack.setText(R.string.cancel);
        multipleTitleBar.tvBack.setClickable(true);
        multipleTitleBar.tvBack.setOnClickListener(v -> {
            multipleTitleBar.getRoot().setVisibility(View.GONE);
            getTitleBarView().setVisibility(View.VISIBLE);
            updateMultipleSelectStatus(false);
            getViewModel().getSelectedMsgData().clear();
            mMessageAdapter.notifyDataSetChanged();
            getViewModel().sendMultiSelectPointClick("6");
        });

        multipleTitleBar.getRoot().setVisibility(View.VISIBLE);
        getTitleBarView().setVisibility(View.GONE);
        getViewModel().mSelectedMsgData.clear();
        updateMultipleSelectStatus(true);
        ExecutorFactory.execMainTaskDelay(() -> QMUIKeyboardHelper.hideKeyboard(getEditText()), 100);
        getViewModel().checkBoxChange(chatMessage);
        mMessageAdapter.notifyDataSetChanged();
    }

    public void updateMultipleSelectStatus(boolean isInMultipleSelect) {
        getViewModel().setInMultipleSelect(isInMultipleSelect);
    }

    private String appendUrlWithExtraParams(String url) {
        StringBuilder builder = new StringBuilder(url);
        Map<String, String> urlParams = URLUtils.parseUrlParams(url);
        if (urlParams.size() > 0) {
            builder.append("&barheight=");
        } else {
            builder.append("?barheight=");
        }
        builder.append(KotlinExtKt.getPxToDp(getStatusBarsHeight(activity)));
        builder.append("&noHead=1");
        return builder.toString();
    }

    /**
     * 消息取消mark弹窗
     */
    protected void showMsgCancelMarkDialog(ChatMessage chatMessage) {
        mDialog = new DialogUtils.Builder(activity)
                .setCancelable(true)
                .setCanceledOnTouchOutside(true)
                .setContent(R.string.chat_mark_cancel_tips)
                .setPositive(R.string.sure)
                .setPositiveListener(v -> {
                    cancelMarkMessage(chatMessage);
                    mDialog.dismiss();
                })
                .setNegative(R.string.cancel)
                .setNegativeListener(v -> mDialog.dismiss())
                .build();
        if (!mDialog.isShowing()) {
            mDialog.show();
        }
    }

    @Override
    public void onSwitchVoiceClick() {
        resetEmotionVoiceIcon();
        KPSwitchConflictUtil.hidePanelAndKeyboard(getPanelRoot());
        if (getChatInputBottomViewBinding().btnVoice.getVisibility() == View.VISIBLE) {
            // 隐藏语音发送相关UI
            getChatInputBottomViewBinding().ivVoice.setImageResource(R.drawable.chat_ic_icon_chat_voice);
            getChatInputBottomViewBinding().btnVoice.setVisibility(View.GONE);
            getChatInputBottomViewBinding().inputContainer.setVisibility(View.VISIBLE);
            updateSendTvUI();
        } else {
            // 展示语音发送相关UI
            getChatInputBottomViewBinding().ivVoice.setImageResource(R.drawable.chat_ic_icon_chat_voice_keyboard);
            getChatInputBottomViewBinding().btnVoice.setVisibility(View.VISIBLE);
            getChatInputBottomViewBinding().inputContainer.setVisibility(View.GONE);
            getChatInputBottomViewBinding().tvSend.setVisibility(View.GONE);
            new PointUtils.BuilderV4()
                    .name("chat-mobile-function-click")
                    .params("type", 1)
                    .point();
        }
    }

    @Override
    public void clickCloseReply() {
        getViewModel().updateReplyDraft(null);
        getViewModel().setReplyBean(null);
        getLayoutReplyTipsBinding().getRoot().setVisibility(View.GONE);
        getLayoutReplyTipsBinding().setVariable(BR.msg, null);
    }

    protected void cancelMarkMessage(ChatMessage chatMessage) {
        // group chat需要重写增加权限校验
        getViewModel().markCancelMessage(chatMessage);
    }

    protected void showScreenShotSelectDialog() {
        if (mScreenShotChatDialog == null) {
            mScreenShotChatDialog = new ScreenShotChatDialog((FragmentActivity) activity, (fileList, originalEnable) -> {
                if (LList.isEmpty(fileList)) {
                    ToastUtils.ssd("所选项目中没有可发送的内容。");
                    return;
                }
                getViewModel().handleMediaFilesSend(fileList, getViewModel().getChatId(), getViewModel().getType(), getViewModel().getReplyBean());
            });
        }
        if (getLayoutReplyTipsBinding().getRoot().getVisibility() == View.VISIBLE) {
            mScreenShotChatDialog.showScreenShot(getLayoutReplyTipsBinding().viewClose);
        } else {
            mScreenShotChatDialog.showScreenShot(getChatInputBottomViewBinding().ivMore);
        }
    }

    @Override
    public void onRewriteClick(ChatMessage chatMessage) {
        super.onRewriteClick(chatMessage);
        ExecutorFactory.execLocalTask(() -> {
            ChatMessage message = ServiceManager.getInstance().getMessageService().getChatMessage(-chatMessage.getMid());
            ExecutorFactory.execMainTask(() -> {
                smoothToBottom();
                rewriteForMessage(message);
                updateReplyMessageTips(message);
            });
        });
    }

    private void updateReplyMessageTips(ChatMessage chatMessage) {
        if (chatMessage == null || chatMessage.getMessageReply() == null || chatMessage.getMessageReply().getReplyId() <= 0) {
            return;
        }
        ExecutorFactory.execLocalTask(() -> {
            ChatMessage replyMessage = ServiceManager.getInstance().getMessageService().getChatMessage(chatMessage.getMessageReply().getReplyId());
            ExecutorFactory.execMainTask(() -> showReplyTips(replyMessage, chatMessage.getMessageReply().getReplySnapshot(), false));
        });
    }

    private void rewriteForMessage(ChatMessage message) {
        if (getContext() == null) return;
        if (message instanceof MessageForText) {
            Log.i("rewriteForMessage", "MessageForText");
            rewriteOldTextMessage(message);
        } else if (message instanceof MessageForMarkdownText) {
            Log.i("rewriteForMessage", "MessageForMarkdownText");

            MessageForMarkdownText markdownText = (MessageForMarkdownText) message;
            if (!TextUtils.isEmpty(markdownText.getTitle())) {
                getEditTitleText().setText(markdownText.getTitle());
            }
            MarkdownDraft draft = new MarkdownDraft();
            draft.setContent(markdownText.getContent());
            draft.setAtIds(markdownText.getAtIds());
            rewriteOldMarkdownMessage(draft);
        } else if (message instanceof MessageForRichText) {
            Log.i("rewriteForMessage", "MessageForRichText");

            MessageForRichText richMsg = (MessageForRichText) message;
            if (!TextUtils.isEmpty(richMsg.getTitle())) {
                getEditTitleText().setText(richMsg.getTitle());
            }
            detectAndReplaceRichImage(richMsg.getContent(), result -> {
                MarkdownDraft draft = new MarkdownDraft();
                draft.setContent(result);
                draft.setAtIds(richMsg.getAtIds());
                rewriteOldMarkdownMessage(draft);
            });
        }
    }

    protected void rewriteOldMarkdownMessage(MarkdownDraft draft){
        new DraftHelper(getContext(), draft).parseDraftTo(getEditText(), needFixListWhenApplyDraft());
    }

    protected void rewriteOldTextMessage(ChatMessage message) {
        Editable editableText = getEditText().getEditableText();
        int selectionEnd = getEditText().getSelectionEnd();
        editableText.insert(selectionEnd, message.getContent());
    }

    protected boolean needFixListWhenApplyDraft() {
        return false;
    }

    @Override
    public void onReplyContainerClick(View view, ChatMessage replyMessage, ChatMessage chatMessage) {
        super.onReplyContainerClick(view, replyMessage, chatMessage);
        if (replyMessage != null && replyMessage.isViewDisable()) {
            return;
        }
        ChatMessage.MessageReply messageReply = chatMessage.getMessageReply();
        long topId = messageReply == null ? 0 : messageReply.getTopId();
        jumpToReplyDetailPage(activity, chatMessage.getChatId(), chatMessage.getMid(), topId <= 0 ? chatMessage.getMid() : topId);
    }

    // ====================================== 消息表情回复 ======================================

    @Override
    public void onEmojiItemReplyClick(SelectionEmojiItem selectionEmojiItem, ChatMessage chatMessage) {
        EmotionItem emotionItem = selectionEmojiItem.emotionItem;
        if (emotionItem == null) {
            return;
        }
        if (selectionEmojiItem.textSelection != null) {
            selectionEmojiItem.textSelection.destroy();
        } else {
            if (mLongClickPopupWindow != null) {
                mLongClickPopupWindow.dismiss();
            }
        }
        if (chatMessage != null) {
            getViewModel().sendThumbsUpEmoji(chatMessage.getMid(), emotionItem.getServerMappingId());
        }
    }

    @Override
    public void onDefaultEmojiItemReplyClick(View view, SelectionEmojiItem selectionEmojiItem, ChatMessage chatMessage) {
        EmotionItem emotionItem = selectionEmojiItem.emotionItem;
        if (emotionItem == null) {
            return;
        }
        if (emotionItem.getServerMappingId() > 0) {
            if (selectionEmojiItem.textSelection != null) {
                selectionEmojiItem.textSelection.destroy();
            } else {
                if (mLongClickPopupWindow != null) {
                    mLongClickPopupWindow.dismiss();
                }
            }
            if (chatMessage != null) {
                getViewModel().sendThumbsUpEmoji(chatMessage.getMid(), emotionItem.getServerMappingId());
                new PointUtils.BuilderV4()
                        .name("chat-message-function-click")
                        .params("type", 19)
                        .point();
            }
        } else {
            popupEmojiSwitch(view, selectionEmojiItem, emotionItem);
            new PointUtils.BuilderV4()
                    .name("chat-message-function-click")
                    .params("type", 20)
                    .point();
        }
    }

    @Override
    public void onEmojiReplyUserInfoClick(String userId) {
    }

    // ====================================== 消息多选操作 ======================================

    @Override
    public void onMultipleForwardClick(int forwardType) {
    }

    @Override
    public void onMultipleAddFavorite() {
        ArrayMap<Long, ChatMessage> checkedMsgsMap = getViewModel().getSelectedMsgData();
        if (checkedMsgsMap.size() == 0) {
            return;
        }
        getViewModel().sendMultiSelectPointClick("3");
        if (checkedMsgsMap.size() == 1) {
            ChatMessage chatMessage = checkedMsgsMap.valueAt(0);
            if (chatMessage != null) {
                getViewModel().addFavorite(chatMessage.getMid());
                resetMultipleStatus();
            }
            return;
        }

        getViewModel().batchAddFavorite(checkedMsgsMap);
        resetMultipleStatus();
    }

    @Override
    public void onMultipleSaveLocal() {
        ArrayMap<Long, ChatMessage> checkedMsgsMap = getViewModel().getSelectedMsgData();
        if (checkedMsgsMap.size() == 0) {
            return;
        }

        FragmentActivity activity = getActivity();
        if (activity == null) {
            return;
        }

        PermissionAvoidManager permissionAvoidManager = new PermissionAvoidManager(activity);
        permissionAvoidManager.requestPermission(HiPermissionUtil.getAccessSharedFilePermissions(), (hasPermission, shouldShowAllRequestPermissionRationale) -> {
            if (hasPermission) {
                boolean downloadOperation = getViewModel().startDownloadCheckMsgs(checkedMsgsMap);
                if (downloadOperation) {
                    resetMultipleStatus();
                }
            } else {
                ToastUtils.sl(getString(R.string.chat_save_error_no_permission));
                resetMultipleStatus();
            }
        });
        getViewModel().sendMultiSelectPointClick("4");
    }

    @Override
    public void onMultipleDelete() {
        ArrayMap<Long, ChatMessage> checkedMsgsMap = getViewModel().getSelectedMsgData();
        if (checkedMsgsMap.size() == 0) {
            return;
        }
        Collection<ChatMessage> values = checkedMsgsMap.values();
        List<ChatMessage> checkedMsgs = new ArrayList<>(values);
        if (checkedMsgs.size() > 30) {
            ToastUtils.ss(R.string.chat_delete_messages_less_than_30);
            return;
        }
        List<Long> ids = new ArrayList<>();
        for (ChatMessage message : values) {
            ids.add(message.getMid());
        }
        showDeletedMultipleMessagesDialog(ids);
        getViewModel().sendMultiSelectPointClick("5");
    }

    // ====================================== 定时消息处理 ======================================

    @Override
    public void onScheduledMsgResetClick() {
        if (getViewModel().getCurrentScheduledMsgModel() != null) {
            ScheduledMsgModel scheduledMsgModel = getViewModel().getCurrentScheduledMsgModel().getValue();
            if (scheduledMsgModel != null) {
                getViewModel().requestDeleteScheduledMsg(scheduledMsgModel);
            }
            PointUtils.pointV4("chat-scheduledmsg-modify", new HashMap<>());
        }
    }

    // ====================================== 富文本二级编辑弹窗回调 ======================================

    @Override
    public boolean onRichEditSendClick(
            @NonNull String inputTitle,
            @NonNull String inputContent,
            @NonNull String contentMarkdownString,
            @NonNull List<String> atIds,
            int originalLength
    ) {
        TLog.debug("onRichEditSendClick","onRichEditSendClick: " + inputTitle + " " + inputContent + " " + contentMarkdownString);

        if (getViewModel().isScheduledMsgInSet()) {
            return sendScheduleMsgWithCheck(inputTitle, inputContent, contentMarkdownString, atIds);
        } else {
            sendMessage(inputTitle, contentMarkdownString, atIds, originalLength,null);
            return true;
        }
    }

    @Override
    public void onRichEditScheduledMsgFinishSet(@Nullable ScheduledMsgModel scheduledMsgModel) {
        if (scheduledMsgModel == null) {
            exitScheduledMsgSet();
        } else {
            hideNormalSendUI();
            getViewModel().updateScheduledMsgSetTime(scheduledMsgModel.getSendTimeMillis());
        }
    }

    @Override
    public boolean onRichEditScheduledMsgStartSet() {
        if (getViewModel().checkForScheduledMsgExisting()) {
            ToastUtils.failure(R.string.tips_scheduled_msg_limit);
            return false;
        }
        return true;
    }

    private void resetInputContentForText(String title, String text, List<String> ats) {
        ExecutorFactory.execMainTask(() -> {
            if (!TextUtils.isEmpty(title)) {
                String newTitle = getEditTitleText().getText().append(title).toString();
                getEditTitleText().setText(newTitle);
            }
            MarkdownDraft draft = new MarkdownDraft();
            draft.setContent(text);
            draft.setAtIds(ats);
            if (getContext() != null) {
                new DraftHelper(getContext(), draft).parseDraftAppend(getEditText());
            }
        });
    }

    private void resetInputContentForSticker(long sid) {
        ExecutorFactory.execMainTask(() -> {
            EmotionItem emojiItem = new EmojiHelper().getEmojiItemBySid(sid);
            String join = StringUtils.isNotEmpty(getEditText().getText().toString()) ? "\n" : "";
            getEditText().setText(getEditText().getText().append(join).append(emojiItem.getName()));
            getEditText().setSelection(getEditText().getText().length());
        });
    }

    @Override
    public void onScheduledMsgCancelClick() {
        exitScheduledMsgSet();
    }

    /**
     * 退出定时消息设置，ui相关重置
     */
    public void exitScheduledMsgSet() {
        getViewModel().clearScheduledMsgModel();
        resetVoiceIcon();
        updateSendTvUI();
    }

    /**
     * 定时消息选择日期&时间
     */
    @Override
    public void onScheduledMsgTimeClick() {
        mDateTimePickerView = new DateTimePickerBuilder(activity, (datePickerModel, timePickerModel) -> {
            mScheduledMsgDate.setLength(0);
            mScheduledMsgDate.append(datePickerModel);
            mScheduledMsgTime.setLength(0);
            mScheduledMsgTime.append(timePickerModel.getPickerViewText());
        }).setLayoutRes(R.layout.dialog_pickerview_scheduled_time, v -> {
                    View closePickTv = v.findViewById(R.id.tv_pick_close);
                    View cancelPickTv = v.findViewById(R.id.tv_pick_cancel);
                    View okPickTv = v.findViewById(R.id.tv_pick_ok);
                    okPickTv.setBackgroundResource(ThemeUtils.useNewTheme ? R.drawable.bg_corner_7_color_primary : R.drawable.bg_corner_7_color_5d68e8);
                    closePickTv.setOnClickListener(v1 -> mDateTimePickerView.dismiss());
                    cancelPickTv.setOnClickListener(v12 -> mDateTimePickerView.dismiss());
                    okPickTv.setOnClickListener(v13 -> {
                        long selectDateTimeMillis = LDate.transformStringToTimestamp(mScheduledMsgDate.toString(), mScheduledMsgTime.toString());
                        if (selectDateTimeMillis < System.currentTimeMillis()) {
                            ToastUtils.failure(R.string.tips_scheduled_msg_time_expired);
                            return;
                        }
                        mDateTimePickerView.dismiss();
                        getViewModel().updateScheduledMsgSetTime(selectDateTimeMillis);
                    });
                })
                .isDialog(true)
                .setItemVisibleCount(5)
                .setLayoutGravity(Gravity.BOTTOM)
                .setDividerColor(Color.TRANSPARENT)
                .setTextColorCenter(Color.BLACK) //设置选中项文字颜色
                .setContentTextSize(18)
                .setMinuteInterval(5) // 分钟间隔为5
                .build();
        mDateTimePickerView.show();
    }

    @Override
    protected boolean isInChatInputPage() {
        return true;
    }

    protected boolean isSysChatType() {
        return false;
    }

    @Override
    public RecyclerView getRecyclerView() {
        return getMessageListRecyclerView();
    }

    /**
     * 输入框-消息标题
     */
    protected EditText getEditTitleText() {
        return getChatInputBottomViewBinding().etTitle;
    }

    /**
     * 输入框-消息内容
     */
    protected RichEditView getEditText() {
        return getChatInputBottomViewBinding().etInput;
    }

    protected String getMessageInputHint() {
        return mInputHint;
    }

    protected void updateMessageInputHint(String hint) {
        mInputHint = "发送给 " + hint;
        getEditText().setLimitHint(mInputHint);
    }

    protected View getCommandPopupAtView() {
        return getChatInputBottomViewBinding().mInputParent;
    }

    // ====================================== 单聊&群聊抽象方法 ======================================

    protected abstract ChatMessageViewModel getChatMessageViewModel();

    protected abstract ConversationSelectBean getConversationSelectSourceBean();

    // ====================================== 获取页面UI组件 ======================================

    protected abstract View getTitleBarView(); // 页面标题

    protected abstract ChatTitleBarChatBinding getMultipleTitleBar(); // 页面标题

    protected abstract RecyclerView getMessageListRecyclerView(); // 消息列表

    protected abstract ChatInputBottomViewBinding getChatInputBottomViewBinding(); // 底部输入框区域

    protected abstract KPSwitchPanelLinearLayout getPanelRoot(); // 功能面板容器

    protected abstract EmotionPanelView getSubPanelEmotion(); // 表情回复功能面板

    protected abstract ChatFuncView getSubPanelFunc(); // 更多选项功能面板

    protected abstract ChatViewChatCommonWordsBinding getViewChatCommonWordsSubSpanBinding(); // 常用语操作面板

    protected abstract ChatViewReplyTipsBinding getLayoutReplyTipsBinding(); // 回复消息展示区域

    protected abstract ChatFuncView.FuncItemExtraClickListener getFuncItemExtraClickListener();

    // ====================================== 执行页面操作 ======================================

    protected abstract void smoothToBottom();

    public abstract void startSelectLocalFile();

    protected abstract void jumpToReplyDetailPage(Context context, String chatId, long mid, long topId);

    // ====================================== 消息长按选项相关 ======================================

    protected abstract void handleMessageUrgent(ChatMessage chatMessage); // 消息加急

    protected abstract void handleMessageEmotionAdd(ChatMessage chatMessage); // 消息表情添加

    protected abstract void handleScheduleCreate(ChatMessage chatMessage); // 消息日程创建

    protected abstract void handleMessageStickyTopCancel(ChatMessage chatMessage); // 消息取消置顶

    protected abstract String getPbExtensionFromAttachment();//从附件中组装 Pb 字符串
}
