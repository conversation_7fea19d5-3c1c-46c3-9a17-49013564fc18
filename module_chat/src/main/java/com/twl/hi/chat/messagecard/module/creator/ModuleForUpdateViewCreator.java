package com.twl.hi.chat.messagecard.module.creator;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.twl.hi.chat.messagecard.config.CardViewConfig;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.message.extensioncard.data.modules.Module;

import lib.twl.common.util.QMUIDisplayHelper;

/**
 * <AUTHOR>
 * @date 2023/3/17
 * description: 升级module View构造器
 */
public class ModuleForUpdateViewCreator extends ModuleViewCreator<Module> {

    public ModuleForUpdateViewCreator(Module module, CardViewConfig config) {
        super(module, config);
    }

    @Override
    public View createView() {
        Context context = config.getContext();
        TextView textView = new TextView(context);
        float fontScale = ServiceManager.getInstance().getSettingService().getFontScale();
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, QMUIDisplayHelper.dp2px(context, 41));
        layoutParams.topMargin = QMUIDisplayHelper.dp2px(context, 16);
        textView.setLayoutParams(layoutParams);
        textView.setBackgroundColor(Color.parseColor("#F9F9FA"));
        textView.setText("请升级新版本查看该模块");
        textView.setGravity(Gravity.CENTER);
        textView.setTextSize(12 * fontScale);
        textView.setTextColor(Color.parseColor("#9999A3"));
        return textView;
    }
}
