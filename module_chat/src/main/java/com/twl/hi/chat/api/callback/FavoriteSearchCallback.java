package com.twl.hi.chat.api.callback;

import com.twl.hi.chat.api.response.FavoriteSearchListResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.utils.MessageUtils;
import com.twl.http.ApiData;
import com.twl.http.error.AbsRequestException;
import com.twl.http.error.ErrorReason;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import okhttp3.Response;

/**
 * <AUTHOR>
 * @date 2021/12/24.
 */
public class FavoriteSearchCallback<R extends FavoriteSearchListResponse> extends BaseApiRequestCallback<R> {
    @Override
    public ApiData<R> parseResponse(Response resp) throws IOException, AbsRequestException {
        JSONObject jsonObject = buildResult(resp);
        try {
            R response = createClass().newInstance();
            response.list = MessageUtils.json2FavoriteSearch(jsonObject.getJSONArray("list"));
            response.hasMore = jsonObject.optInt("hasMore");
            response.offsetId = jsonObject.optString("offsetId");
            return buildApiData(response);
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return buildApiData(createObj(jsonObject.toString()));
    }

    @Override
    public void onSuccess(ApiData<R> data) {

    }

    @Override
    public void onComplete() {

    }

    @Override
    public void onFailed(ErrorReason reason) {

    }
}
