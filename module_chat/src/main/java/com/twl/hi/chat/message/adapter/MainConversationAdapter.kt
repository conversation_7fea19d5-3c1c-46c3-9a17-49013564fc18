package com.twl.hi.chat.message.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.DiffUtil
import com.twl.hi.basic.adapter.DataBoundListAdapter
import com.twl.hi.chat.R
import com.twl.hi.chat.databinding.ChatItemCollapseTopBinding
import com.twl.hi.chat.databinding.ChatItemConversationBinding
import com.twl.hi.chat.databinding.ChatItemEmptyBinding
import com.twl.hi.chat.message.callback.ConversationListCallback
import com.twl.hi.chat.message.viewmodel.ConversationListViewModel
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.ConversationWithMessage
import lib.twl.common.ext.dp

class MainConversationAdapter(
    private val lifecycleOwner: LifecycleOwner,
    private val clickCallback: ConversationListCallback,
    private val chatViewModel: ConversationListViewModel,
    private val collapseListener: () -> Unit
) : DataBoundListAdapter<ChatItemData?, ViewDataBinding>(
    object : DiffUtil.ItemCallback<ChatItemData?>() {
        override fun areItemsTheSame(oldItem: ChatItemData, newItem: ChatItemData): Boolean {
            return when {
                oldItem is ChatItemData.ConversationItem && newItem is ChatItemData.ConversationItem -> {
                    val old = oldItem.cwm.conversation
                    val new = newItem.cwm.conversation
                    old.chatId == new.chatId && old.type == new.type && old.isTop == new.isTop
                }

                oldItem is ChatItemData.CollapseItem && newItem is ChatItemData.CollapseItem -> true
                else -> false
            }
        }

        override fun areContentsTheSame(oldItem: ChatItemData, newItem: ChatItemData): Boolean {
            return when {
                oldItem is ChatItemData.ConversationItem && newItem is ChatItemData.ConversationItem -> {
                    val old = oldItem.cwm
                    val new = newItem.cwm
                    old.conversation == new.conversation && old.markdownDraft == new.markdownDraft && old.hasExtraFile == new.hasExtraFile
                }

                else -> false
            }
        }
    }
) {

    private val alwaysVisible = MutableLiveData(true)

    override fun submitList(list: List<ChatItemData?>?) {
        submitList(list, null)
    }

    override fun submitList(list: List<ChatItemData?>?, omCommit: Runnable?) {
        if (list.isNullOrEmpty()) {
            super.submitList(list, omCommit)
        } else {
            super.submitList(listOf(null) + list, omCommit)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            null -> 0
            is ChatItemData.CollapseItem -> 1
            is ChatItemData.ConversationItem -> 2
        }
    }

    override fun createBinding(parent: ViewGroup, viewType: Int): ViewDataBinding {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            0 -> ChatItemEmptyBinding.inflate(inflater, parent, false)
            1 -> ChatItemCollapseTopBinding.inflate(inflater, parent, false)
            else -> ChatItemConversationBinding.inflate(inflater, parent, false)
        }
    }

    override fun bind(vdb: ViewDataBinding, item: ChatItemData?, position: Int) {
        when (item) {
            is ChatItemData.CollapseItem -> with(vdb as ChatItemCollapseTopBinding) {
                val fontScale = ServiceManager.getInstance().settingService.fontScale
                val avatarSize = (48.dp * fontScale).coerceAtMost(64.dp).toInt()
                if (position == 1) { // 由于列表第一项固定插入一个空项，CollapseItem 处于视觉顶部时 position 为 1
                    root.setBackgroundColor(0xFFF0F1F2.toInt())
                } else {
                    root.setBackgroundResource(R.drawable.chat_bg_collapse_top)
                }
                collapseIcon.layoutParams.width = avatarSize
                visible = alwaysVisible
                collapsed = item.topCollapsed
                text = item.collapseText
                root.setOnClickListener { collapseListener() }
            }

            is ChatItemData.ConversationItem -> with(vdb as ChatItemConversationBinding) {
                adaptToScaledSize()
                cwm = item.cwm
                conversationContent = chatViewModel.getConversationSummary(item.cwm)
                root.setOnClickListener { v -> clickCallback.onItemClick(v, item.cwm.conversation) }
                root.setOnLongClickListener { v -> clickCallback.onItemLongClick(v, item.cwm.conversation, position) }
                lifecycleOwner = <EMAIL>
            }

            else -> {}
        }
    }
}

fun ChatItemConversationBinding.adaptToScaledSize() {
    val fontScale = ServiceManager.getInstance().settingService.fontScale
    val avatarSize = (48.dp * fontScale).coerceAtMost(64.dp).toInt()
    val badgeMinSize = (18.dp * fontScale).coerceAtMost(24.dp).toInt()
    val highlightSize = (10.dp * fontScale).coerceAtMost(14.dp).toInt()
    val appendIconSize = (14.dp * fontScale).coerceAtMost(22.dp).toInt()
    vgAvatar.layoutParams.apply {
        width = avatarSize
        height = avatarSize
    }
    tvCount.minWidth = badgeMinSize
    tvCount.minHeight = badgeMinSize
    tvNoCount.layoutParams.apply {
        width = highlightSize
        height = highlightSize
    }
    ivRemind.layoutParams.apply {
        width = appendIconSize
        height = appendIconSize
    }
    ivMark.layoutParams.apply {
        width = appendIconSize
        height = appendIconSize
    }
}

sealed interface ChatItemData {
    class ConversationItem(
        val cwm: ConversationWithMessage
    ) : ChatItemData {
        override fun toString(): String {
            return "ConversationItem(${cwm.conversation.chatId})"
        }
    }

    data class CollapseItem(
        val topCollapsed: LiveData<Boolean>,
        val collapseText: LiveData<CharSequence>
    ) : ChatItemData
}
