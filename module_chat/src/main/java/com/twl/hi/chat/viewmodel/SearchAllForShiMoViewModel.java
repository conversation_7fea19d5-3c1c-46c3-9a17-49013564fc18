package com.twl.hi.chat.viewmodel;

import android.app.Application;

import com.twl.hi.foundation.utils.point.GlobalSearchPointRecord;
import com.twl.hi.foundation.utils.point.GlobalSearchPointUtilKt;
import com.twl.hi.foundation.utils.point.SearchPointState;
import com.twl.hi.chat.api.request.ShiMoSearchRequest;
import com.twl.hi.chat.api.response.ShiMoSearchResponse;
import com.twl.hi.chat.api.response.bean.ShiMoSearchBean;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import lib.twl.common.util.LList;

/**
 * <AUTHOR>
 * @date 2022/1/4.
 */
public class SearchAllForShiMoViewModel extends SearchPageBaseViewModel<ShiMoSearchBean> {

    public SearchAllForShiMoViewModel(Application application) {
        super(application);
    }

    @Override
    public void searchData(final boolean cleanCache) {
        SearchPointState.Builder builder = GlobalSearchPointUtilKt.initialSearchState(states, GlobalSearchPointUtilKt.GLOBAL_SEARCH,
                        getSearchContent(), GlobalSearchPointRecord.INSTANCE.getGlobalSearchId())
                .setTab(getTab());
        String uniqueId = builder
                .getUniqueId();

        ShiMoSearchRequest request = new ShiMoSearchRequest(new BaseApiRequestCallback<ShiMoSearchResponse>() {
            @Override
            public void handleInChildThread(ApiData<ShiMoSearchResponse> data) {
                super.handleInChildThread(data);
                ShiMoSearchResponse response = data.resp;
                if (response == null) {
                    return;
                }
                setHasMore(response.hasMore);
                setOffsetId(response.offset);
                handleSearchData(cleanCache, response.list);
                GlobalSearchPointUtilKt.uploadSearchResult(states, uniqueId, true, !LList.isEmpty(response.list));
            }

            @Override
            public void onSuccess(ApiData<ShiMoSearchResponse> data) {

            }

            @Override
            public void onComplete() {
                loadFinishLiveData.postValue(true);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                GlobalSearchPointUtilKt.uploadSearchResult(states, uniqueId, false, false);
            }
        });
        request.content = getSearchContent();
        request.offset = getOffsetId();
        HttpExecutor.execute(request);
    }

    @Override
    protected String getTab() {
        return "石墨文档";
    }
}
