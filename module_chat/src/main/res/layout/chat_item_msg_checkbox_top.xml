<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.ChatBaseViewModel" />

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.ChatMessage" />

    </data>

    <CheckBox
        android:id="@+id/checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/selector_contact"
        android:button="@null"
        android:checked="@{viewModel.isCheck(msg)}"
        android:enabled="@{viewModel.checkBoxEnable(msg)}"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:visibleGone="@{viewModel.shouldShowCheckBox(msg)}" />

</layout>