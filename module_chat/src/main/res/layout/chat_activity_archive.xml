<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".chat.message.ArchiveActivity">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.basic.callback.TitleBarCallback" />

        <variable
            name="showEmpty"
            type="boolean" />
    </data>


    <LinearLayout
        android:id="@+id/activityMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical">

        <include
            layout="@layout/title_bar"
            app:callback="@{callback}"
            app:title="@{@string/chat_collapsed_conversations}" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            app:visibleGone="@{!showEmpty}" />

        <LinearLayout
            android:id="@+id/ll_empty"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:visibleGone="@{showEmpty}">

            <ImageView
                android:id="@+id/iv_empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="150dp"
                android:src="@drawable/ic_icon_message_empty" />

            <TextView
                android:id="@+id/tv_empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="@string/no_messages"
                android:textColor="@color/color_B1B1B8"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</layout>