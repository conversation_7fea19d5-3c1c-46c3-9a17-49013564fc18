<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.twl.hi.basic.model.QuickTypeBean" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.search.viewmodel.ChatSearchAllFragmentViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.search.callback.ChatSearchAllFragmentCallback" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_user_search"
            tools:visibility="invisible" />

        <include
            android:id="@+id/search_empty"
            layout="@layout/chat_layout_item_no_search_result_hint"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="150dp"
            android:visibility="gone" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/ll_quick"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/container_search_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingStart="15dp"
                        android:paddingEnd="15dp"
                        android:paddingTop="20dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tv_search_history"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/chat_search_history"
                            android:textColor="@color/color_0D0D1A"
                            android:textSize="14sp"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_search_history"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="10dp"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tv_search_history"
                            tools:itemCount="2"
                            tools:listitem="@layout/chat_item_search_history" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/ll_history"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:layout_marginTop="15dp">

                        <TextView
                            android:id="@+id/tv_single_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="15dp"
                            android:text="@string/contacts"
                            android:textColor="@color/color_B1B1B8"
                            android:textSize="13sp"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_single_record"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_single_title"
                            tools:itemCount="2"
                            tools:listitem="@layout/chat_item_search_record_single" />

                        <TextView
                            android:id="@+id/tv_group_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:layout_marginLeft="15dp"
                            android:text="@string/group"
                            android:textColor="@color/color_B1B1B8"
                            android:textSize="13sp"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/rv_single_record" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_group_record"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginLeft="15dp"
                            android:layout_marginRight="15dp"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_group_title"
                            tools:itemCount="2"
                            tools:listitem="@layout/chat_item_search_record_group" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>

                <ImageView
                    android:id="@+id/delete_all"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|end"
                    android:onClick="@{v->callback.onDelSearchHistory()}"
                    android:padding="20dp"
                    android:src="@drawable/chat_ic_icon_search_record_delelte" />

            </FrameLayout>
        </androidx.core.widget.NestedScrollView>
    </FrameLayout>
</layout>