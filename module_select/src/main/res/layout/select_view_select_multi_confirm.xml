<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.twl.hi.basic.util.ThemeUtils" />

        <variable
            name="contactCount"
            type="androidx.databinding.ObservableInt" />

        <variable
            name="departmentCount"
            type="androidx.databinding.ObservableInt" />

        <variable
            name="confirmClick"
            type="android.view.View.OnClickListener" />

        <variable
            name="countClick"
            type="android.view.View.OnClickListener" />

        <variable
            name="canChooseNone"
            type="Boolean" />
    </data>


    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/fl_confirm"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:background="@color/app_white">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/list_divider_height"
            android:background="@color/color_E9E9EE" />

        <TextView
            android:id="@+id/tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="20dp"
            android:onClick="@{countClick}"
            android:text="@{@string/select_has_select_multi_members(contactCount,departmentCount)}"
            android:textColor="@color/color_575CD7"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/btn_confirm"
            android:layout_width="62dp"
            android:layout_height="30dp"
            android:layout_gravity="center_vertical|right"
            android:layout_marginEnd="20dp"
            android:background="@{ThemeUtils.useNewTheme ? @drawable/sel_confirm_button_new : @drawable/sel_confirm_button}"
            android:enabled="@{canChooseNone ? true : ((contactCount>0 || departmentCount > 0) ? true : false)}"
            android:gravity="center"
            android:onClick="@{confirmClick}"
            android:text="@string/sure"
            android:textColor="@color/app_white"
            android:textSize="15sp"
            tools:background="@drawable/sel_confirm_button_new" />
    </FrameLayout>
</layout>
