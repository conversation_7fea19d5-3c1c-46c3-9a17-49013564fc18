<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <variable
            name="callback"
            type="com.twl.hi.select.multi.callback.SelectedMultiItemCallback" />

        <variable
            name="bean"
            type="com.twl.hi.select.multi.model.SelectedMultiBean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_white"
        android:clickable="true"
        android:focusable="true"
        android:orientation="horizontal"
        android:paddingLeft="20dp">

        <include
            layout="@layout/item_common_user"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:avatarClick="@{false}"
            app:showDepart="@{false}"
            app:userId="@{bean.id}" />

        <ImageView
            android:id="@+id/iv_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|center_vertical"
            android:onClick="@{()->callback.deleteSelectedMulti(bean)}"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            android:paddingRight="20dp"
            android:paddingBottom="10dp"
            android:src="@drawable/ic_icon_del" />

    </LinearLayout>

</layout>
