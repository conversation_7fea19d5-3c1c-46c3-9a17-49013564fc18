package com.twl.hi.select.bean;


import com.twl.hi.basic.model.select.SelectInterface;

import java.util.ArrayList;

public class SelectedInterfaceSimpleMutableModel {
    public ArrayList<SelectInterface> mSelects = new ArrayList<>(0);
    public SelectInterfaceProxy mSelectProxy = new SelectInterfaceProxy() {
        @Override
        public void setSelect(SelectInterface selectInterface, boolean b) {
            boolean isExist = mSelects.contains(selectInterface);
            if (b) {
                if (!isExist) {
                    mSelects.add(selectInterface);
                }
            } else if (isExist) {
                mSelects.remove(selectInterface);
            }
        }

        @Override
        public boolean isSelect(SelectInterface selectInterface) {
            return mSelects.contains(selectInterface);
        }
    };

}