package com.twl.hi.select.contact.viewmodel;

import android.app.Application;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.twl.hi.basic.api.request.GroupCreateRequest;
import com.twl.hi.basic.api.response.GroupCreateResponse;
import com.twl.hi.basic.api.response.bean.ApiResponse;
import com.twl.hi.export.select.bean.SelectContactsParams;
import com.twl.hi.basic.model.select.SelectProxy;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.foundation.logic.GroupService;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.Group;
import com.twl.hi.foundation.model.GroupMember;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.utils.GroupInfoHelper;
import com.twl.hi.foundation.utils.GroupStatusCheckCallback;
import com.twl.hi.select.R;
import com.twl.hi.select.api.request.GroupAddUsersRequest;
import com.twl.hi.select.api.request.GroupOwnerChangeRequest;
import com.twl.hi.select.api.request.GroupReduceRequest;
import com.twl.hi.select.bean.SelectNotifyBean;
import com.twl.hi.select.bean.SelectedContactsAndGroupsMutableModel;
import com.twl.hi.util.PushConstants;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import hi.kernel.Constants;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.ToastUtils;

public class SelectContactsViewModel extends FoundationViewModel {
    private static final String TAG = "SelectOnlyContactsViewModel";
    private ObservableInt selectCount = new ObservableInt(0);
    private SelectedContactsAndGroupsMutableModel selectedContactsMutableModel = new SelectedContactsAndGroupsMutableModel();
    private MutableLiveData<List<String>> selectLiveData = new MutableLiveData<>();
    private MutableLiveData<List<Group>> selectGroupLiveData = new MutableLiveData<>();
    private MutableLiveData<SelectNotifyBean> notifyRefreshMainLiveData = new MutableLiveData<>();
    private MutableLiveData<SelectNotifyBean> notifyCleanOtherLiveData = new MutableLiveData<>();
    private MutableLiveData<SelectNotifyBean> notifyDeleteSelectedLiveData = new MutableLiveData<>();
    private final ObservableBoolean multiSelectStatus = new ObservableBoolean(false); //当前状态是否是多选状态
    private SelectContactsParams params;
    private int urgentType = Constants.SHINING_APP;//加急默认选中类型
    private String mCurrentSearchId = "";

    public SelectContactsViewModel(Application application) {
        super(application);
    }

    public void init(SelectContactsParams params) {
        this.params = params;
        setIsMultiSelect(params.isMultiSelect());
        selectedContactsMutableModel.firstId = params.getFirstId();
    }

    public MutableLiveData<SelectNotifyBean> getNotifyDeleteSelectedLiveData() {
        return notifyDeleteSelectedLiveData;
    }

    public MutableLiveData<SelectNotifyBean> getNotifyCleanOtherLiveData() {
        return notifyCleanOtherLiveData;
    }

    public MutableLiveData<SelectNotifyBean> getNotifyRefreshMainLiveData() {
        return notifyRefreshMainLiveData;
    }

    public MutableLiveData<List<String>> getSelectLiveData() {
        return selectLiveData;
    }

    public MutableLiveData<List<Group>> getSelectGroupLiveData() {
        return selectGroupLiveData;
    }

    public SelectedContactsAndGroupsMutableModel getSelectedContactsMutableModel() {
        return selectedContactsMutableModel;
    }

    public void setIsMultiSelect(boolean multiSelect) {
        selectedContactsMutableModel.setMultiSelect(multiSelect);
    }

    public ArrayList<String> getAddIds() {
        return getSelectedContactsMutableModel().getAddIds();
    }

    public List<String> getExistIds() {
        return getSelectedContactsMutableModel().mExistIds;
    }

    public SelectProxy getSelectProxy() {
        return getSelectedContactsMutableModel().getSelectProxy();
    }

    public void notifyDeletedIds(ArrayList<String> remainingContacts, ArrayList<String> remainingGroups) {
        List<String> changeIds = new ArrayList<>();
        ArrayList<String> allContactIds = new ArrayList<>(selectedContactsMutableModel.getAddIds());
        for (String contactId : allContactIds) {
            if (!remainingContacts.contains(contactId)) {
                selectedContactsMutableModel.deleteSelectedContact(contactId);
                changeIds.add(contactId);
            }
        }
        List<String>  allGroupIds = new ArrayList<>(selectedContactsMutableModel.mSelectGroups);
        for (String groupId : allGroupIds) {
            if (!remainingGroups.contains(groupId)) {
                selectedContactsMutableModel.deleteSelectedGroup(groupId);
                changeIds.add(groupId);
            }
        }
        SelectNotifyBean selectNotifyBean = new SelectNotifyBean(false, changeIds);
        notifyDeleteSelectedLiveData.postValue(selectNotifyBean);
    }

    public void setSelectCountChange(int count) {
        selectCount.set(count);
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                selectLiveData.postValue(selectedContactsMutableModel.getAddIds());
                ArrayList<String> selectGroups = selectedContactsMutableModel.mSelectGroups;
                if (selectGroups.isEmpty()) {
                    selectGroupLiveData.postValue(Collections.emptyList());
                    return;
                }

                String[] ids = new String[selectGroups.size()];
                for (int i = 0; i < ids.length; i++) {
                    ids[i] = selectGroups.get(i);
                }
                List<Group> groups = ServiceManager.getInstance().getGroupService().getOnlyGroupByIdWithExtendCache(ids);
                Collections.sort(groups, new Comparator<Group>() {
                    @Override
                    public int compare(Group o1, Group o2) {
                        return Integer.compare(selectGroups.indexOf(o1.getGroupId()), selectGroups.indexOf(o2.getGroupId()));
                    }
                });
                selectGroupLiveData.postValue(groups);
            }
        });
    }

    public ObservableInt getCount() {
        return selectCount;
    }

    /**
     * 创建群
     *
     * @param list
     * @return
     */
    public LiveData<ApiResponse<GroupCreateResponse>> groupCreate(List<String> list) {
        MutableLiveData<ApiResponse<GroupCreateResponse>> liveData = new MutableLiveData<>();
        GroupCreateRequest req = new GroupCreateRequest(new BaseApiRequestCallback<GroupCreateResponse>() {

            @Override
            public void handleInChildThread(ApiData<GroupCreateResponse> data) {
                super.handleInChildThread(data);
                saveGroup(data);
            }

            private void saveGroup(ApiData<GroupCreateResponse> data) {
                GroupService service = ServiceManager.getInstance().getGroupService();
                Group group = new Group();
                group.setGroupId(data.resp.groupId);
                group.setOwnerId(data.resp.ownerId);
                group.setMemberList(data.resp.users);
                group.setGroupName(data.resp.groupName);
                group.setGroupNamePy(data.resp.groupNamePy);
                group.setAvatar(data.resp.avatar);
                group.setTinyAvatar(data.resp.tinyAvatar);
                group.setBulletin(data.resp.bulletin);
                group.setBulletinUpdateUid(data.resp.bulletinUpdateUid);
                group.setBulletinUpdateTs(data.resp.bulletinUpdateTs);
                group.setTop(data.resp.top);
                group.setSilence(data.resp.silence);
                group.setStatus(data.resp.status);
                group.setDeptId(data.resp.deptId);
                service.addGroup(group);
            }

            @Override
            public void onSuccess(ApiData<GroupCreateResponse> data) {
                liveData.setValue(ApiResponse.create(true, data.resp, null));
            }

            @Override
            public void onComplete() {

            }

            @Override
            protected boolean isFocusOnNetworkError() {
                return true;
            }

            @Override
            public void onFailed(ErrorReason reason) {
                liveData.setValue(ApiResponse.create(false, null, reason));
            }
        });
        ExecutorFactory.execWorkTask(() -> {
            req.setTimeoutSensitive(true);
            req.userIds = sortUserIdByPy(list);
            HttpExecutor.execute(req);
        });
        return liveData;
    }

    private String convertString(List<String> mSelects) {
        StringBuilder sb = new StringBuilder();
        for (String existId : mSelects) {
            sb.append(existId);
            sb.append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 添加群成员
     *
     * @param groupId
     * @param addIds
     * @param optionId
     * @return
     */
    public LiveData<ApiResponse<HttpResponse>> groupAddUsers(String groupId, List<String> addIds, int optionId) {
        MutableLiveData<ApiResponse<HttpResponse>> liveData = new MutableLiveData<>();
        setShowProgressBar(getResources().getString(R.string.select_add_group));
        GroupAddUsersRequest req = new GroupAddUsersRequest(new BaseApiRequestCallback<HttpResponse>() {

            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);

                GroupService groupService = ServiceManager.getInstance().getGroupService();
                List<String> membersId = groupService.getMembersId(groupId);

                List<GroupMember> list = new ArrayList<>();
                for (String uid : addIds) {
                    if (membersId.contains(uid)) {
                        continue;
                    }
                    GroupMember member = new GroupMember();
                    member.setUserId(uid);
                    member.setGroupId(groupId);
                    member.setCreateTime(System.currentTimeMillis());
                    list.add(member);
                }
                if (list.isEmpty()) {
                    return;
                }
                groupService.addGroupMembers(list);
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                ToastUtils.success(R.string.select_add_group_success);
                liveData.setValue(ApiResponse.create(true, data.resp, null));
            }

            @Override
            public void onComplete() {
                hideShowProgressBar();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                liveData.setValue(ApiResponse.create(false, null, reason));
            }
        });
        ExecutorFactory.execWorkTask(() -> {
            req.groupId = groupId;
            req.userIds = sortUserIdByPy(addIds);
            HttpExecutor.execute(req);
        });
        return liveData;
    }

    /**
     * 删除群成员
     *
     * @param groupId
     * @param selectIds
     */
    public MutableLiveData<Boolean> reduceGroupMembers(String groupId, List<String> selectIds) {
        MutableLiveData<Boolean> reduceLiveData = new MutableLiveData<>();
        setShowProgressBar("");
        GroupReduceRequest request = new GroupReduceRequest(new BaseApiRequestCallback<HttpResponse>() {

            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                ServiceManager.getInstance().getGroupService().reduceGroupMember(groupId, selectIds);
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                reduceLiveData.postValue(true);
            }

            @Override
            public void onComplete() {
                hideShowProgressBar();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                reduceLiveData.postValue(false);
            }
        });
        request.groupId = groupId;
        request.userIds = convertString(selectIds);
        HttpExecutor.execute(request);
        return reduceLiveData;
    }

    /**
     * 群组转让
     *
     * @param groupId
     * @param ownerId
     * @return
     */
    public LiveData<ApiResponse<HttpResponse>> requestChangeGroupOwner(String groupId, String ownerId) {
        MutableLiveData<ApiResponse<HttpResponse>> liveData = new MutableLiveData<>();
        GroupOwnerChangeRequest req = new GroupOwnerChangeRequest(new BaseApiRequestCallback<HttpResponse>() {

            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                liveData.postValue(ApiResponse.create(true, data.resp, null));
                Map<String, String> message = new HashMap<>();
                message.put(PushConstants.PUSH_MSG_TYPE, MessageConstants.MSG_GROUP_CHAT + "");
                message.put(PushConstants.PUSH_MSG_TO, groupId + "");
                ServiceManager.getInstance().getConversationService().setChatNotify(message);
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                liveData.postValue(ApiResponse.create(false, null, reason));
            }
        });
        req.groupId = groupId;
        req.ownerId = ownerId;
        HttpExecutor.execute(req);
        return liveData;
    }

    public void deleteSelectedContact(String userId) {
        if (requireOneMemberAtLeast()) {
            ToastUtils.failure(R.string.select_at_least_one_member);
            return;
        }
        getSelectedContactsMutableModel().deleteSelectedContact(userId);
        List<String> changedIds = new ArrayList<>();
        changedIds.add(userId);
        SelectNotifyBean selectNotifyBean = new SelectNotifyBean(false, changedIds);
        notifyDeleteSelectedLiveData.postValue(selectNotifyBean);
    }

    public void deleteSelectedGroup(String groupId) {
        if (requireOneMemberAtLeast()) {
            ToastUtils.failure(R.string.select_at_least_one_member);
            return;
        }
        getSelectedContactsMutableModel().deleteSelectedGroup(groupId);
        List<String> changedIds = new ArrayList<>();
        changedIds.add(groupId);
        SelectNotifyBean selectNotifyBean = new SelectNotifyBean(false, changedIds);
        notifyDeleteSelectedLiveData.postValue(selectNotifyBean);
    }

    private boolean requireOneMemberAtLeast() {
        if (params.canChooseNone) {
            return false;
        }
        ArrayList<String> groups = getSelectedContactsMutableModel().mSelectGroups;
        ArrayList<String> contacts = getSelectedContactsMutableModel().mSelects;
        return groups.size() + contacts.size() <= 1;
    }

    /**
     * 设置选中状态
     *
     * @param notifyCheck 通知变化 true选中，false删除
     * @param userId
     */
    public void notifyRefreshMainSelectedContact(boolean notifyCheck, String userId) {
        List<String> changedIds = new ArrayList<>();
        changedIds.add(userId);
        SelectNotifyBean selectNotifyBean = new SelectNotifyBean(notifyCheck, changedIds);
        notifyRefreshMainLiveData.postValue(selectNotifyBean);
    }

    /**
     * 设置选中状态
     *
     * @param notifyCheck 通知变化 true选中，false删除
     * @param userId
     */
    public void cleanSelectedContact(boolean notifyCheck, String userId) {
        List<String> changedIds = new ArrayList<>();
        changedIds.add(userId);
        SelectNotifyBean selectNotifyBean = new SelectNotifyBean(notifyCheck, changedIds);
        notifyCleanOtherLiveData.postValue(selectNotifyBean);
    }

    /**
     * 用户Id根据名称首字母排序 成字符串
     */
    private String sortUserIdByPy(List<String> ids){
        StringBuilder sb = new StringBuilder();
        List<Contact> contacts = ServiceManager.getInstance().getContactService().getContactsByIdsForCache(ids);
        Collections.sort(contacts, (o1, o2) -> o1.getUserNamePy().compareTo(o2.getUserNamePy()));
        for (Contact contact : contacts) {
            sb.append(contact.getUserId());
            sb.append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    public int getUrgentType() {
        return urgentType;
    }

    public void setUrgentType(int urgentType) {
        this.urgentType = urgentType;
    }

    public void updateSearchId(String searchId) {
        mCurrentSearchId = searchId;
    }

    public String getCurrentSearchId() {
        return mCurrentSearchId;
    }

    public void checkGroupStatus(String groupId, GroupStatusCheckCallback callback) {
        GroupInfoHelper.optWithGroupStatusCheck(groupId, MessageConstants.MSG_GROUP_CHAT, callback);
    }

    public void checkGroupStatus(String chatId, int chatType, GroupStatusCheckCallback callback) {
        GroupInfoHelper.optWithGroupStatusCheck(chatId, chatType, callback);
    }

    public ObservableBoolean getMultiSelectStatus() {
        return multiSelectStatus;
    }

    public void setMultiSelectStatus(boolean multiSelectStatus) {
        this.multiSelectStatus.set(multiSelectStatus);
    }
}