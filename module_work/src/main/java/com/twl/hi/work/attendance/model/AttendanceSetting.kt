package com.twl.hi.work.attendance.model

import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.twl.hi.work.api.response.bean.WorkNoticeBean

/**
 *@author: musa on 2022/5/14
 *@e-mail: yang<PERSON><PERSON><PERSON>@kanzhun.com
 *@desc:打卡设置页item实体类
 */
const val TYPE_PUNCH_IN = 1
const val TYPE_PUNCH_OUT = 2

const val PROFILE_TYPE_SHAKE_ATTENDANCE = 0
const val PROFILE_TYPE_AUTO_ATTENDANCE = 1
/**打卡页设置项*/
open class AttendanceSetting(val itemType : Int){
    companion object{
        const val ATTENDANCE_SETTING_ITEM_TYPE_DIVIDER = 0
        const val ATTENDANCE_SETTING_ITEM_TYPE_PROFILE = ATTENDANCE_SETTING_ITEM_TYPE_DIVIDER + 1
        const val ATTENDANCE_SETTING_ITEM_TYPE_NOTICE_TIME = ATTENDANCE_SETTING_ITEM_TYPE_PROFILE + 1
        const val ATTENDANCE_SETTING_ITEM_TYPE_NOTICE_TYPE = ATTENDANCE_SETTING_ITEM_TYPE_NOTICE_TIME + 1
    }
}

/**分隔栏item*/
class AttendanceSettingDividerBean(
    itemType: Int,
    val text: String
) : AttendanceSetting(itemType)

/**设置item*/
class AttendanceSettingProfileBean(
    itemType: Int,
    val profileCode : Int,
    val text: String,
    val tip: String,
    val isChosen: ObservableBoolean
) : AttendanceSetting(itemType){
}
/**提醒设置item*/
class AttendanceSettingNoticeBean(
    itemType: Int,
    val attendanceType : Int,
    val timeList : MutableLiveData<List<WorkNoticeBean>>,
    val noticeType: ObservableField<String>
) : AttendanceSetting(itemType)

