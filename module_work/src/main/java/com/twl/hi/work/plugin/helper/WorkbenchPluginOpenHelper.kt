package com.twl.hi.work.plugin.helper

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import androidx.appcompat.app.AppCompatActivity
import com.amap.api.mapcore.util.it
import com.sankuai.waimai.router.Router
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.api.request.QueryOpenAppLastedRequest
import com.twl.hi.basic.api.response.QueryOpenAppLastedResult
import com.twl.hi.basic.dialog.DialogUtils
import com.twl.hi.basic.model.TemporaryTokenBean
import com.twl.hi.basic.model.WebViewBean
import com.twl.hi.export.audio.shorthand.IAudioShortHandService
import com.twl.hi.export.audio.shorthand.router.AudioShortHandPageRouter
import com.twl.hi.export.chat.router.ChatPageRouter
import com.twl.hi.export.main.router.AppPageRouter
import com.twl.hi.export.webview.WebViewPageRouter
import com.twl.hi.export.workflow.router.WorkflowPagerRouter
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.base.HostConfig
import com.twl.hi.foundation.facade.SyncDispatch
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.User
import com.twl.hi.login.TeamCreateActivity
import com.twl.hi.work.FeedBackActivity
import com.twl.hi.work.GroupAttendanceActivity
import com.twl.hi.work.R
import com.twl.hi.work.attendance.AttendanceActivity.Companion.goAttendanceActivity
import com.twl.hi.work.utils.PointHelper
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.error.ErrorCodes
import com.twl.http.error.ErrorReason
import com.twl.kzmp.api.response.CheckVersionResponse
import com.twl.kzmp.callback.IKZMPActionListener
import com.twl.kzmp.openKZApp
import com.twl.utils.GsonUtils
import com.twl.utils.SettingBuilder
import com.twl.utils.URLUtils
import com.twl.utils.jurisdiction.JurisdictionUtils
import hi.kernel.BuildConfig
import hi.kernel.BundleConstants
import hi.kernel.Constants
import hi.kernel.HiKernel
import io.dcloud.feature.sdk.StartArgs.appId
import lib.twl.common.base.BaseApplication
import lib.twl.common.dialog.MyProgressDialog
import lib.twl.common.ext.getResourceString
import lib.twl.common.ext.getStatusBarsHeightCache
import lib.twl.common.ext.pxToDp
import lib.twl.common.ext.safeDismissDialog
import lib.twl.common.ext.safeShowDialog
import lib.twl.common.permission.PermissionAvoidManager
import lib.twl.common.util.AppUtil
import lib.twl.common.util.DeviceIdUtils
import lib.twl.common.util.HiPermissionUtil
import lib.twl.common.util.ToastUtils
import lib.twl.common.util.url.UrlUtil
import org.json.JSONException
import org.json.JSONObject
import java.util.Calendar

/**
 *@author: musa on 2023/1/9
 *@e-mail: <EMAIL>
 *@desc: 工作台打开插件辅助类
 */
object WorkbenchPluginOpenHelper {
    val WORK_ATTENDANCE: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_s8yx4txjh9mmataz"
                else -> "bli_onlfmccrssrnjgda"
            }
        }//打卡

    val WORK_MEETING: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_wxijede7co4a3h1b"
                else -> "bli_jdtraew4hqyxl85w"
            }
        }//会议室预定
    val WORK_I_CLOUD: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_qxh56ximxpvvyxtt"
                else -> "bli_ye3cwxzrgw7bbjeb"
            }
        }//共享云盘
    val WORK_TASK_FLOW: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_aqdkvbroydgxmw2d"
                else -> "bli_shz5m83ix8l9uito"
            }
        }//工作流

    val WORK_ONLINE_FILE: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_mfgd42pzwaecyaqg"
                else -> "bli_0i24zdq5exs8vtlv"
            }
        }//文档

    @JvmStatic
    val WORK_DAY_IN_SERVICE: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_gk1nqvvxdc6te5vt"
                else -> "bli_z1r04geosyyrxxxi"
            }
        }//在职天数

    val WORK_FEEDBACK: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_98bro6lpcrx1ek4i"
                else -> "bli_j4lfmrtv039dvqol"
            }
        }//意见反馈

    val WORK_GROUP_ATTENDANCE: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_2hikkekr6rmwudfa"
                else -> "bli_alfkduad10qz4hpw"
            }
        }//团队考勤

    val WORK_IT_PASSWORD: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_rpoosipjw5e0oure"
                else -> "bli_de5grnsu56kbkrzf"
            }
        } //IT密码管理

    val WORK_ACCESS_APPLICATION: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_onneigz8kuk9bcdc"
                else -> "bli_zj8dsjpt8scsulx7"
            }
        }//准入

    val WORK_SHORTHAND: String
        get() {
            return when (HostConfig.getCONFIG()) {
                HostConfig.Addr.PRE, HostConfig.Addr.ONLINE -> "bli_9tiptpo3nlw254f5"
                else -> "bli_l12a0urez3nkhlqe"
            }
        }//AI速记
    private const val TAG = "WorkbenchPluginOpenHelp"
    private var loadingDialog: MyProgressDialog? = null

    /**
     * 打开应用插件
     * @param context 上下文
     * @param appId
     * @param configParam appName:应用名称 appCode:低版本的code仅在兼容低版本时使用 openAppType:开放平台应用打开类型
     * chatId:会话id chatType:会话类型 小程序可选参数  sceneCode:小程序打开场景 埋点用
     * isBrowserOpen:是否吊起外部浏览器打开网页
     */
    @JvmOverloads
    fun openWorkbenchPlugin(
        context: Context,
        appId: String?,
        configParam: MutableMap<String, String?>? = null,
        callback: ((Boolean) -> Unit)? = null
    ) {
        showLoading()
        val request =
            QueryOpenAppLastedRequest(object : BaseApiRequestCallback<QueryOpenAppLastedResult>() {
                override fun onSuccess(data: ApiData<QueryOpenAppLastedResult?>?) {
                    data?.resp?.let {
                        if (it.getEnableApp()) { //app端不可用app
                            ToastUtils.ss(R.string.work_open_in_pc)
                            callback?.invoke(false)
                            return
                        }
                        callback?.invoke(true)
                        if (it.getMock()) { //虚拟插件
                            val bundleDefault = Bundle()
                            bundleDefault.putString(BundleConstants.BUNDLE_DATA_STRING, "")
                            AppUtil.startActivity(
                                context,
                                Intent(context, TeamCreateActivity::class.java).apply {
                                    putExtras(bundleDefault)
                                })
                            return
                        }
                        PointHelper.workFunctionClick(
                            appId ?: "",
                            it.name ?: "",
                            mutableListOf(WORK_ACCESS_APPLICATION, WORK_IT_PASSWORD).contains(appId)
                        )
                        if (appCodeMatchPlugin(
                                context,
                                it,
                                it.appId ?: "0",
                                configParam?.get("appName") ?: ""
                            )
                        ) { //appId写死匹配的ship插件
                            return
                        }
                        if (handleOpenAppJump(context, it, it.appId ?: "0", configParam)) { //开放平台插件
                            TLog.info(TAG, "openWorkbenchPlugin handleOpenAppJump")
                            return
                        }
                        openShipH5Plugin(context, it) //shipH5插件
                    }
                }

                override fun onComplete() {
                    hideLoading()
                }

                override fun onFailed(reason: ErrorReason?) {
                    callback?.invoke(false)
                    if (configParam?.get("fromNavTab") == "1" && reason != null && (reason.errCode == ErrorCodes.PLUGIN_IS_UNABLE || reason.errCode == ErrorCodes.PLUGIN_IS_INVISIBLE)) {
                        showPluginUnableDialog(appId)
                    } else {
                        ToastUtils.failure(reason?.errReason)
                    }
                }

                override fun isFocusOnNetworkError(): Boolean {
                    return true
                }

                override fun showFailed(reason: ErrorReason?) {

                }

            })
        if (appId.isNullOrEmpty().not()) {
            request.appId = appId
        } else {
            request.appCode = configParam?.get("appCode") ?: ""
        }
        configParam?.get("openAppType")?.let {
            request.appType = it.toIntOrNull() ?: 0
        }
        HttpExecutor.execute(request)
    }

    private fun showPluginUnableDialog(appId: String?) {
        if (appId.isNullOrEmpty()) return
        val activity: Context? = BaseApplication.getApplication().topContext
        if (activity != null) {
            DialogUtils.Builder(activity)
                .setTitle(R.string.work_plugin_is_unable)
                .setCanceledOnTouchOutside(false)
                .setAutoCloseAfterClick(true)
                .setPositive(R.string.i_know)
                .setPositiveListener {
                    ServiceManager.getInstance().navTabService.deleteTabByIdAsync(appId)
                }
                .build()
        }

    }

    /**
     * 打开开放平台应用
     * @return 是否消费
     */
    private fun handleOpenAppJump(
        context: Context,
        data: QueryOpenAppLastedResult,
        appId: String,
        extraParamsMap: MutableMap<String, String?>?
    ): Boolean {
        when (data.appType) {
            QueryOpenAppLastedResult.APP_TYPE_APPLET -> {
                if (context !is AppCompatActivity) return false
                val permissionAvoidManager = PermissionAvoidManager(context)
                val mpParams = JSONObject()
                try {
                    mpParams.put("app_id", appId)
                    mpParams.put("user_id", HiKernel.getHikernel().account.userId)
                    mpParams.put("company_id", HiKernel.getHikernel().account.companyId)
                    mpParams.put("host_version", BuildConfig.VERSION_NAME)
                    mpParams.put("device_id", DeviceIdUtils.getDeviceId())
                    mpParams.put("is_debug", HostConfig.getCONFIG() == HostConfig.Addr.QA)
                    extraParamsMap?.let {
                        mpParams.put("scene_id", it["sceneCode"] ?: "")
                        mpParams.put("chat_id", it["chatId"])
                        mpParams.put("chat_type", it["chatType"])
                        mpParams.put("direct_path", it["appPath"] ?: "")
                    }
                } catch (e: JSONException) {
                    TLog.error(TAG, "handleOpenAppJump APP_TYPE_APPLET %s", e)
                }
                val hasPermission = PermissionAvoidManager.hasPermissions(
                    context,
                    HiPermissionUtil.storagePermissions
                )
                val checkVersion = GsonUtils.getGson()
                    .fromJson(GsonUtils.getGson().toJson(data), CheckVersionResponse::class.java)
                if (!hasPermission) {
                    permissionAvoidManager.requestPermission(
                        HiPermissionUtil.storagePermissions
                    ) { getpermission: Boolean, shouldShowAllRequestPermissionRationale: Boolean ->
                        if (getpermission) {
                            TLog.error(TAG, "openKZApp with permission")
                            context.openKZApp(
                                mpParams,
                                checkVersion,
                                listener = object : IKZMPActionListener {
                                    override fun onOpenApp(uri: Uri) {
                                        AppUtil.startUri(context, uri.toString())
                                    }

                                    override fun onUpgrade() {
                                        SyncDispatch.getInstance()
                                            .setEvent(
                                                SyncDispatch.EVENT_PLATFORM_PLUGIN_STATUS_SYNC,
                                                null
                                            )
                                    }
                                })
                        } else {
                            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.S_V2) {
                                ToastUtils.ss(R.string.open_read_permission_sv2)
                            } else {
                                ToastUtils.ss(R.string.open_read_permission)
                            }
                        }
                    }
                } else {
                    context.openKZApp(
                        mpParams,
                        checkVersion,
                        listener = object : IKZMPActionListener {
                            override fun onOpenApp(uri: Uri) {
                                AppUtil.startUri(context, uri.toString())
                            }

                            override fun onUpgrade() {
                                SyncDispatch.getInstance()
                                    .setEvent(SyncDispatch.EVENT_PLATFORM_PLUGIN_STATUS_SYNC, null)
                            }
                        })
                }
                return true
            }

            QueryOpenAppLastedResult.APP_TYPE_PLATFORM_H5 -> {
                val finalUrl = extraParamsMap?.get("appPath")?.let {
                    combinePath(data.openH5Url ?: "", it)
                } ?: data.openH5Url
                jumpH5(
                    context,
                    finalUrl,
                    extraParamsMap
                )
                return true
            }

            QueryOpenAppLastedResult.APP_TYPE_ROBOT -> {
                val bundle = Bundle()
                bundle.putString(BundleConstants.BUNDLE_USER_ID, data.openRobotUserId ?: "")
                AppUtil.startUri(context, ChatPageRouter.SINGLE_CHAT_ACTIVITY, bundle)
                return true
            }
        }
        return false
    }

    /**
     * 参数名相同时的两个规则
     * 1、参数处于不同位置，优先级: 插件详情返回的 url＞path＞applink上的参数
     * 2、参数处于同一位置，取该位置的第一个值作为参数值
     *
     * 这里是处理 服务端返回的url 和 path 的相同参数优先级
     */
    private fun combinePath(
        url: String,
        appPath: String
    ): String {
        val webUrl = Uri.parse(url)
        val pathUrl = Uri.parse(appPath)
        //做参数去重的map
        val params: MutableMap<String, String> = mutableMapOf()

        //服务端返回的url中的参数优先级最高
        UrlUtil.combineParams(webUrl, params)
        UrlUtil.combineParams(pathUrl, params)

        val result = webUrl.buildUpon()
            .clearQuery()

        //替换path
        if (!pathUrl.path.isNullOrEmpty()) {
            result.path(pathUrl.path)
        }
        //替换hash
        if (!pathUrl.fragment.isNullOrEmpty()) {
            result.fragment(pathUrl.fragment)
        }

        params.forEach {
            result.appendQueryParameter(it.key, it.value)
        }
        return result.build().toString()
    }

    private fun appCodeMatchPlugin(
        context: Context,
        response: QueryOpenAppLastedResult,
        appId: String,
        appName: String
    ): Boolean {
        when (appId) {
            WORK_ATTENDANCE -> {
                toAttendanceFragment(context)
                return true
            }

            WORK_GROUP_ATTENDANCE -> {
                val groupAttendanceIntent = GroupAttendanceActivity.createIntent(context)
                AppUtil.startActivity(context, groupAttendanceIntent)
                return true
            }

            WORK_FEEDBACK -> {
                FeedBackActivity.jumpToFeedback(context, response.openH5Url)
                return true
            }

            WORK_I_CLOUD -> {
                ToastUtils.ss("已下线")
                return true
            }

            WORK_SHORTHAND -> {
                val service = Router.getService(
                    IAudioShortHandService::class.java,
                    AudioShortHandPageRouter.AUDIO_SHORTHAND_SERVICE
                )
                context?.let {
                    service?.startAudioShortHand(it)
                }
                return true
            }

            WORK_TASK_FLOW -> {
                WorkflowPagerRouter.jumpToTaskListActivity(context, null, null)
                return true
            }

            WORK_DAY_IN_SERVICE -> {
                if (TextUtils.isEmpty(response.openH5Url)) {
                    ToastUtils.ssdc(R.string.apply_h5_url)
                    return false
                }
                val bean = WebViewBean()
                if (TextUtils.isEmpty(appName)) {
                    bean.name = ""
                } else {
                    bean.name = appName
                }
                bean.style = Constants.WEB_STYLE_HAS_NO_TITLE_AND_TRANSLUCENT
                val urlBuilder = java.lang.StringBuilder()
                urlBuilder.append(response.openH5Url)
                val param = URLUtils.parseUrlParams(response.openH5Url)
                val hasParam = param.isNotEmpty()
                val user: User? = ServiceManager.getInstance().profileService.user.value
                if (user != null) {
                    urlBuilder.append(if (hasParam) "&" else "?")
                    urlBuilder.append("entrydate=")
                    urlBuilder.append(user.entryDate)
                    urlBuilder.append("&workdays=")
                    urlBuilder.append(user.workDays)
                    urlBuilder.append("&barheight=")
                    urlBuilder.append(getStatusBarsHeightCache().pxToDp)
                }
                bean.url = urlBuilder.toString()
                val webBundle = Bundle()
                webBundle.putSerializable(Constants.DATA_WEB_BEAN, bean)
                AppUtil.startUri(context, WebViewPageRouter.WEB_VIEW_ACTIVITY, webBundle)
                return true
            }

            WORK_MEETING -> {
                AppUtil.getDefaultUriRequest(context, AppPageRouter.MAIN_TAB_ACTIVITY)
                    .setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .putExtra(
                        BundleConstants.BUNDLE_URL,
                        BundleConstants.SCHEME_TO_RESERVE_MEETING_ROOM
                    )
                    .start()
                return true
            }

            WORK_ONLINE_FILE -> {
                val visible = SettingBuilder.getInstance()
                    .getJurisdictionVisible(JurisdictionUtils.JURISDICTION_SHI_MO)
                if (JurisdictionUtils.IDENTITY_VISIBLE != visible) {
                    ToastUtils.ss(R.string.no_permission_shimo.getResourceString())
                    return true
                }
                val onlineFileBean = WebViewBean()
                onlineFileBean.url = SettingBuilder.getInstance().onlineFileMidUrl
                val bundleOnLine = Bundle()
                bundleOnLine.putSerializable(Constants.DATA_WEB_BEAN, onlineFileBean)
                AppUtil.startUri(context, WebViewPageRouter.WEB_VIEW_ACTIVITY, bundleOnLine)
                return true
            }
        }
        return false
    }

    private fun openShipH5Plugin(context: Context, response: QueryOpenAppLastedResult): Boolean {
        if (TextUtils.isEmpty(response.openH5Url)) {
            ToastUtils.ssdc(R.string.apply_h5_url)
            return false
        }
        jumpH5(context, response.openH5Url)
        return true
    }

    private fun jumpH5(
        context: Context,
        appUrl: String?,
        params: Map<String, String?>? = null
    ) {
        appUrl ?: return
        val url = Uri.parse(appUrl)
        if (url.isOpaque) return

        //是否有顶部导航栏
        val noHead = params?.get("noHead")?.takeIf { it.isNotEmpty() } ?: "0"
        val isBrowserOpen = params?.get("isBrowserOpen") == "1"

        if (noHead == "1") { //设置为url中query，加载url之前会解析
            URLUtils.addParam(appUrl, "noHead", "1")
        }

        if (isBrowserOpen) {
            URLUtils.addParam(appUrl, "browserOpen", "1")
        }

        AppUtil.startUri(context, appUrl)
    }

    private fun toAttendanceFragment(context: Context) {
        val calendar = Calendar.getInstance()
        val year = calendar[Calendar.YEAR]
        val month = calendar[Calendar.MONTH] + 1
        goAttendanceActivity(context, year, month, HiKernel.getHikernel().account.userId)
    }

    private fun showLoading() {
        loadingDialog.safeDismissDialog()
        loadingDialog = MyProgressDialog(BaseApplication.getApplication().topContext)
        loadingDialog?.safeShowDialog()
    }

    private fun hideLoading() {
        loadingDialog.safeDismissDialog()
        loadingDialog = null
    }

    interface OnTokenResult {
        fun onSuccess(temporaryTokenBean: TemporaryTokenBean)

        fun onFail(reason: ErrorReason)
    }
}