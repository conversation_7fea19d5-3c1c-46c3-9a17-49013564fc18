package com.twl.hi.work.model

import com.twl.hi.foundation.api.response.WorkStationResponse
import com.twl.hi.foundation.api.response.WorkStationTodoResponse
import com.twl.hi.foundation.api.response.bean.*
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.utils.ContactUtils
import com.twl.hi.work.api.response.WorkWidgetTab
import com.twl.hi.work.api.response.WorkWidgetTabResponse

/**
 * Author : Xuweixiang .
 * Date   : On 2022/6/28
 * Email  : Contact <EMAIL>
 * Desc   : 工作台数据处理类
 *
 */


fun convertResponse2Banner(stationResponse: WorkStationResponse?): ItemWorkStationBanner {
    return stationResponse?.run {
        banner?.run {
            item?.takeIf {
                it.isEmpty().not()
            }?.let {
                ItemWorkStationBanner(it, time)
            }
        }
    } ?: ItemWorkStationBanner(emptyList(), 0)
}

fun convertResponse2Profile(stationResponse: WorkStationResponse?): ItemWorkStationProfile {
    return stationResponse?.run {
        ServiceManager.getInstance().profileService.user.value?.let { user ->
            personalInfo?.run {
                ItemWorkStationProfile(
                    user.avatar,
                    user.userName,
                    slogan,
                    days,
                    url,
                    isBirthday == 1
                )
            }
        }
    } ?: ItemWorkStationProfile(
        isBirthday = false
    )
}

fun convertResponse2Announcement(stationResponse: WorkStationResponse?): ItemWorkStationAnnouncement {
    return stationResponse?.run {
        announcementList?.let {
            ItemWorkStationAnnouncement(it)
        }
    } ?: ItemWorkStationAnnouncement(emptyList())
}

fun convertResponse2Widget(response: WorkWidgetTabResponse?): ItemWorkStationWidget {
    return response?.run {
        ItemWorkStationWidget(dataSourceList?.map { WorkWidgetTab(it.code, it.name) } ?: emptyList())
    } ?: ItemWorkStationWidget(emptyList())
}

// duration 单位秒
data class ItemWorkStationBanner(val banners: List<BannerItemBean>, val interval: Int)

data class ItemWorkStationProfile(
    val avatarUrl: String? = "",
    val userName: String? = "",
    val signature: String? = "",
    val entryTime: String? = "",
    val iconUrl: String? = "",
    val isBirthday: Boolean
) {
    fun avatarStr(): String = ContactUtils.getDisplayAvatarStr(userName)
}

data class ItemWorkStationWidget(val widgets: List<WorkWidgetTab>)

data class ItemWorkStationAnnouncement(val announcements: List<WorkStationAnnouncementBean>)
