package com.twl.hi.work;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.facebook.drawee.drawable.ScalingUtils;
import com.facebook.drawee.generic.RoundingParams;
import com.facebook.drawee.view.SimpleDraweeView;
import com.google.android.material.tabs.TabLayout;
import com.google.gson.JsonObject;
import com.twl.hi.basic.adapter.MyMultiTypeAdapter;
import com.twl.hi.basic.dialog.DialogUtils;
import com.twl.hi.basic.helpers.RemindViewUpdateHelper;
import com.twl.hi.basic.model.WebViewBean;
import com.twl.hi.basic.model.select.ForwardMessageCardBean;
import com.twl.hi.basic.util.DebouncedClickListener;
import com.twl.hi.basic.util.Scene;
import com.twl.hi.basic.views.group.LinearLayoutManagerWrapper;
import com.twl.hi.basic.views.menu.FloatMenuCallback;
import com.twl.hi.basic.views.menu.IconFloatMenu;
import com.twl.hi.basic.views.menu.MenuItem;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.export.select.bean.SelectBaseParams;
import com.twl.hi.export.select.bean.SelectConversationParams;
import com.twl.hi.export.select.router.SelectPageRouter;
import com.twl.hi.export.webview.WebViewPageRouter;
import com.twl.hi.export.work.router.WorkPageRouter;
import com.twl.hi.foundation.MessageFactory;
import com.twl.hi.foundation.SendMessageContent;
import com.twl.hi.foundation.api.response.bean.BannerItemBean;
import com.twl.hi.foundation.api.response.bean.SystemPluginBean;
import com.twl.hi.foundation.api.response.bean.SystemPluginGroupBean;
import com.twl.hi.foundation.api.response.bean.TodoBean;
import com.twl.hi.foundation.api.response.bean.WorkStationAnnouncementBean;
import com.twl.hi.foundation.base.fragment.FoundationVMFragment;
import com.twl.hi.foundation.helper.ConfigBean;
import com.twl.hi.foundation.helper.GlobalRemindHelper;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.logic.SystemGrayConfigService;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard;
import com.twl.hi.foundation.model.workbench.ItemWorkStationFavorite;
import com.twl.hi.foundation.model.workbench.ItemWorkStationPlugin;
import com.twl.hi.foundation.utils.MessageUtils;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.login.TeamCreateActivity;
import com.twl.hi.work.adapter.PluginAdapter;
import com.twl.hi.work.api.response.WorkWidgetTab;
import com.twl.hi.work.attendance.AttendanceActivity;
import com.twl.hi.work.callback.WorkCallback;
import com.twl.hi.work.callback.WorkForwardSuccessCallback;
import com.twl.hi.work.databinding.WorkBannerBinding;
import com.twl.hi.work.databinding.WorkFragmentBinding;
import com.twl.hi.work.databinding.WorkItemGroupAllBinding;
import com.twl.hi.work.databinding.WorkItemGroupBinding;
import com.twl.hi.work.databinding.WorkItemWidgetBinding;
import com.twl.hi.work.databinding.WorkViewAnnouncementBinding;
import com.twl.hi.work.databinding.WorkViewAnnouncementContainerBinding;
import com.twl.hi.work.databinding.WorkViewProfileBinding;
import com.twl.hi.work.dialog.OnCategorySelectListener;
import com.twl.hi.work.dialog.OnMoreActionListener;
import com.twl.hi.work.dialog.VisibleListener;
import com.twl.hi.work.dialog.WorkCommonReactionDialogKt;
import com.twl.hi.work.model.ItemWorkStationAnnouncement;
import com.twl.hi.work.model.ItemWorkStationBanner;
import com.twl.hi.work.model.ItemWorkStationProfile;
import com.twl.hi.work.model.ItemWorkStationWidget;
import com.twl.hi.work.model.WorkWidgetCategoryBean;
import com.twl.hi.work.plugin.ConvertorKt;
import com.twl.hi.work.plugin.helper.WorkbenchPluginOpenHelper;
import com.twl.hi.work.utils.PointHelper;
import com.twl.hi.work.viewmodel.WorkViewModel;
import com.twl.hi.work.viewmodel.WorkViewModelHelper;
import com.twl.hi.work.views.WorkWidgetView;
import com.twl.utils.SettingBuilder;
import com.twl.utils.jurisdiction.JurisdictionUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import hi.kernel.HiKernel;
import hi.kernel.RequestCodeConstants;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;
import lib.twl.common.ext.KotlinExtKt;
import lib.twl.common.ext.ViewExtKt;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.ProcessHelper;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.ToastUtils;
import lib.twl.common.views.banner.BannerAdapter;
import lib.twl.common.views.banner.Indicator;
import lib.twl.common.views.banner.indicator.BannerIndicator;
import lib.twl.common.views.banner.listener.OnBannerListener;
import lib.twl.common.views.banner.viewholder.SimpleDraweeHolder;

/**
 * 工作台应用列表
 */
public class WorkFragment extends FoundationVMFragment<WorkFragmentBinding, WorkViewModel> implements WorkCallback, OnBannerListener<BannerItemBean>, WorkForwardSuccessCallback {

    private static final int TAB_SCROLL_OFFSET = (int) KotlinExtKt.getDp(9);
    private final Handler handler = new WorkHandler(this);
    MyMultiTypeAdapter mWorkStationAdapter;
    private PluginAdapter mWorkAdapter;
    private PluginAdapter mFavoriteAdapter;
    private SparseArray<TabLayout> mTabLayouts = new SparseArray<>(2);
    // 是否通过同步操作，选择了 Tab
    private boolean selectBySync = false;
    private Runnable mAnnounceRunnable = null;

    private DialogUtils mRemoveWidgetAlertDialog;
    private WorkViewModelHelper workViewModelHelper;
    private boolean canScroll = true;//长按出弹框，如果松手有滑动，会导致移位，所以先禁止滑动，pupup消失以后再可滚

    @Override
    protected void initFragment() {
        getDataBinding().flWork.setPadding(0, ViewExtKt.getStatusBarsHeight(getActivity()), 0, 0);
        getDataBinding().layoutForwardMessageSuccess.setCallback(this);
        getDataBinding().title.setPivotX(0);
        getDataBinding().recyclerPlugins.setLayoutManager(new LinearLayoutManagerWrapper(activity){
            @Override
            public boolean canScrollVertically() {
                return canScroll && super.canScrollVertically();
            }
        });
        workViewModelHelper = new WorkViewModelHelper(getViewModel());
        initStationAdapter();
        getDataBinding().recyclerPlugins.setAdapter(mWorkStationAdapter);
        ((SimpleItemAnimator) getDataBinding().recyclerPlugins.getItemAnimator()).setSupportsChangeAnimations(false);
        getViewModel().getOperationEnd().observe(this, operationEnd -> {
            if (operationEnd) {
                dismissProgressDialog();
            }
        });

        getViewModel().getProfileLiveData().observe(this, profileItem -> {
            if (mWorkStationAdapter != null && profileItem != null) {
                List dataList = mWorkStationAdapter.getData();
                int profileIndex = CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationProfile);
                updateAdapterItem(profileItem, profileIndex, 0);
            }
        });

        getViewModel().getWidgetLiveData().observe(this, widgetItem -> {
            if (mWorkStationAdapter != null) {
                List dataList = mWorkStationAdapter.getData();
                int widgetIndex = CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationWidget);
                if (widgetItem != null && !widgetItem.getWidgets().isEmpty()) {
                    int targetIndex = 0;
                    targetIndex += CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationProfile) >= 0 ? 1 : 0;
                    targetIndex += CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationAnnouncement) >= 0 ? 1 : 0;
                    targetIndex += CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationBanner) >= 0 ? 1 : 0;
                    updateAdapterItem(widgetItem, widgetIndex, targetIndex);
                } else {
                    if (widgetIndex > 0) {
                        removeAdapterItem(widgetIndex);
                    }
                }
            }
        });

        getViewModel().getAnnouncementLiveData().observe(this, announcementItem -> {
            if (mWorkStationAdapter != null && announcementItem != null) {
                List dataList = mWorkStationAdapter.getData();
                int announcementIndex = CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationAnnouncement);
                int targetIndex = CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationProfile) >= 0 ? 1 : 0;
                if (!announcementItem.getAnnouncements().isEmpty()) {
                    updateAdapterItem(announcementItem, announcementIndex, targetIndex);
                } else if (announcementIndex > 0) {
                    removeAdapterItem(announcementIndex);
                }
            }
        });

        getViewModel().getBannerLiveData().observe(this, bannerItem -> {
            if (mWorkStationAdapter != null && bannerItem != null) {
                List dataList = mWorkStationAdapter.getData();
                int bannerIndex = CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationBanner);
                int targetIndex = 0;
                if (CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationProfile) >= 0) {
                    targetIndex++;
                }
                if (CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationAnnouncement) >= 0) {
                    targetIndex++;
                }
                if (bannerItem.getBanners().size() > 0) {
                    updateAdapterItem(bannerItem, bannerIndex, targetIndex);
                } else if (bannerIndex >= 0) { //没有banner里没有数据且之前填充过则删除banner item
                    removeAdapterItem(bannerIndex);
                }
            }
        });
        getViewModel().getFavoriteLiveData().observe(this, favoriteItem -> {
            if (mWorkStationAdapter != null && favoriteItem != null) {
                //增加一个添加常用占位
                List<SystemPluginBean> uiData = new ArrayList<>();
                SystemPluginBean addItem = new SystemPluginBean();
                addItem.appId = Constants.WORK_FAVORITE_ADD;
                uiData.addAll(favoriteItem.getPlugins());
                uiData.add(addItem);
                //不能直接修改 favoriteItem，会污Domain层中的数据源
                ItemWorkStationFavorite favoriteDecor = new ItemWorkStationFavorite(uiData, favoriteItem.getName());

                List dataList = mWorkStationAdapter.getData();
                int todoIndex = CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationFavorite);
                int targetIndex = 0;
                if (CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationProfile) >= 0) {
                    targetIndex++;
                }
                if (CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationAnnouncement) >= 0) {
                    targetIndex++;
                }
                if (CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationBanner) >= 0) {
                    targetIndex++;
                }
                updateAdapterItem(favoriteDecor, todoIndex, targetIndex);
            }
        });
        getViewModel().getGroupPluginLiveData().observe(this, groupPluginItem -> {
            if (mWorkStationAdapter != null && groupPluginItem != null) {
                List dataList = mWorkStationAdapter.getData();
                int todoIndex = CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationPlugin);
                int targetIndex = 0;
                if (CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationProfile) >= 0) {
                    targetIndex++;
                }
                if (CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationAnnouncement) >= 0) {
                    targetIndex++;
                }
                if (CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationBanner) >= 0) {
                    targetIndex++;
                }
                if (CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationFavorite) >= 0) {
                    targetIndex++;
                }
                updateAdapterItem(groupPluginItem, todoIndex, targetIndex);
                initTabs(getDataBinding().tabs, groupPluginItem.getPluginGroups(), 0);
            }
        });
        getViewModel().getExternalScanResult().observe(this, temporaryTokenBean -> {
            if (temporaryTokenBean == null) {
                return;
            }
            WebViewBean webViewBean = new WebViewBean();
            webViewBean.setUrl(temporaryTokenBean.getUrl());
            webViewBean.setStyle(temporaryTokenBean.getStyle());
            webViewBean.setNeedGpsService(true);
            Bundle bundle = new Bundle();
            bundle.putSerializable(Constants.DATA_WEB_BEAN, webViewBean);
            AppUtil.startUri(activity, WebViewPageRouter.WEB_VIEW_ACTIVITY, bundle);
        });
        getDataBinding().recyclerPlugins.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                LinearLayoutManager layoutManager = (LinearLayoutManager) getDataBinding().recyclerPlugins.getLayoutManager();
                int lastVisiblePosition = layoutManager.findLastVisibleItemPosition();
                View lastView = layoutManager.findViewByPosition(lastVisiblePosition);
                float totalOffset = KotlinExtKt.getDp(15) + KotlinExtKt.getDp(10);
                int top = lastView.getTop();
                int tabVisible;
                if (top < -totalOffset) {
                    tabVisible = View.VISIBLE;
                    getDataBinding().appBar.setBackgroundColor(Color.WHITE);
                } else {
                    tabVisible = View.GONE;
                    getDataBinding().appBar.setBackgroundColor(getResources().getColor(R.color.color_F9F9FB));
                }
                getDataBinding().tabs.setVisibility(tabVisible);
            }
        });

        GlobalRemindHelper.INSTANCE.remindLiveData().observe(this, new Observer<Map<String, ConfigBean>>() {
            @Override
            public void onChanged(Map<String, ConfigBean> stringConfigBeanMap) {
                if (stringConfigBeanMap == null) {
                    RemindViewUpdateHelper.INSTANCE.updateRemindView(activity, getDataBinding().remindStub, "", null);
                    return;
                }
                if (GlobalRemindHelper.INSTANCE.isGlobalRemindShow(stringConfigBeanMap)) {
                    RemindViewUpdateHelper.INSTANCE.updateRemindView(activity, getDataBinding().remindStub, SystemGrayConfigService.GraySettings.PageRemind.WHOLE_HOMEPAGE_REMIND, stringConfigBeanMap);
                } else if (GlobalRemindHelper.INSTANCE.isWorkPlaceRemindShow(stringConfigBeanMap)) {
                    RemindViewUpdateHelper.INSTANCE.updateRemindView(activity, getDataBinding().remindStub, SystemGrayConfigService.GraySettings.PageRemind.WORKPLACE_HOMEPAGE_REMIND, stringConfigBeanMap);
                } else {
                    RemindViewUpdateHelper.INSTANCE.updateRemindView(activity, getDataBinding().remindStub, "", stringConfigBeanMap);
                }
            }
        });
    }

    private void updateAdapterItem(Object item, int index, int targetIndex) {
        if (index >= 0) {
            mWorkStationAdapter.getData().set(index, item);
            mWorkStationAdapter.notifyItemChanged(index);
        } else {
            if (targetIndex >= 0 && item != null) {
                mWorkStationAdapter.getData().add(targetIndex, item);
                mWorkStationAdapter.notifyItemChanged(index);
            }
        }
    }

    private void removeAdapterItem(int index) {
        mWorkStationAdapter.getData().remove(index);
        mWorkStationAdapter.notifyItemRemoved(index);
    }

    /**
     * 注册 adapter
     */
    @SuppressLint("ClickableViewAccessibility")
    private void initStationAdapter() {
        mWorkStationAdapter = new MyMultiTypeAdapter();
        mWorkAdapter = new PluginAdapter(this);
        mWorkStationAdapter.register(
                ItemWorkStationProfile.class,
                R.layout.work_view_profile,
                (MyMultiTypeAdapter.ItemViewBinder<WorkViewProfileBinding, ItemWorkStationProfile>) (workViewProfileBinding, item, linkIndex)
                        -> {
                    workViewProfileBinding.setModel(item);
                    workViewProfileBinding.setCallback(this);
                }
        );
        mWorkStationAdapter.register(
                ItemWorkStationAnnouncement.class,
                R.layout.work_view_announcement_container,
                (MyMultiTypeAdapter.ItemViewBinder<WorkViewAnnouncementContainerBinding, ItemWorkStationAnnouncement>) (binding, item, index)
                        -> {
                    binding.container.setInAnimation(activity, R.anim.work_in);
                    binding.container.setOutAnimation(activity, R.anim.work_out);
                    binding.container.removeAllViews();
                    if (mAnnounceRunnable != null) {
                        handler.removeCallbacks(mAnnounceRunnable);
                    }
                    List<WorkStationAnnouncementBean> announcementBeans = item.getAnnouncements();
                    for (WorkStationAnnouncementBean announcementBean : announcementBeans) {
                        WorkViewAnnouncementBinding itemBinding = WorkViewAnnouncementBinding.inflate(getLayoutInflater(), binding.container, true);
                        itemBinding.setModel(announcementBean);
                        itemBinding.setCallback(this);
                    }
                    if (announcementBeans.size() > 1) {
                        AnnounceRunnable r = new AnnounceRunnable(binding, handler);
                        r.repeat();
                        mAnnounceRunnable = r;
                    }
                }
        );
        //常用应用
        mWorkStationAdapter.register(
                ItemWorkStationFavorite.class,
                R.layout.work_item_group,
                (MyMultiTypeAdapter.ItemViewBinder<WorkItemGroupBinding, ItemWorkStationFavorite>) (binding, item, index)
                        -> {
                    binding.setItem(item);
                    if (mFavoriteAdapter == null) {
                        mFavoriteAdapter = new PluginAdapter(this);
                    }
                    binding.recyclerGroup.setAdapter(mFavoriteAdapter);
                    mFavoriteAdapter.submitList(new ArrayList<>(item.getPlugins()));
                });
        //
        mWorkStationAdapter.register(
                ItemWorkStationPlugin.class,
                R.layout.work_item_group_all,
                (MyMultiTypeAdapter.ItemViewBinder<WorkItemGroupAllBinding, ItemWorkStationPlugin>) (binding, item, index)
                        -> {
                    binding.recyclerGroup.setAdapter(mWorkAdapter);
                    List<SystemPluginGroupBean> groupBeans = item.getPluginGroups();
                    initTabs(binding.tab, groupBeans, 1);
                });
        mWorkStationAdapter.register(ItemWorkStationWidget.class, R.layout.work_item_widget, (MyMultiTypeAdapter.ItemViewBinder<WorkItemWidgetBinding, ItemWorkStationWidget>) (binding, item, index)
                -> {
            binding.widgetView.bindLifecycleOwner(this);
            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) binding.widgetView.getLayoutParams();
            lp.topMargin = QMUIDisplayHelper.dp2px(activity, 10);
            lp.leftMargin = QMUIDisplayHelper.dp2px(activity, 10);
            lp.rightMargin = QMUIDisplayHelper.dp2px(activity, 10);
            binding.widgetView.setLayoutParams(lp);
            if (TextUtils.isEmpty(getViewModel().getDatasourceCode())) {
                if (!item.getWidgets().isEmpty()) {
                    WorkWidgetTab tab = item.getWidgets().get(0);
                    String code = tab.getCode();
                    String name = tab.getName();
                    if (code != null) {
                        binding.widgetView.loadData(code, name, 0);
                    }
                }
            } else {
                int tabIndex = CollectionsKt.indexOfFirst(item.getWidgets(), new Function1<WorkWidgetTab, Boolean>() {
                    @Override
                    public Boolean invoke(WorkWidgetTab workWidgetTab) {
                        return Objects.equals(workWidgetTab.getCode(), getViewModel().getDatasourceCode());
                    }
                });
                if (tabIndex > -1) {
                    WorkWidgetTab tab = item.getWidgets().get(tabIndex);
                    String code = tab.getCode();
                    String name = tab.getName();
                    if (code != null) {
                        binding.widgetView.loadData(code, name, tabIndex);
                    }
                }
            }
            RecyclerView recyclerView = binding.widgetView.findViewById(R.id.rv_widgets);
            recyclerView.setOnTouchListener(new View.OnTouchListener() {

                private float downX;
                private float downY;

                @Override
                public boolean onTouch(View view, MotionEvent motionEvent) {
                    if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                        downX = motionEvent.getX();
                        downY = motionEvent.getY();
                    } else if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
                        float upX = motionEvent.getX();
                        float upY = motionEvent.getY();
                        if (Math.abs(upX - downX) < 10 && Math.abs(upY - downY) < 10) {
                            String appLinkUrl = binding.widgetView.currentTabAppLinkUrl();
                            if (!TextUtils.isEmpty(appLinkUrl)) {
                                AppUtil.startUri(activity, appLinkUrl);
                            }
                        }
                    }
                    return false;
                }
            });
            TextView widgetTitleView = binding.widgetView.findViewById(R.id.tv_title);
            widgetTitleView.setOnClickListener(new DebouncedClickListener() {
                @Override
                public void onDebouncedClick(View v) {
                    WorkCommonReactionDialogKt.showSwitchTabDialog(getChildFragmentManager(), item.getWidgets(), binding.widgetView.getDatasourceIndex(), new OnCategorySelectListener() {
                        @Override
                        public void onSelect(@NonNull WorkWidgetCategoryBean tab, int index) {
                            // 点击切换数据源埋点
                            Map<String, Object> params = new HashMap<>();
                            params.put("type", Objects.equals(getViewModel().getDatasourceCode(), tab.getId()) ? 0 : 1);
                            PointUtils.pointV4("workbench-databoard-datachoice", params);

                            binding.widgetView.loadData(tab.getId(), tab.getTitle(), index);
                            getViewModel().setDatasourceCode(tab.getId());
                            getViewModel().recordWidgetDataSource(tab.getId());
                        }
                    }, new VisibleListener() {
                        @Override
                        public void onShow() {
                            binding.widgetView.titleArrowRotateUp();
                        }

                        @Override
                        public void onDismiss() {
                            binding.widgetView.titleArrowRotateDown();
                        }
                    });
                }
            });
            binding.widgetView.findViewById(R.id.iv_more).setOnClickListener(new DebouncedClickListener() {
                @Override
                public void onDebouncedClick(View v) {
                    super.onDebouncedClick(v);
                    // 点击切换数据源埋点
                    WorkCommonReactionDialogKt.showMoreOptionsDialog(getChildFragmentManager(), new OnMoreActionListener() {
                        @Override
                        public void onRefresh() {
                            binding.widgetView.refresh();
                            final int typeRefresh = 1;
                            Map<String, Object> params = new HashMap<>();
                            params.put("type", typeRefresh);
                            PointUtils.pointV4("workbench-databoard-menuclick", params);
                        }

                        @Override
                        public void onRemove() {
                            boolean removedOnce = ProcessHelper.getUserCompanyPreferences().getBoolean(Constants.WORKBENCH_REMOVED_ONCE_WIDGET, false);
                            getViewModel().removeWidget();
                            if (!removedOnce) {
                                mRemoveWidgetAlertDialog = new DialogUtils.Builder(getContext())
                                        .setTitle("数据组件已移除。你可在Boss Hi设置页面重新配置数据组件的展示")
                                        .setCanceledOnTouchOutside(true)
                                        .setPositive("知道了")
                                        .setPositiveListener(v -> mRemoveWidgetAlertDialog.dismiss())
                                        .build();
                                mRemoveWidgetAlertDialog.show();
                                ProcessHelper.getUserCompanyPreferences().putBoolean(Constants.WORKBENCH_REMOVED_ONCE_WIDGET, true);
                            }
                            List dataList = mWorkStationAdapter.getData();
                            int widgetIndex = CollectionsKt.indexOfFirst(dataList, o -> o instanceof ItemWorkStationWidget);
                            dataList.remove(widgetIndex);
                            mWorkStationAdapter.notifyItemRemoved(widgetIndex);
                            final int typeRemove = 2;
                            Map<String, Object> params = new HashMap<>();
                            params.put("type", typeRemove);
                            PointUtils.pointV4("workbench-databoard-menuclick", params);
                        }
                    });
                }
            });
            binding.widgetView.setListener(new WorkWidgetView.AppLinkListener() {
                @Override
                public void handler(@NonNull String appLink) {
                    AppUtil.startUri(activity, appLink);
                }
            });
        });
        mWorkStationAdapter.register(
                ItemWorkStationBanner.class,
                R.layout.work_banner,
                (MyMultiTypeAdapter.ItemViewBinder<WorkBannerBinding, ItemWorkStationBanner>) (binding, item, index)
                        -> {
                    getLifecycle().removeObserver(binding.banner);
                    binding.banner.addBannerLifecycleObserver(this);
                    BannerAdapter bannerAdapter = binding.banner.getAdapter();
                    if (bannerAdapter == null) {
                        binding.banner.setAdapter(new BannerAdapter<BannerItemBean, SimpleDraweeHolder>() {
                            @Override
                            public SimpleDraweeHolder onCreateHolder(ViewGroup parent, int viewType) {
                                SimpleDraweeView imageView = new SimpleDraweeView(parent.getContext());
                                //注意，必须设置为match_parent，这个是viewpager2强制要求的
                                ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(
                                        ViewGroup.LayoutParams.MATCH_PARENT,
                                        ViewGroup.LayoutParams.MATCH_PARENT);
                                imageView.setLayoutParams(params);
                                if (imageView.getHierarchy() != null) {
                                    imageView.getHierarchy().setActualImageScaleType(ScalingUtils.ScaleType.FIT_XY);
                                }
                                return new SimpleDraweeHolder(imageView);
                            }

                            @Override
                            public void onBindView(SimpleDraweeHolder holder, BannerItemBean bannerItemBean, int position, int size) {
                                holder.imageView.getHierarchy().setFailureImage(R.drawable.bg_corner_6_color_f5f5f7);
                                RoundingParams roundingParams = new RoundingParams();
                                roundingParams.setCornersRadius(QMUIDisplayHelper.dpToPx(6));
                                holder.imageView.getHierarchy().setRoundingParams(roundingParams);
                                holder.imageView.setImageURI(bannerItemBean.imageUrl);
                            }
                        });
                    }
                    binding.banner.setOnBannerListener(this);
                    Indicator indicator = binding.banner.getIndicator();
                    if (indicator == null) {
                        binding.banner.setIndicator(new BannerIndicator(activity));
                    }
                    long loopTime = item.getInterval();
                    binding.banner.setLoopTime(loopTime < 3 ? 3000 : loopTime * 1000);
                    binding.banner.setDatas(item.getBanners());
                }
        );
    }

    private TabLayout.Tab buildTab(TabLayout tabContainer, int i, String tabTitle) {
        TabLayout.Tab tab = tabContainer.newTab();
        tab.setCustomView(new TextView(activity));
        View view = tab.getCustomView();
        if (view instanceof TextView) {
            TextView textView = (TextView) view;
            textView.setPadding(TAB_SCROLL_OFFSET, 0, TAB_SCROLL_OFFSET, 0);
            textView.setText(tabTitle);
            textView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            textView.setTextSize(tab.isSelected() ? 17 : 13);
            textView.setGravity(Gravity.CENTER);
            textView.setTextColor(tab.isSelected()
                    ? getResources().getColor(R.color.color_1d2026) : getResources().getColor(R.color.color_A8ABB3));
        }
        return tab;
    }

    /**
     * 填充分组 tab
     *
     * @param tabContainer tab 父容器
     * @param groupBeans   分组数据
     */
    private void initTabs(TabLayout tabContainer, List<SystemPluginGroupBean> groupBeans, int index) {
        if (groupBeans == null) {
            return;
        }
        // 修改或添加 tab
        for (int i = 0; i < groupBeans.size(); i++) {
            SystemPluginGroupBean groupBean = groupBeans.get(i);
            TabLayout.Tab positionTab = tabContainer.getTabAt(i);
            if (positionTab != null && positionTab.getCustomView() instanceof TextView) {
                TextView textView = (TextView) positionTab.getCustomView();
                if (!textView.getText().equals(groupBean.pluginGroupName)) {
                    textView.setText(groupBean.pluginGroupName);
                }
            } else {
                tabContainer.addTab(buildTab(tabContainer, i, groupBean.pluginGroupName));
            }
        }
        // 删除多余 tab
        int deleteIndex = groupBeans.size();
        for (int i = deleteIndex; i < tabContainer.getTabCount(); i++) {
            TabLayout.Tab positionTab = tabContainer.getTabAt(i);
            if (positionTab != null) {
                tabContainer.removeTabAt(i);
            } else {
                break;
            }
        }

        tabContainer.clearOnTabSelectedListeners();
        tabContainer.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                View view = tab.getCustomView();
                if (view instanceof TextView) {
                    TextView textView = (TextView) view;
                    textView.setTextSize(17);
                    textView.setTextColor(getResources().getColor(R.color.color_1d2026));
                }
                if (selectBySync) {
                    selectBySync = false;
                    return;
                }
                int position = tab.getPosition();
                selectBySync = true;
                SystemPluginGroupBean groupBean = CollectionsKt.getOrNull(groupBeans, position);
                if (groupBean != null) {
                    if (mWorkAdapter != null) {
                        mWorkAdapter.submitList(new ArrayList<>(groupBean.pluginList));
                    }
                    new PointUtils.BuilderV4()
                            .name("work-function-group-click")
                            .params("type", groupBean.pluginGroupName)
                            .point();

                    // 总共有两个 tabLayout，同步另外一个 tab 的选中项
                    int syncIndex = 1 - index;
                    if (syncIndex >= 0) {
                        TabLayout tabLayout = mTabLayouts.get(syncIndex);
                        if (tabLayout != null) {
                            TabLayout.Tab targetTab = tabLayout.getTabAt(position);
                            if (targetTab != null) {
                                targetTab.select();
                            }
                        }
                    }
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                View view = tab.getCustomView();
                if (view instanceof TextView) {
                    TextView textView = (TextView) view;
                    textView.setTextSize(13);
                    textView.setTextColor(getResources().getColor(R.color.color_A8ABB3));
                }
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        mTabLayouts.put(index, tabContainer);
        for (int i = 0; i < mTabLayouts.size(); i++) {
            TabLayout tabLayout = mTabLayouts.valueAt(i);
            if (tabLayout != null) {
                int finalI = i;
                tabLayout.setOnScrollChangeListener((v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
                    if (scrollX < TAB_SCROLL_OFFSET) {
                        scrollX = TAB_SCROLL_OFFSET;
                        tabLayout.scrollTo(scrollX, 0);
                    }
                    TabLayout other = mTabLayouts.get(1 - finalI);
                    if (other != null) {
                        if (other.getScrollX() != scrollX) {
                            other.scrollTo(scrollX, scrollY);
                        }
                    }
                });
            }
        }
        int position = tabContainer.getSelectedTabPosition();
        if (position > -1 && position < groupBeans.size() && mWorkAdapter != null) {
            mWorkAdapter.submitList(new ArrayList<>(groupBeans.get(position).pluginList));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        getViewModel().refresh();
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.work_fragment;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }


    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void onSettingClick() {
        AppUtil.startUri(activity, WorkPageRouter.WORK_SETTING_ACTIVITY);
    }

    @Override
    public void onItemClick(SystemPluginBean systemPluginBean) {
        if (Constants.WORK_FAVORITE_ADD.equals(systemPluginBean.appId)) {
            AppUtil.startActivity(activity, new Intent(activity, FavoritePluginsActivity.class));
            return;
        }
        ServiceManager.getInstance().getWorkbenchService().openWorkbenchPlugin(((AppCompatActivity) getActivity()),
                systemPluginBean.appId,
                systemPluginBean.appName,
                Scene.WorkStation.INSTANCE.getCode());
    }

    private void toAttendanceFragment() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        AttendanceActivity.goAttendanceActivity(getActivity(), year, month, HiKernel.getHikernel().getAccount().getUserId());
    }

    @Override
    public void onGroupItemClick(SystemPluginGroupBean bean) {
        toAttendanceFragment();
    }

    @Override
    public void onAttendance() {
        new PointUtils.BuilderV4()
                .name("work-mobile-top-click")
                .params("type", "2")
                .point();
        toAttendanceFragment();
    }

    @Override
    public void onSearchClick() {
        AppUtil.startUri(activity, ChatPageRouter.CHAT_RECORD_ALL_SEARCH_ACTIVITY);
        new PointUtils.BuilderV4()
                .name("search-enter-click")
                .params("source", "work")
                .point();
    }

    @Override
    public void onItemLongClick(View view, SystemPluginBean item) {
        if (!getViewModel().isFavoriteOperationEnd()) {
            ToastUtils.ss(R.string.work_frequent_operation);
            return;
        }

        // 获取图标位置信息
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        int iconSize = view.getWidth(); // 假设图标是正方形
        int iconCenterX = location[0];
        int iconCenterY = location[1];

        IconFloatMenu floatMenu = new IconFloatMenu(activity);
        // 设置原始图标信息
        floatMenu.setOriginalIconInfo(iconCenterX, iconCenterY, iconSize);

        floatMenu.setCallback(new FloatMenuCallback() {
            @Override
            public void onMenuItemClick(MenuItem menuItem) {
                floatMenu.dismiss();
                switch (menuItem.getType()) {
                    case 0:
                        getViewModel().saveFavoritePlugins(item);
                        showProgressDialog(R.string.work_data_update_in_progress);
                        new PointUtils.BuilderV4()
                                .name("work-add-favorite-function")
                                .params("type", item.appName)
                                .point();
                        break;
                    case 1:
                        getViewModel().removeFavoritePlugins(item);
                        showProgressDialog(R.string.work_data_update_in_progress);
                        break;
                    case 2:
                        forwardToChat(item);
                        break;
                    case 3: // 添加到导航栏
                        operateForNavBar(item,true);
                        break;
                    case 4: // 从导航栏移除
                        operateForNavBar(item,false);
                        break;
                    default:
                        break;
                }
                // 菜单选择埋点
                PointUtils.BuilderV3 builderV3 = new PointUtils.BuilderV3();
                builderV3.name("work-press-click").params("icon_type", String.valueOf(menuItem.getType())).point();
            }
        });
        List<MenuItem> items = new ArrayList<>();
        workViewModelHelper.checkPluginAdded(item.appId, item.canOperate, new Function2<Boolean, Boolean, Unit>() {
            @Override
            public Unit invoke(Boolean showAdd, Boolean hasAdd) {
                // 添加导航栏选项 - 检查是否可操作
                if (showAdd) {
                    if (hasAdd) {
                        // 已添加到导航栏，显示"从导航栏移除"选项
                        items.add(new MenuItem(4, "从导航栏移除", getResources().getDrawable(R.drawable.ic_remove_from_nav_tab)));
                    } else {
                        // 未添加到导航栏，显示"添加到导航栏"选项
                        items.add(new MenuItem(3, "添加到导航栏", getResources().getDrawable(R.drawable.ic_add_to_nav_tab)));
                    }
                }
                // 非常用列表，可显示添加常用，否则显示取消常用
                if (!getViewModel().isFavoriteItem(item)) {
                    items.add(new MenuItem(0, getResources().getString(R.string.work_add_favorite_plugin), getResources().getDrawable(R.drawable.ic_add_to_favorite)));
                } else {
                    items.add(new MenuItem(1, getResources().getString(R.string.work_remove_favorite_plugin), getResources().getDrawable(R.drawable.ic_remove_from_favorite)));
                }
                // 转发到聊天
                if (SettingBuilder.getInstance().isJurisdictionVisible(JurisdictionUtils.JURISDICTION_WORKBENCH_FORWARD)) {
                    items.add(new MenuItem(2, getResources().getString(R.string.work_forward_to_chat), getResources().getDrawable(R.drawable.ic_share_to_chat)));
                }


                floatMenu.setOnDismissListener(new PopupWindow.OnDismissListener() {
                    @Override
                    public void onDismiss() {
                        canScroll = true;
                    }
                });

                // 使用优化的方法设置插件图标
                floatMenu.setIconUrl(item.appLogo);
                canScroll = false;
                floatMenu.items(items);
                floatMenu.show(); // 直接调用 show()，不需要传入点击位置
                return null;
            }
        });

    }


    /**
     * 从导航栏移除
     * @param item 插件项
     * @param add 是否添加 true 添加 false 删除
     */
    private void operateForNavBar(SystemPluginBean item,boolean add) {
        ServiceManager.getInstance().getNavTabService().operateFromWorkbench(item,add).observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if(aBoolean){
                    if(add){
                        ToastUtils.success(R.string.common_added);
                    }else{
                        ToastUtils.success(R.string.common_removed);
                    }
                }
            }
        });
        if(add){
            PointHelper.addToNav(item.appName);
        }else{
            PointHelper.removeFromNav(item.appName);
        }
    }

    /**
     * 应用转发到聊天
     *
     * @param item
     */
    private void forwardToChat(SystemPluginBean item) {
        ForwardMessageCardBean forwardMessageCardBean = new ForwardMessageCardBean();
        forwardMessageCardBean.cardInfo = ConvertorKt.toCard(item);
        MessageForExtensionCard extensionCard = MessageFactory.createExtensionCardMessage(forwardMessageCardBean.cardInfo);
        SelectConversationParams params = new SelectConversationParams();
        params.setSendMessageType(SendMessageContent.TYPE_FORWARD_EXTENSION_CARD)
                .<SelectConversationParams>setTitle(getResources().getString(R.string.select_contact))
                .<SelectConversationParams>setOrgVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setShowRightTitleVisible(SelectBaseParams.INVISIBLE)
                .<SelectConversationParams>setMultiSelect(false)
                .<SelectConversationParams>setSearchVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setOtherVisible(SelectBaseParams.VISIBLE);
        params.sendMessageContent.content = forwardMessageCardBean;
        params.sendMessageContent.msgShowContent = MessageUtils.messageContentSummary(extensionCard);
        Bundle bundleSelect = new Bundle();
        bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        AppUtil.startFragmentUriForResult(
                WorkFragment.this,
                SelectPageRouter.SELECT_CONVERSATION_ACTIVITY,
                RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_CHAT_FORWARD,
                bundleSelect
        );
    }

    @Override
    public void onCreateTeamClick() {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_DATA_STRING, "");
        Intent intent = new Intent(activity, TeamCreateActivity.class);
        intent.putExtras(bundle);
        AppUtil.startActivity(activity, intent);
    }

    @Override
    public void OnBannerClick(BannerItemBean bannerItemBean, int position) {
        if (TextUtils.isEmpty(bannerItemBean.jumpUrl)) {
            return;
        }
        getViewModel().jumpH5(bannerItemBean.jumpUrl, Constants.WEB_STYLE_SHARE_URL);
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("imageUrl", bannerItemBean.imageUrl);

        new PointUtils.BuilderV4()
                .name("work-nonlist-function-click")
                .params("type", "banner")
                .point();
    }

    @Override
    public void clickTodo(TodoBean todoBean) {
        getViewModel().jumpH5(todoBean.url, Constants.WEB_STYLE_HAS_NO_SHARE);
        new PointUtils.BuilderV4()
                .name("work-nonlist-function-click")
                .params("type", "todo")
                .params("source", "work")
                .point();
    }

    @Override
    public void clickAnnouncement(WorkStationAnnouncementBean announcementBean) {
        getViewModel().jumpH5(announcementBean.getUrl(), Constants.WEB_STYLE_HAS_NO_SHARE);
        new PointUtils.BuilderV4()
                .name("work-nonlist-function-click")
                .params("type", "notice")
                .point();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != Activity.RESULT_OK) {
            return;
        }

        switch (requestCode) {
            case RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_CHAT_FORWARD:
                handForwardResult(data);
                break;
            default:
                break;
        }
    }

    private void handForwardResult(Intent data) {
        if (data != null) {
            String chatId = data.getStringExtra(BundleConstants.BUNDLE_CHAT_ID);
            if (!TextUtils.isEmpty(chatId)) {
                int chatType = data.getIntExtra(BundleConstants.BUNDLE_CHAT_TYPE, MessageConstants.MSG_SINGLE_CHAT);
                getDataBinding().layoutForwardMessageSuccess.setChatId(chatId);
                getDataBinding().layoutForwardMessageSuccess.setChatType(chatType);
                handler.removeMessages(Constants.HEART_WHAT_FORWARD_SUCCESS);
                handler.sendEmptyMessageDelayed(Constants.HEART_WHAT_FORWARD_SUCCESS, Constants.FORWARD_SUCCESS_DISMISS_DELAY);
            }
        }
    }

    @Override
    public void onChatForwardSuccessClick(int chatType, String chatId) {
        handler.removeCallbacksAndMessages(null);
        getDataBinding().layoutForwardMessageSuccess.setChatId("");
        Bundle bundle = new Bundle();
        if (chatType == MessageConstants.MSG_GROUP_CHAT) {
            bundle.putString(BundleConstants.BUNDLE_CHAT_ID, chatId);
            AppUtil.startUri(getContext(), ChatPageRouter.GROUP_CHAT_ACTIVITY, bundle);
        } else {
            bundle.putString(BundleConstants.BUNDLE_USER_ID, chatId);
            AppUtil.startUri(getContext(), ChatPageRouter.SINGLE_CHAT_ACTIVITY, bundle);
        }
    }

    @Override
    public void onClickEntryTime() {
        new PointUtils.BuilderV4()
                .name("work-mobile-top-click")
                .params("type", "1")
                .point();
        ItemWorkStationPlugin pluginItem = getViewModel().getGroupPluginLiveData().getValue();
        Optional.ofNullable(pluginItem)
                .map(ItemWorkStationPlugin::getPluginGroups)
                .flatMap(pluginGroupBeans -> pluginGroupBeans.stream()
                        .flatMap(it -> it.pluginList.stream())
                        .collect(Collectors.toList())
                        .stream()
                        .filter(it -> TextUtils.equals(it.appId, WorkbenchPluginOpenHelper.getWORK_DAY_IN_SERVICE()))
                        .findFirst()).ifPresent(it -> {
                    ServiceManager.getInstance().getWorkbenchService().openWorkbenchPlugin(getActivity(),
                            it.appId,
                            it.appName,
                            Scene.WorkStation.INSTANCE.getCode());
                });
    }

    private static class WorkHandler extends Handler {

        private final WeakReference<WorkFragment> mWeakRef;

        WorkHandler(WorkFragment fragment) {
            this.mWeakRef = new WeakReference<>(fragment);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == Constants.HEART_WHAT_FORWARD_SUCCESS) {
                WorkFragment f = mWeakRef.get();
                if (f != null) {
                    f.getDataBinding().layoutForwardMessageSuccess.setChatId("");
                }
            }
        }
    }

    private static class AnnounceRunnable implements Runnable {

        private final WeakReference<WorkViewAnnouncementContainerBinding> mWeakRef;
        private final Handler mHandler;

        AnnounceRunnable(WorkViewAnnouncementContainerBinding binding, Handler handler) {
            this.mWeakRef = new WeakReference<>(binding);
            this.mHandler = handler;
        }

        void repeat() {
            this.mHandler.postDelayed(this, 5000L);
        }

        @Override
        public void run() {
            WorkViewAnnouncementContainerBinding binding = mWeakRef.get();
            if (binding != null) {
                binding.container.showNext();
                repeat();
            }
        }
    }
}