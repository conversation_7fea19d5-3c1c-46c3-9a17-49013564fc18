package com.twl.hi.work.api.request;

import com.twl.hi.work.api.response.VacationBalanceResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class VacationBalanceRequest extends BaseApiRequest<VacationBalanceResponse> {

    public VacationBalanceRequest(BaseApiRequestCallback<VacationBalanceResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_WORK_GETHOLIDAY;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
