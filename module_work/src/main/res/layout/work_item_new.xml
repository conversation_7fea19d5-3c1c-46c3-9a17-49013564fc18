<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <variable
            name="bean"
            type="com.twl.hi.foundation.api.response.bean.SystemPluginBean" />

        <variable
            name="callback"
            type="com.twl.hi.work.callback.WorkCallback" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:background="@color/app_white"
        android:layout_gravity="center_horizontal"
        android:onClick="@{()->callback.onItemClick(bean)}">


        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/sdv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:minWidth="28dp"
            android:minHeight="30dp"
            app:roundedCornerRadius="14dp"
            app:defaultImageHeight="@{40}"
            app:defaultImageWith="@{40}"
            app:imageUrlWrap="@{bean.appLogo}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:singleLine="true"
            android:text="@{bean.appName}"
            android:textColor="@color/color_0D0D1A"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/sdv" />

        <TextView
            android:id="@+id/tv_todo_count"
            android:layout_width="wrap_content"
            android:layout_height="14dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="9dp"
            android:layout_marginTop="9dp"
            android:background="@drawable/bg_circle_color_f35959_stroke_white"
            android:gravity="center"
            android:minWidth="14dp"
            android:minHeight="14dp"
            android:paddingLeft="3dp"
            android:paddingRight="3dp"
            android:text="@{bean.num > 99 ? @string/max_count : @string/int_to_string(bean.num)}"
            android:textColor="@color/app_white"
            android:textSize="10sp"
            android:visibility="@{(bean.num > 0) ? View.VISIBLE : View.INVISIBLE}"
            app:layout_constraintLeft_toRightOf="@+id/guideline"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="99+" />
        <!-- 水平引导线 -->
        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
