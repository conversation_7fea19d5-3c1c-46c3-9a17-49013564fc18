<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.twl.hi.work.model.DepartmentFilterBean" />

        <variable
            name="callback"
            type="com.twl.hi.work.callback.GroupAttendanceDepartmentFilterCallback" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@drawable/sel_item_user"
        android:clickable="true"
        android:focusable="true"
        android:onClick="@{()->callback.onDepartmentItemClick(bean)}"
        android:orientation="horizontal"
        android:paddingLeft="20dp">

        <TextView
            android:id="@+id/tv_dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginRight="46dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:text="@{bean.deptName}"
            android:textColor="#212121"
            android:textSize="17sp"
            tools:text="产品四组" />

        <com.google.android.material.internal.CheckableImageButton
            android:id="@+id/cb_dp"
            android:layout_width="46dp"
            android:layout_height="match_parent"
            android:layout_gravity="right|center_vertical"
            android:background="@null"
            android:button="@null"
            android:checked="@{bean.isSelected}"
            android:clickable="false"
            android:focusable="false"
            android:src="@drawable/work_selector_department"
            android:text="" />

    </FrameLayout>
</layout>