# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=2048m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#org.gradle.jvmargs=-Xmx2048m -Dkotlin.daemon.jvm.options=-Xmx1024m -Dfile.encoding=UTF-8
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.daemon=true
org.gradle.parallel=true
#android.enableD8=false
#android.enableR8=false
#android.enableR8.libraries=false
#android.enableD8.desugaring=false
#android.enableIncrementalDesugaring=false
#android.useDexArchive=true
#Tinker
#Whether ext.isJenkins packaging is related to the actual situation, both need to be true to trigger Tinker.
hotfixEnable=true 
android.useAndroidX=true
android.enableJetifier=true
android.injected.testOnly=false

# ??????
org.gradle.caching=true

# maven
GROUP=com.bzl.bosshi

apmsInstrumentationEnabled=false

#????????????100??2.36.1?versionCode?2360100
appVersionCode=3390000
appVersionName=3.39.0
appShowVersionName=3.39.0_dev