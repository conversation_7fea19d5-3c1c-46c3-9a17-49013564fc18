<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="textOne"
            type="String" />

        <variable
            name="textTow"
            type="String" />

        <variable
            name="resOne"
            type="Integer" />

        <variable
            name="resTow"
            type="Integer" />

        <variable
            name="callback"
            type="com.twl.hi.basic.callback.PopCommonTowCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_corner_20_top_2_color_white">


        <FrameLayout
            android:id="@+id/fl_photo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:onClick="@{()->callback.selectOne()}"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/fl_album"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/bg_corner_7_color_f6f6f6"
                android:scaleType="center"
                app:resId="@{resOne}" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="65dp"
                android:text="@{textOne}"
                android:textColor="@color/color_0D0D1A"
                android:textSize="12sp"
                tools:text="@string/photo" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_album"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:onClick="@{()->callback.selectTow()}"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@+id/fl_photo"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/bg_corner_7_color_f6f6f6"
                android:scaleType="center"
                app:resId="@{resTow}" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="65dp"
                android:text="@{textTow}"
                android:textColor="@color/color_0D0D1A"
                android:textSize="12sp"
                tools:text="@string/album" />
        </FrameLayout>

        <TextView
            android:id="@+id/txt_cancel"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="30dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/bg_corner_7_color_f2f2f5"
            android:gravity="center"
            android:onClick="@{()->callback.dismissPop()}"
            android:text="@string/cancel"
            android:textColor="@color/color_0D0D1A"
            android:textSize="17sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fl_photo" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
