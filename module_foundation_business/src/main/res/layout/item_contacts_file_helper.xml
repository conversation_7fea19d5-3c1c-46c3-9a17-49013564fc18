<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="click"
            type="android.view.View.OnClickListener" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/vg_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/sel_item_user"
        android:onClick="@{click}"
        android:paddingLeft="20dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:src="@drawable/organize_ic_file_assist" />

        <TextView
            android:id="@+id/tv_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="12dp"
            android:text="@string/file_helper"
            android:textColor="#ff212121"
            android:textSize="17sp" />
    </LinearLayout>
</layout>
