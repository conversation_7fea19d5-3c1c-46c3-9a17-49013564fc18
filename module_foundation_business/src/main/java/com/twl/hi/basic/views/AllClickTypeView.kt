package com.twl.hi.basic.views

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import kotlin.math.abs


/**
 * <AUTHOR>
 * @date 2022/11/14
 * description:可监听单击、双击、长按事件的view
 */
class AllClickTypeView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        const val LONG_CLICK_OFFSET = 400L
    }

    private var firstClickTime: Long = 0
    private var secondClickTime: Long = 0
    private var lastDoubleClickTime: Long = 0
    private var stillTime: Long = 0
    private var isUp = false
    private var isDoubleClick = false

    private var downX = 0f
    private var downY = 0f

    /**
     * 是否滑动过
     */
    private var isMoved = false

    private val mRunnable = {
        //避免一直点击触发双击后依然触发长按事件
        if (System.currentTimeMillis() - lastDoubleClickTime >= LONG_CLICK_OFFSET) {
            if (!isUp) {
                onAllTypeClickListener?.onLongClickListener()
                firstClickTime = 0
                secondClickTime = 0
                isDoubleClick = false
            } else {
                if (!isDoubleClick && !isMoved) {
                    onAllTypeClickListener?.onClickListener()
                }
                isDoubleClick = false
                firstClickTime = 0
                secondClickTime = 0
            }
        }
    }

    var onAllTypeClickListener: OnAllTypeClickListener? = null

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                isUp = false
                isPressed = true
                isMoved = false
                downX = event.x
                downY = event.y
                if (firstClickTime == 0L && secondClickTime == 0L) { //第一次点击
                    //避免一直点击触发双击后依然触发长按事件
                    if (System.currentTimeMillis() - lastDoubleClickTime > LONG_CLICK_OFFSET) {
                        firstClickTime = System.currentTimeMillis()
                        handler.postDelayed(mRunnable, LONG_CLICK_OFFSET)
                    }
                } else {
                    secondClickTime = System.currentTimeMillis()
                    stillTime = secondClickTime - firstClickTime
                    if (stillTime < LONG_CLICK_OFFSET) { //两次点击小于时间间隔
                        lastDoubleClickTime = System.currentTimeMillis()
                        onAllTypeClickListener?.onDoubleClickListener()
                        isDoubleClick = true
                        firstClickTime = 0
                        secondClickTime = 0
                    }
                }
            }
            MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_UP -> {
                isPressed = false
                isUp = true
                isMoved = abs(event.x - downX) > 1 || abs(event.y - downY) > 1
            }
        }
        return true
    }


    interface OnAllTypeClickListener {

        fun onClickListener()

        fun onDoubleClickListener()

        fun onLongClickListener()
    }

}