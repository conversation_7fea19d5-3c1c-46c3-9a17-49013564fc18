package com.twl.hi.basic.bindadapter;

import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.text.*;
import android.text.style.ForegroundColorSpan;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.databinding.BindingAdapter;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.viewpager.widget.ViewPager;
import androidx.viewpager2.widget.ViewPager2;
import com.bumptech.glide.Glide;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.backends.pipeline.PipelineDraweeControllerBuilder;
import com.facebook.drawee.controller.BaseControllerListener;
import com.facebook.drawee.controller.ControllerListener;
import com.facebook.drawee.generic.GenericDraweeHierarchy;
import com.facebook.drawee.generic.GenericDraweeHierarchyBuilder;
import com.facebook.drawee.generic.RoundingParams;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.image.ImageInfo;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.PageConstantsKt;
import com.twl.hi.basic.R;
import com.twl.hi.basic.api.response.bean.GroupMatchBean;
import com.twl.hi.basic.feature.attendance.AttendanceHelper;
import com.twl.hi.basic.helpers.ConversationAvatarPointerHelperKt;
import com.twl.hi.basic.model.SearchContact;
import com.twl.hi.basic.model.SearchGroup;
import com.twl.hi.basic.util.ThemeUtils;
import com.twl.hi.basic.viewmodel.FileDownLoadViewModel;
import com.twl.hi.basic.views.*;
import com.twl.hi.basic.views.expand.ExpandableStateRecord;
import com.twl.hi.basic.views.expand.ExpandableTextView;
import com.twl.hi.basic.views.multitext.ChatRecordSearchTextView;
import com.twl.hi.basic.views.multitext.MLinkEmotionTextView;
import com.twl.hi.basic.views.multitext.MLinkTextView;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.export.organization.router.OrganizationPageRouter;
import com.twl.hi.foundation.api.response.bean.ResourceBean;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.*;
import com.twl.hi.foundation.model.message.ChatMessage;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.utils.ContactUtils;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.image.GlideApp;
import com.twl.utils.StringUtils;
import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import hi.kernel.HiKernel;
import kotlin.text.StringsKt;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.ext.KotlinExtKt;
import lib.twl.common.util.*;
import lib.twl.common.util.image.ImageUtils;
import lib.twl.common.views.NoRepeatClickListener;
import lib.twl.common.views.adapter.entity.MultiItemEntity;
import lib.twl.common.views.banner.BannerUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static hi.kernel.Constants.INVISIBLE_SPLIT_START;

public class BasicBindingAdapters {
    public static final String TAG = "BasicBindingAdapters";

    @BindingAdapter("avatarConversation")
    public static void avatarConversation(View view, ConversationProfile oldProfile, ConversationProfile newProfile) {
        if (newProfile == null) {
            BasicBindingAdapters.setAvatar(view, null, "");
        } else if (!Objects.equals(oldProfile, newProfile)) {
            BasicBindingAdapters.setAvatar(view, newProfile.getAvatar(), ContactUtils.getDisplayAvatarStr(newProfile.getRealName()), 0);
        }
    }

    /**
     * 设置F1会话Fragment的会话列表的中 群聊的tag
     */
    @BindingAdapter("groupChatTag")
    public static void setMainConversationGroupTag(TextView textView, ConversationProfile profile) {
        if (profile == null) {
            textView.setVisibility(View.GONE);
            return;
        }
        if (TextUtils.equals(profile.getDepartment(), Constants.All_STAFF_GROUP)) {
            textView.setText(com.twl.hi.basic.R.string.all_member_group);
            textView.setVisibility(View.VISIBLE);
        } else if (!TextUtils.isEmpty(profile.getDepartment()) && !TextUtils.equals(profile.getDepartment(), Constants.NOT_DEPARTMENT_GROUP)) {
            textView.setText(com.twl.hi.basic.R.string.department_group);
            textView.setVisibility(View.VISIBLE);
        } else if (profile.isPublic()) {
            textView.setText(com.twl.hi.basic.R.string.group_public);
            textView.setVisibility(View.VISIBLE);
        } else {
            textView.setVisibility(View.GONE);
        }
    }

    @BindingAdapter("visibleGone")
    public static void showHide(View view, boolean show) {
        view.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    @BindingAdapter("visibleTurnGone")
    public static void showTurnHide(View view, boolean gone) {
        view.setVisibility(gone ? View.GONE : View.VISIBLE);
    }

    @BindingAdapter("visibleGone")
    public static void showHide(View view, String content) {
        view.setVisibility(TextUtils.isEmpty(content) ? View.GONE : View.VISIBLE);
    }

    @BindingAdapter("visibleInVisible")
    public static void showInvisible(View view, boolean show) {
        view.setVisibility(show ? View.VISIBLE : View.INVISIBLE);
    }

    @BindingAdapter("viewEnable")
    public static void setViewEnable(View view, boolean enable) {
        view.setEnabled(enable);
    }

    @BindingAdapter("showIfPresent")
    public static void showIfPresent(TextView textView, CharSequence content) {
        if (!TextUtils.isEmpty(content)) {
            textView.setText(content);
            textView.setVisibility(View.VISIBLE);
        } else {
            textView.setVisibility(View.GONE);
        }
    }

    @BindingAdapter("showIfPresent")
    public static void showIfPresent(TextView textView, int contentId) {
        if (contentId != 0) {
            textView.setText(contentId);
            textView.setVisibility(View.VISIBLE);
        } else {
            textView.setVisibility(View.GONE);
        }
    }

    @BindingAdapter("showIfPresent")
    public static void showIfPresent(TextView textView, long content) {
        if (content != 0) {
            textView.setText(String.valueOf(content));
            textView.setVisibility(View.VISIBLE);
        } else {
            textView.setVisibility(View.INVISIBLE);
        }
    }

    @BindingAdapter("scaledTextSize")
    public static void scaledTextSize(TextView textView, float size) {
        float scale = ServiceManager.getInstance().getSettingService().getFontScale();
        textView.setTextSize(size * scale);
    }

    @BindingAdapter("textColorId")
    public static void textColorId(TextView textView, int id) {
        textView.setTextColor(textView.getContext().getColor(id));
    }

    @BindingAdapter("imageUrl")
    public static void setImage(SimpleDraweeView simpleDraweeView, String url) {
        try {
            TLog.info("setImage", "Loading image via Fresco: " + url);
            String origin = (String) simpleDraweeView.getTag();
            if (TextUtils.isEmpty(origin) || !origin.equals(url)) {
                simpleDraweeView.setImageURI(url);
                simpleDraweeView.setTag(url);
            } else if (!TextUtils.isEmpty(url)) {
                simpleDraweeView.setImageURI(url);
            }
        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
        }
    }

    /**
     * 设置头像挂件
     *
     * @param simpleDraweeView
     * @param url              挂件地址
     */
    @BindingAdapter(value = {"pendantUrl", "useFresco"}, requireAll = false)
    public static void setPendant(SimpleDraweeView simpleDraweeView, String url, boolean useFresco) {
        if (TextUtils.isEmpty(url)) {
            simpleDraweeView.setVisibility(View.GONE);
            return;
        } else {
            simpleDraweeView.setVisibility(View.VISIBLE);
        }
        try {
            ViewGroup parent = (ViewGroup) simpleDraweeView.getParent();
            simpleDraweeView.post(new Runnable() {
                @Override
                public void run() {
                    int parentWidth = parent.getMeasuredWidth();
                    int pendantSize = (int) (parentWidth * 114 / 90f);
                    ViewGroup.LayoutParams lp = simpleDraweeView.getLayoutParams();
                    if (lp == null) {
                        lp = new ViewGroup.LayoutParams(pendantSize, pendantSize);
                    } else {
                        lp.width = pendantSize;
                        lp.height = pendantSize;
                    }
                    simpleDraweeView.setLayoutParams(lp);
                }
            });
            if (useFresco) {
                loadImgByFresco(simpleDraweeView, url);
            } else {
                GlideApp.with(simpleDraweeView)
                        .load(url)
                        .into(simpleDraweeView);
            }
        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
        }
    }

    @BindingAdapter("urlLinkText")
    public static void setUrlLinkText(MLinkTextView textView, String content) {
        if (!TextUtils.isEmpty(content)) {
            textView.parseUrlText(content);
        }
    }

    @BindingAdapter("applyContact")
    public static void setApplyContact(View view, Contact contact) {
        if (contact == null) {
            return;
        }
        setAvatar(view, TextUtils.isEmpty(contact.getTinyAvatar()) ? contact.getAvatar() : contact.getTinyAvatar(), contact.getStrAvatar());
    }

    /**
     * 标准尺寸
     * 图片：文字 = 48：16
     *
     * @param view
     * @param url
     * @param name
     */
    public static void setAvatar(View view, String url, String name) {
        setAvatar(view, url, name, 0);
    }

    @BindingAdapter({"fileCategory", "fileName"})
    public static void setAttachmentFileIcon(SimpleDraweeView simpleDraweeView, int category, String name) {
        if (category >= 0) {
            switch (category) {
                case ResourceBean.CATEGORY_PIC:
                    simpleDraweeView.setImageResource(R.mipmap.icon_pic);
                    break;
                case ResourceBean.CATEGORY_VIDEO:
                    simpleDraweeView.setImageResource(R.mipmap.icon_video);
                    break;
                default:
                    setFileIcon(simpleDraweeView, name);
            }
        } else {
            simpleDraweeView.setImageResource(R.mipmap.icon_folder);
        }
    }

    @BindingAdapter("fileType")
    public static void setFileIcon(SimpleDraweeView simpleDraweeView, String fileName) {
        if (!TextUtils.isEmpty(fileName)) {
            String end = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            switch (end) {
                case "zip":
                case "rar":
                case "7z":
                case "apk":
                    simpleDraweeView.setImageResource(R.mipmap.icon_zip);
                    break;
                case "m4a":
                case "mp3":
                case "mid":
                case "xmf":
                case "ogg":
                case "wav":
                    simpleDraweeView.setImageResource(R.mipmap.icon_voice);
                    break;
                case "doc":
                case "docx":
                    simpleDraweeView.setImageResource(R.mipmap.icon_word);
                    break;
                case "xls":
                case "xlsx":
                case "csv":
                    simpleDraweeView.setImageResource(R.mipmap.icon_excel);
                    break;
                case "txt":
                    simpleDraweeView.setImageResource(R.mipmap.icon_txt);
                    break;
                case "ppt":
                case "pptx":
                    simpleDraweeView.setImageResource(R.mipmap.icon_ppt);
                    break;
                case "pdf":
                    simpleDraweeView.setImageResource(R.mipmap.icon_pdf);
                    break;
                case "png":
                case "jpg":
                case "jpeg":
                case "gif":
                    simpleDraweeView.setImageResource(R.mipmap.icon_pic);
                    break;
                case "mp4":
                case "3gp":
                case "mov":
                case "avi":
                    simpleDraweeView.setImageResource(R.mipmap.icon_video);
                    break;
                default:
                    simpleDraweeView.setImageResource(R.mipmap.icon_document);
                    break;
            }
        }
    }

    @BindingAdapter("taskTime")
    public static void setTaskTime(TextView textView, long time) {
        if (time > 0) {
            textView.setText(LDate.yMdHmFormatC.format(time));
        } else {
            textView.setVisibility(View.GONE);
        }
    }

    @BindingAdapter({"searchMemberContent", "searchContent"})
    public static void setSearchMemberContent(ChatRecordSearchTextView textView, String content, String searchContent) {
        if (content != null && StringsKt.contains(content, INVISIBLE_SPLIT_START, true)) {
            // 服务端下发的内容包含高亮
            textView.setServerContent(content);
        } else {
            textView.setAllContentText(content, searchContent);
        }
    }

    @BindingAdapter("taskStatus")
    public static void setTaskStatus(CommonTextView textView, int status) {
        //状态：1-待认领，2-进行中，3-已完成
        switch (status) {
            case 2:
                textView.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_6974E8));
                textView.setDefaultBgColor(BaseApplication.getApplication().getResources().getColor(R.color.color_E7ECFD));
                textView.setText(R.string.task_on);
                break;
            case 3:
                textView.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_5CC37A));
                textView.setDefaultBgColor(BaseApplication.getApplication().getResources().getColor(R.color.color_E3F8EC));
                textView.setText(R.string.task_finish);
                break;
            default:
                textView.setTextColor(BaseApplication.getApplication().getResources().getColor(R.color.color_EA4848));
                textView.setDefaultBgColor(BaseApplication.getApplication().getResources().getColor(R.color.color_FDE7E6));
                textView.setText(R.string.task_get);
                break;
        }
    }

    @BindingAdapter("avatar")
    public static void setAvatar(View view, String userId) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
        if (contact != null) {
            setAvatar(view, TextUtils.isEmpty(contact.getTinyAvatar()) ? contact.getAvatar() : contact.getTinyAvatar(), contact.getStrAvatar());
        }
    }

    @BindingAdapter("avatarBig")
    public static void setAvatarBig(View view, String userId) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
        if (contact != null) {
            setAvatar(view, TextUtils.isEmpty(contact.getAvatar()) ? contact.getTinyAvatar() : contact.getAvatar(), contact.getStrAvatar());
        }
    }

    @BindingAdapter("userName")
    public static void setContactName(TextView textView, String userId) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
        setContactUserName(textView, contact);
    }

    @BindingAdapter("contactUserName")
    public static void setContactUserName(TextView textView, Contact contact) {
        if (contact != null) {
            textView.setText(ContactUtils.getDisplayNameByContact(contact));
        } else {
            textView.setText("");
        }
    }

    @BindingAdapter("avatarMultiItemEntity")
    public static void avatarMultiItemEntity(View view, MultiItemEntity item) {
        if (item instanceof SearchGroup) {
            Group group = ((SearchGroup) item).getGroup();
            if (group == null) {
                return;
            }

            setAvatar(view, TextUtils.isEmpty(group.getTinyAvatar()) ? group.getAvatar() : group.getTinyAvatar(), "", 0);
        } else if (item instanceof SearchContact) {
            Contact contact = ((SearchContact) item).getContact();
            if (contact == null) {
                return;
            }
            setContactAvatarOnly(view, contact);
            setAvatar(view, TextUtils.isEmpty(contact.getTinyAvatar()) ? contact.getAvatar() : contact.getTinyAvatar(), contact.getStrAvatar(), 0);
        }
    }

    @BindingAdapter("avatarContactOnly")
    public static void setContactAvatarOnly(View view, Contact contact) {
        if (contact == null) {
            return;
        }
        setAvatar(view, TextUtils.isEmpty(contact.getTinyAvatar()) ? contact.getAvatar() : contact.getTinyAvatar(), contact.getStrAvatar());
    }

    @BindingAdapter("avatarGroup")
    public static void avatarGroup(SimpleDraweeView sdvAvatar, Group group) {
        RoundingParams roundingParams = new RoundingParams();
        roundingParams.setRoundAsCircle(true);
        GenericDraweeHierarchy genericDraweeHierarchy = GenericDraweeHierarchyBuilder.newInstance(sdvAvatar.getContext().getResources())
                .setRoundingParams(roundingParams).build();
        sdvAvatar.setHierarchy(genericDraweeHierarchy);
    }

    // 服务端搜索结果高亮
    @BindingAdapter("searchRecordServerContent")
    public static void setSearchRecordServerContent(ChatRecordSearchTextView textView, String content) {
        textView.setServerContent(content);
    }

    /**
     * 居中展示高亮部分
     */
    @BindingAdapter("centerAlignHighlight")
    public static void centerAlignHighlight(ChatRecordSearchTextView textView, CharSequence searchText) {
        textView.post(new Runnable() {
            @Override
            public void run() {
                int lines = 1;
                int len = searchText.length();
                // 假定每行可以显示 50 个字符，实际上达不到
                int countOfLine = 50;
                int remainContentCount = countOfLine * lines;
                int sourceHighlightStart = searchText.toString().indexOf(INVISIBLE_SPLIT_START);
                int sourceHighlightEnd = searchText.toString().indexOf(INVISIBLE_SPLIT_START);
                // 判断内容是否超长，超长则做快速截断处理，将内容截断到 countOfLine * lines 个字符
                CharSequence fixedContent;
                if (searchText.length() > remainContentCount) {
                    int sourceEndOffset = searchText.length() - sourceHighlightEnd;
                    int highlightCount = sourceHighlightEnd - sourceHighlightStart;
                    int offsetCount = (remainContentCount - highlightCount) / 2;
                    // 如果超长判断高亮的位置
                    if (sourceHighlightStart < offsetCount) {
                        // 高亮在开头
                        fixedContent = searchText.subSequence(0, remainContentCount);
                    } else if (sourceEndOffset < offsetCount) {
                        // 高亮在结尾
                        fixedContent = searchText.subSequence(len - remainContentCount, len);
                    } else {
                        // 高亮在中间
                        fixedContent = searchText.subSequence(sourceHighlightStart - offsetCount, sourceHighlightEnd + offsetCount);
                    }
                } else {
                    fixedContent = searchText;
                }

                final String ellipse = "...";
                boolean headFixed = false;
                boolean tailFixed = false;
                int widgetWidth = textView.getWidth();
                Paint paint = textView.getPaint();
                SpannableStringBuilder sb = new SpannableStringBuilder(fixedContent);
                while (paint.measureText(sb, 0, sb.length()) > widgetWidth) {
                    int highlightStart = StringsKt.indexOf(sb, INVISIBLE_SPLIT_START, 0, false);
                    int highlightEnd = StringsKt.indexOf(sb, Constants.INVISIBLE_SPLIT_END, 0, false);
                    int length = sb.length();
                    int endOffset = length - highlightEnd;
                    if (highlightStart > endOffset) {
                        headFixed = true;
                        sb.delete(0, 1);
                    } else {
                        tailFixed = true;
                        sb.delete(length - 1, length);
                    }
                }
                if (headFixed) {
                    sb.replace(0, 3, ellipse);
                }
                if (tailFixed) {
                    sb.replace(sb.length() - 3, sb.length(), ellipse);
                }
                textView.setServerContent(sb.toString());
            }
        });
    }

    // 客户端搜索结果高亮
    @BindingAdapter({"searchRecordContent", "searchContent"})
    public static void setSearchRecordContent(ChatRecordSearchTextView textView, String content, String searchContent) {
        textView.setContentText(content, searchContent);
    }

    @BindingAdapter(value = {"avatarContact", "groupRobot", "avatarLongClick", "message", "pageFrom", "showPendant"}, requireAll = false)
    public static void setContactAvatar(View view, Contact contact, GroupRobotEntity robot, View.OnLongClickListener onLongClickListener, ChatMessage message, String pageFrom, boolean showPendant) {
        if (robot != null) {
            setAvatar(view, robot.getAvatar(), robot.getName());
            view.setOnClickListener(v -> {
                if (robot.getType() == Constants.ROBOT_TYPE_SHIP) {
                    return;
                }
                ChatPageRouter.jumpToRobotProfilePage(view.getContext(), robot.getRobotId());
                new PointUtils.BuilderV4()
                        .name("chatbot-card-expose")
                        .params("source", 2)
                        .params("robot_id", robot.getRobotId())
                        .point();
            });
            if (onLongClickListener != null) {
                view.setOnLongClickListener(onLongClickListener);
            }
            return;
        }

        if (contact == null) {
            setAvatar(view, "", "");
            return;
        }
        SimpleDraweeView pendantView = view.findViewById(R.id.iv_pendant);
        if (showPendant && pendantView != null) {
            setPendant(pendantView, contact.getAvatarDecoration(), false);
        }
        setAvatar(view, TextUtils.isEmpty(contact.getTinyAvatar()) ? contact.getAvatar() : contact.getTinyAvatar(), contact.getStrAvatar());
        view.setOnClickListener(v -> {
            Bundle bundle = new Bundle();
            if (null != message) {
                ConversationAvatarPointerHelperKt.conversionAvatarClickedPoint(contact, message);
                bundle.putString(PageConstantsKt.PAGE_FROM, PageConstantsKt.CONVERSATION_PAGE_AVATAR);
            }
            if (contact.getUserType() == MessageConstants.MSG_CONTACT_TYPE_SYS) {
                return;
            }
            if (!TextUtils.isEmpty(pageFrom)) {
                bundle.putString(PageConstantsKt.PAGE_FROM, pageFrom);
            }
            bundle.putString(BundleConstants.BUNDLE_DATA_LONG, contact.getUserId());
            AppUtil.getDefaultUriRequest(view.getContext(), OrganizationPageRouter.USER_INFO_ACTIVITY, bundle, ActivityAnimType.DEFAULT).setIntentFlags(Intent.FLAG_ACTIVITY_NEW_TASK).start();
        });

        if (onLongClickListener != null) {
            view.setOnLongClickListener(onLongClickListener);
        }
    }

    @BindingAdapter({"userId", "showDepart", "avatarClick"})
    public static void setCommonItemUser(View view, String userId, boolean showDepart,
                                         boolean avatarClick) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
        setCommonUser(view, contact, showDepart, avatarClick);

    }

    private static void setCommonUser(View view, Contact contact, boolean showDepart,
                                      boolean avatarClick) {
        if (contact == null) {
            return;
        }
        TextView tvName = view.findViewById(R.id.tv_name);
        setUserSelectContent(tvName, contact, !showDepart);
        View vgAvatar = view.findViewById(R.id.vg_avatar);
        setAvatar(vgAvatar, TextUtils.isEmpty(contact.getTinyAvatar()) ? contact.getAvatar() : contact.getTinyAvatar(), contact.getStrAvatar());
        if (avatarClick) {
            vgAvatar.setOnClickListener(v -> {
                if (contact.getUserType() == MessageConstants.MSG_CONTACT_TYPE_SYS) {
                    return;
                }
                Bundle bundle = new Bundle();
                bundle.putString(BundleConstants.BUNDLE_DATA_LONG, contact.getUserId());
                AppUtil.startUri(view.getContext(), OrganizationPageRouter.USER_INFO_ACTIVITY, bundle);
            });
        }
    }

    @BindingAdapter({"userSelectContent", "hideDept"})
    public static void setUserSelectContent(TextView textView, Contact contact, boolean hideDept) {
        setUserSelectContent(textView, contact, hideDept, "");
    }

    @BindingAdapter({"userSelectContent", "hideDept", "searchKey"})
    public static void setUserSelectContent(TextView textView, Contact contact, boolean hideDept, String searchKey) {
        if (contact == null) {
            return;
        }
        String showName = ContactUtils.getDisplayNameByContact(contact);
        if (TextUtils.isEmpty(showName)) {
            return;
        }
        SpannableStringBuilder style = new SpannableStringBuilder(showName);
        String deptNames = contact.getShowDeptNames();
        if (!hideDept && !TextUtils.isEmpty(deptNames)) {
            style.append(" | ");
            style.append(deptNames);
            style.setSpan(new ForegroundColorSpan(textView.getContext().getResources().getColor(R.color.color_9B9B9B)), showName.length(), style.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
        }
        textView.setText(style);

        if (textView instanceof ChatRecordSearchTextView && !TextUtils.isEmpty(searchKey)) {
            // 需要做本地搜索高亮处理
            ChatRecordSearchTextView searchTextView = (ChatRecordSearchTextView) textView;
            searchTextView.setAllContentText(style.toString(), searchKey);
        }
    }

    @BindingAdapter({"userSelectContent", "hideDept"})
    public static void setUserSelectContent(TextView textView, String contactId, boolean hideDept) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(contactId);
        setUserSelectContent(textView, contact, hideDept);
    }

    @BindingAdapter({"chatId", "chatType", "lifeCycleOwner"})
    public static void conversationGroupType(TextView textView, String chatId, int type, LifecycleOwner lifecycleOwner) {
        setConversationGroupType(textView, ServiceManager.getInstance().getConversationService().queryConversationProfile(chatId, type), lifecycleOwner);
    }

    /**
     * 注册群聊会话的标签监听
     */
    public static void setConversationGroupType(TextView textView, LiveData<ConversationProfile> profileLiveData, LifecycleOwner lifecycleOwner) {
        // bugfix https://bugly.qq.com/v2/crash-reporting/crashes/78781211ab/3582956?pid=1
        if (profileLiveData == null) {
            return;
        }

        profileLiveData.observe(lifecycleOwner, groupConversationTag -> {
            if (groupConversationTag == null) {
                textView.setVisibility(View.GONE);
                return;
            }
            if (TextUtils.equals(groupConversationTag.getDepartment(), Constants.All_STAFF_GROUP)) {
                textView.setText(R.string.all_member_group);
                textView.setVisibility(View.VISIBLE);
            } else if (!TextUtils.isEmpty(groupConversationTag.getDepartment()) && !TextUtils.equals(groupConversationTag.getDepartment(), Constants.NOT_DEPARTMENT_GROUP)) {
                textView.setText(R.string.department_group);
                textView.setVisibility(View.VISIBLE);
            } else if (groupConversationTag.isPublic()) {
                textView.setText(R.string.group_public);
                textView.setVisibility(View.VISIBLE);
            } else {
                textView.setVisibility(View.GONE);
            }
        });
    }

    /**
     * 全局搜索中的群信息 设置群聊tag
     */
    @BindingAdapter("allSearchGroupTag")
    public static void allSearchGroupTag(TextView textView, String chatId) {
        ExecutorFactory.execWorkTask(() -> {
            Group group = ServiceManager.getInstance().getGroupService().getOnlyGroupById(chatId);
            if (group != null) {
                textView.post(() -> {
                    setGroupTag(textView, group);
                });
            } else {
                //本地没有说明必定是公开群，为展示tag 临时拼接数据
                Group tempGroup = new Group();
                tempGroup.setDeptId("");
                tempGroup.setIsPublic(1);
                textView.post(() -> {
                    setGroupTag(textView, tempGroup);
                });
            }

        });
    }

    /**
     * 群聊设置tag
     */
    @BindingAdapter("groupTag")
    public static void setGroupTag(TextView textView, Group group) {
        if (group == null) {
            textView.setVisibility(View.GONE);
            return;
        }
        if (TextUtils.equals(group.getDeptId(), Constants.All_STAFF_GROUP)) {
            textView.setText(R.string.all_member_group);
            textView.setVisibility(View.VISIBLE);
        } else if (!TextUtils.isEmpty(group.getDeptId()) && !TextUtils.equals(group.getDeptId(), Constants.NOT_DEPARTMENT_GROUP)) {
            textView.setText(R.string.department_group);
            textView.setVisibility(View.VISIBLE);
        } else if (group.getIsPublic() == 1) {
            textView.setText(R.string.group_public);
            textView.setVisibility(View.VISIBLE);
        } else {
            textView.setVisibility(View.GONE);
        }
    }

    /**
     * 群聊设置tag
     */
    @BindingAdapter("groupTag")
    public static void setGroupTag(TextView textView, GroupItemBean group) {
        if (group == null) {
            textView.setVisibility(View.GONE);
            return;
        }
        if (TextUtils.equals(group.getDeptId(), Constants.All_STAFF_GROUP)) {
            textView.setText(R.string.all_member_group);
            textView.setVisibility(View.VISIBLE);
        } else if (!TextUtils.isEmpty(group.getDeptId()) && !TextUtils.equals(group.getDeptId(), Constants.NOT_DEPARTMENT_GROUP)) {
            textView.setText(R.string.department_group);
            textView.setVisibility(View.VISIBLE);
        } else if (group.getIsPublic() == 1) {
            textView.setText(R.string.group_public);
            textView.setVisibility(View.VISIBLE);
        } else {
            textView.setVisibility(View.GONE);
        }
    }

    @BindingAdapter("clockStatusTag")
    public static void setClockStatusTag(TextView textView, int clockWorkType) {
        AttendanceHelper.setupStatusTag(textView, clockWorkType);
    }

    @BindingAdapter("nameAndDept")
    public static void setNameAndDeptUserInfo(TextView view, String userId) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
        if (contact != null) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("<font color=\"#212121\">");
            stringBuilder.append(contact.getShowName());
            stringBuilder.append("</font>");
            if (!TextUtils.isEmpty(contact.getShowDeptNames())) {
                stringBuilder.append("<font color=\"#B1B1B8\">");
                stringBuilder.append(" - ");
                stringBuilder.append(contact.getShowDeptNames());
                stringBuilder.append("</font>");
            }
            view.setText(Html.fromHtml(stringBuilder.toString()));
        }
    }

    @BindingAdapter("avatarGroupMatch")
    public static void avatarGroupMatch(View view, GroupMatchBean group) {
        setAvatar(view, group.avatar, group.getStrAvatar(), 0);
    }

    @BindingAdapter("departmentName")
    public static void setDepartmentName(TextView textView, String depId) {
        String departmentName = ServiceManager.getInstance().getDepartmentService().getDepName(depId);
        if (!TextUtils.isEmpty(departmentName)) {
            textView.setText(departmentName);
        }
    }

    @BindingAdapter("imageUrlWrap")
    public static void setWrapImage(ImageView imageView, String url) {
        if (!TextUtils.isEmpty(url)) {
            Glide.with(imageView).load(url).into(imageView);
        }
    }

    @BindingAdapter("corner")
    public static void corner(View view, int radius) {
        BannerUtils.setBannerRound(view, radius);
    }

    @BindingAdapter(value = {"circleCrop", "error"}, requireAll = false)
    public static void circleCrop(ImageView imageView, String url, Drawable error) {

        if (error != null) {
            Glide.with(imageView).load(url).error(error).circleCrop().into(imageView);
        } else {
            Glide.with(imageView).load(url).circleCrop().into(imageView);
        }

    }

    @BindingAdapter({"imageUrlWrap", "defaultImageHeight", "defaultImageWith"})
    public static void setWrapImageByHeight(SimpleDraweeView simpleDraweeView, String url, int defaultImageHeight, int defaultImageWith) {
        try {
            if (TextUtils.isEmpty(url)) {
                return;
            }
            final ViewGroup.LayoutParams layoutParams = simpleDraweeView.getLayoutParams();
            ControllerListener<ImageInfo> controllerListener = new BaseControllerListener<ImageInfo>() {
                @Override
                public void onFinalImageSet(String id, @Nullable com.facebook.imagepipeline.image.ImageInfo imageInfo, @Nullable Animatable anim) {
                    if (imageInfo == null) {
                        return;
                    }
                    int height = imageInfo.getHeight();
                    int width = imageInfo.getWidth();
                    int defaultHeight = QMUIDisplayHelper.dpToPx(defaultImageHeight);
                    if (height <= 0 || width <= 0) {
                        height = defaultHeight;
                        width = QMUIDisplayHelper.dpToPx(defaultImageWith);
                    }
                    int scaleWidth = width * defaultHeight / height;
                    simpleDraweeView.setAspectRatio(scaleWidth);
                    layoutParams.width = scaleWidth;
                    layoutParams.height = defaultHeight;
                    simpleDraweeView.setLayoutParams(layoutParams);
                }

                @Override
                public void onIntermediateImageSet(String id, @Nullable com.facebook.imagepipeline.image.ImageInfo imageInfo) {
                }

                @Override
                public void onFailure(String id, Throwable throwable) {
                }
            };
            url = KotlinExtKt.appendUrlParams(url, "hi_t=" + HiKernel.getHikernel().getAccount().getTicket());
            PipelineDraweeControllerBuilder builder = Fresco.newDraweeControllerBuilder()
                    .setControllerListener(controllerListener);

            String origin = (String) simpleDraweeView.getTag();
            if (TextUtils.isEmpty(origin)) {
                DraweeController controller = builder.setUri(Uri.parse(url))
                        .build();
                simpleDraweeView.setController(controller);
                simpleDraweeView.setTag(url);
            } else if (!origin.equals(url)) {
                DraweeController controller = builder.setUri(Uri.parse(url))
                        .build();
                simpleDraweeView.setController(controller);
                simpleDraweeView.setTag(url);
            } else {

                DraweeController controller = builder.setUri(Uri.parse(url))
                        .build();
                simpleDraweeView.setController(controller);
            }
        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
        }
    }

    @BindingAdapter({"bg"})
    public static void setBackground(View view, int id) {
        view.setBackgroundResource(id);
    }


    /**
     * 标准尺寸
     * 图片：文字 = 48：16
     *
     * @param view
     * @param url
     * @param name
     */
    @BindingAdapter({"avatarUrl", "avatarName", "avatarCorner"})
    public static void setAvatar(View view, String url, String name, int corner) {
        ViewGroup.LayoutParams params = view.getLayoutParams();
        int size = QMUIDisplayHelper.px2dp(view.getContext(), Math.min(params.width, params.height));
        if (size == 0) {
            size = QMUIDisplayHelper.px2dp(view.getContext(), Math.min(view.getMeasuredWidth(), view.getMeasuredHeight()));
        }
        SimpleDraweeView sdvAvatar = view.findViewById(R.id.sdv_avatar);
        if (sdvAvatar == null) {
            sdvAvatar = view.findViewWithTag("contact_avatar");
            if (sdvAvatar == null) {
                TLog.error(TAG, "sdvAvatar is null");
                return;
            }
        }
        setRoundingParams(view, corner, sdvAvatar);
        TextView tvAvatar = view.findViewById(R.id.tv_avatar);
        if (tvAvatar == null) {
            tvAvatar = view.findViewWithTag("contact_str_avatar");
            if (tvAvatar == null) {
                TLog.error(TAG, "tvAvatar is null");
                return;
            }
        }

        boolean hasAvatar = !TextUtils.isEmpty(url);
        if (hasAvatar) {
            setImage(sdvAvatar, url);
        } else {
            tvAvatar.setTextSize(TypedValue.COMPLEX_UNIT_DIP, size * 18 / 48.0f);
            tvAvatar.setText(name);
            TLog.info(TAG, "avatar url is null, name is " + name);
        }
        sdvAvatar.setVisibility(hasAvatar ? View.VISIBLE : View.GONE);
        tvAvatar.setVisibility(hasAvatar ? View.GONE : View.VISIBLE);
    }

    private static void setRoundingParams(View view, int corner, SimpleDraweeView sdvAvatar) {
        RoundingParams roundingParams = new RoundingParams();
        if (corner > 0) {
            roundingParams.setRoundAsCircle(false);
            roundingParams.setCornersRadius(corner);
            GenericDraweeHierarchy genericDraweeHierarchy = GenericDraweeHierarchyBuilder.newInstance(view.getContext().getResources())
                    .setRoundingParams(roundingParams).build();
            sdvAvatar.setHierarchy(genericDraweeHierarchy);
            if (view.findViewById(R.id.tv_bg_circle) != null) {
                view.findViewById(R.id.tv_bg_circle).setVisibility(View.GONE);
            }
        } else {
            roundingParams.setRoundAsCircle(true);
            GenericDraweeHierarchy genericDraweeHierarchy = GenericDraweeHierarchyBuilder.newInstance(view.getContext().getResources())
                    .setRoundingParams(roundingParams).build();
            sdvAvatar.setHierarchy(genericDraweeHierarchy);
            if (view.findViewById(R.id.tv_bg_circle) != null) {
                view.findViewById(R.id.tv_bg_circle).setVisibility(View.VISIBLE);
            }
        }
    }

    //drawableIndex 1-left,2-top,3-right,4-bottom
    @BindingAdapter(value = {"textDrawableId", "drawableIndex", "drawableWith", "drawableHeight"}, requireAll = false)
    public static void setTextDrawableId(TextView textView, int id, int drawableIndex, int with, int height) {
        if (id > 0) {
            Drawable dra = textView.getResources().getDrawable(id);
            if (with <= 0 || height <= 0) {
                setTextDrawable(textView, dra, drawableIndex);
            } else {
                setTextDrawable(textView, dra, drawableIndex, QMUIDisplayHelper.dpToPx(with), QMUIDisplayHelper.dpToPx(height));
            }
        }
    }

    public static void setTextDrawable(TextView textView, Drawable dra, int drawableIndex) {
        setTextDrawable(textView, dra, drawableIndex, dra.getMinimumWidth(), dra.getMinimumHeight());
    }

    public static void setTextDrawable(TextView textView, Drawable dra, int drawableIndex, int with, int height) {
        dra.setBounds(0, 0, with, height);
        switch (drawableIndex) {
            case BasicBindingUtils.TEXT_DRAWABLE_LEFT:
                textView.setCompoundDrawables(dra, null, null, null);
                break;
            case BasicBindingUtils.TEXT_DRAWABLE_TOP:
                textView.setCompoundDrawables(null, dra, null, null);
                break;
            case BasicBindingUtils.TEXT_DRAWABLE_RIGHT:
                textView.setCompoundDrawables(null, null, dra, null);
                break;
            case BasicBindingUtils.TEXT_DRAWABLE_BOTTOM:
                textView.setCompoundDrawables(null, null, null, dra);
                break;
        }

    }

    @BindingAdapter(value = {"avatarUser", "showPendant", "useFresco"}, requireAll = false)
    public static void avatarUser(View view, User user, boolean showPendant, boolean useFresco) {
        if (user == null) {
            return;
        }
        setAvatar(view, TextUtils.isEmpty(user.getTinyAvatar()) ? user.getAvatar() : user.getTinyAvatar(), user.getStrAvatar());
        SimpleDraweeView pendantView = view.findViewById(R.id.iv_pendant);
        if (showPendant && pendantView != null) {
            setPendant(pendantView, user.getAvatarDecoration(), useFresco);
        }
    }

    @BindingAdapter({"videoDownloadProgress"})
    public static void setVideoDownloadProgress(VideoDownloadProgressbar downloadProgressbar, int progress) {
        downloadProgressbar.setProgress(progress);
    }

    @BindingAdapter("setEmotionText")
    public static void showEmotionText(MLinkEmotionTextView mLinkEmotionTextView, String text) {
        if (!StringUtils.isEmpty(text)) {

            mLinkEmotionTextView.setEmotionAndLinkText(text, true);
        }
    }

    @BindingAdapter({"duration", "playEnter"})
    public static void setAlphaEnterAnimating(View view, long duration, boolean playEnter) {
        if (playEnter) {
            PropertyValuesHolder alpha = PropertyValuesHolder.ofFloat("alpha", 0f, 1f);
            ObjectAnimator objectAnimator = ObjectAnimator.ofPropertyValuesHolder(view, alpha);
            objectAnimator.setDuration(duration);
            objectAnimator.start();
        }
    }

    @BindingAdapter({"duration", "playExit"})
    public static void setAlphaExitAnimating(View view, long duration, boolean playExit) {
        if (playExit) {
            PropertyValuesHolder alpha = PropertyValuesHolder.ofFloat("alpha", 1f, 0f);
            ObjectAnimator objectAnimator = ObjectAnimator.ofPropertyValuesHolder(view, alpha);
            objectAnimator.setDuration(duration);
            objectAnimator.start();
        }
    }

    @BindingAdapter("avatarWithClick")
    public static void setAvatarWithClick(View view, String userId) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
        if (contact != null) {
            BasicBindingAdapters.setAvatar(view, TextUtils.isEmpty(contact.getTinyAvatar()) ? contact.getAvatar() : contact.getTinyAvatar(), contact.getStrAvatar());
            view.setOnClickListener(v -> {
                if (contact.getUserType() == MessageConstants.MSG_CONTACT_TYPE_SYS) {
                    return;
                }
                OrganizationPageRouter.jumpToUserInfoActivity(view.getContext(), contact.getUserId());
            });
        }
    }

    @BindingAdapter({"downloadProgress"})
    public static void setDownloadProgress(DownloadProgressbar downloadProgressbar, int progress) {
        downloadProgressbar.setProgress(progress);
    }

    @BindingAdapter(value = {"status", "isSupportPreviewFileType"}, requireAll = true)
    public static void setStatusText(TextView button, int status, boolean isSupportPreviewFileType) {
        switch (status) {
            case FileDownLoadViewModel.STATUS_NONE:
                button.setText(R.string.download);
                break;
            case FileDownLoadViewModel.STATUS_DOWNLOADING:
                button.setText(R.string.downloading);
                break;
            case FileDownLoadViewModel.STATUS_DOWNLOAD:
                if (isSupportPreviewFileType) {
                    button.setText(R.string.open_file_in_app);
                } else {
                    button.setText(R.string.open_with_other_app);
                }
                break;
            case FileDownLoadViewModel.STATUS_CONTINUE_DOWNLOAD:
                button.setText(R.string.continue_download);
                break;
            default:
                break;
        }
    }

    @BindingAdapter({"isSupportPreviewFileType"})
    public static void setHintText(TextView button, boolean isSupportPreviewFileType) {
        if (isSupportPreviewFileType) {
            button.setText("");
        } else {
            button.setText("BossHi 暂不支持预览此类文件，你可以先下载到本地。");
        }
    }


    /**
     * 新增支持ViewPager2的适配方法
     *
     * @param viewPager
     * @param index
     * @param smoothScroll
     */
    @BindingAdapter({"currentItem", "smooth"})
    public static void setCurrentItem(ViewPager2 viewPager, int index, boolean smoothScroll) {
        if (viewPager.getCurrentItem() != index) {
            viewPager.setCurrentItem(index, smoothScroll);
        }
    }

    @BindingAdapter({"currentItem", "smooth"})
    public static void setCurrentItem(ViewPager viewPager, int index, boolean smoothScroll) {
        if (viewPager.getCurrentItem() != index) {
            viewPager.setCurrentItem(index, smoothScroll);
        }
    }

    @BindingAdapter("imageSrc")
    public static void setImageSrc(ImageView imageView, int src) {
        if (src > 0) {
            imageView.setImageResource(src);
        }
    }

    @BindingAdapter("singleLine")
    public static void setSingleLine(TextView textView, boolean singleLine) {
        textView.setSingleLine(singleLine);
    }

    @BindingAdapter("useThemeTextColor")
    public static void setThemeTextColor(TextView textView, boolean useTheme) {
        textView.setTextColor(ThemeUtils.getThemeTextColorInt());
    }

    @BindingAdapter("taskDetailStatus")
    public static void setTaskDetailStatus(CommonTextView textView, int status) {
        //状态：1-待认领，2-进行中，3-已完成
        switch (status) {
            case 2:
                textView.setText(R.string.task_on);
                break;
            case 3:
                textView.setText(R.string.task_finish);
                break;
            default:
                textView.setText(R.string.task_get);
                break;
        }
    }

    @BindingAdapter({"expandContent", "expandState"})
    public static void setExpandText(ExpandableTextView textView, String expandContent, ExpandableStateRecord expandState) {
        textView.bind(expandState);
        textView.setContent(expandContent);
    }

    @BindingAdapter({"avatarWithTextSize", "avatarTextSize"})
    public static void avatarWithTextSize(View view, String userId, int textSize) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
        if (contact == null) {
            return;
        }
        setAvatarWithTextSize(view, TextUtils.isEmpty(contact.getTinyAvatar()) ? contact.getAvatar() : contact.getTinyAvatar(), contact.getStrAvatar(), 0, textSize);
    }

    public static void setAvatarWithTextSize(View view, String url, String name, int corner, int textSize) {
        SimpleDraweeView sdvAvatar = view.findViewById(R.id.sdv_avatar);
        if (sdvAvatar == null) {
            sdvAvatar = view.findViewWithTag("contact_avatar");
        }
        if (sdvAvatar == null) {
            return;
        }
        setRoundingParams(view, corner, sdvAvatar);
        TextView tvAvatar = view.findViewById(R.id.tv_avatar);
        if (tvAvatar == null) {
            tvAvatar = view.findViewWithTag("contact_str_avatar");
        }
        if (tvAvatar == null) {
            return;
        }
        boolean hasAvatar = !TextUtils.isEmpty(url);
        if (hasAvatar) {
            try {
                setImage(sdvAvatar, url);
            } catch (Throwable error) {
                tvAvatar.setTextSize(TypedValue.COMPLEX_UNIT_DIP, textSize);
                tvAvatar.setText(name);
            }
        } else {
            tvAvatar.setTextSize(TypedValue.COMPLEX_UNIT_DIP, textSize);
            tvAvatar.setText(name);
        }
        sdvAvatar.setVisibility(hasAvatar ? View.VISIBLE : View.GONE);
        tvAvatar.setVisibility(hasAvatar ? View.GONE : View.VISIBLE);
    }

    @BindingAdapter({"viewSelected"})
    public static void setViewSelected(View view, boolean isSelected) {
        view.setSelected(isSelected);
    }

    @BindingAdapter({"onVerticalText"})
    public static void setOnVerticalText(VerticalTextView view, List<ScheduleBean> scheduleBeans) {
        if (scheduleBeans == null || scheduleBeans.isEmpty()) {
            return;
        }
        ArrayList<SpannableString> res = new ArrayList<>();
        for (ScheduleBean scheduleBean : scheduleBeans) {
            res.add(scheduleBean.getShowTitle());
        }
        view.setTextSpannableList(res);
        if (res.size() > 1) {
            view.startAutoScroll();
        } else {
            view.stopAutoScroll();
        }
    }

    @BindingAdapter({"highlightText", "highlightKeyword"})
    public static void highlightText(TextView textView, String text, String keyword) {
        SpannableString span = new SpannableString(text);
        Pattern pattern = Pattern.compile(Pattern.quote("" + keyword.toLowerCase()));
        Matcher matcher = pattern.matcher(text);
        while (matcher.find()) {
            span.setSpan(new ForegroundColorSpan(Color.parseColor("#5055EB")), matcher.start(), matcher.end(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        textView.setText(span);
    }


    @BindingAdapter("showImageByUrl")
    public static void showImageByUrl(SimpleDraweeView view, String url) {
        innerShowMsgPic(view, url);
    }

    private static void innerShowMsgPic(SimpleDraweeView view, String imageUrl) {
        if (TextUtils.isEmpty(imageUrl)) {
            return;
        }
        ImageUtils.isGifAsync(view, imageUrl, isGif -> {
            if (isGif) {
                Glide.with(view)
                        .asGif()
                        .load(imageUrl)
                        .into(view);
            } else {
                loadImgByFresco(view, imageUrl);
            }
        });
    }

    private static void loadImgByFresco(SimpleDraweeView view, String imageUrl) {
        DraweeController controller = Fresco.newDraweeControllerBuilder()
                .setUri(Uri.parse(imageUrl))
                .setAutoPlayAnimations(true)//设置为true将循环播放Gif动画
                .setOldController(view.getController())
                .setContentDescription(imageUrl)
                .build();
        view.setController(controller);
    }

    @BindingAdapter({"itemAvatarClick", "itemAvatarDoubleClick", "itemAvatarLongClick"})
    public static void setAllTypeClickListener(AllClickTypeView view, View.OnClickListener itemAvatarClick, View.OnClickListener itemAvatarDoubleClick, View.OnClickListener itemAvatarLongClick) {
        view.setOnAllTypeClickListener(new AllClickTypeView.OnAllTypeClickListener() {
            @Override
            public void onClickListener() {
                if (itemAvatarClick != null) {
                    itemAvatarClick.onClick(view);
                }
            }

            @Override
            public void onDoubleClickListener() {
                if (itemAvatarDoubleClick != null) {
                    itemAvatarDoubleClick.onClick(view);
                }
            }

            @Override
            public void onLongClickListener() {
                if (itemAvatarLongClick != null) {
                    itemAvatarLongClick.onClick(view);
                }
            }
        });
    }

    @BindingAdapter("resId")
    public static void setImageId(ImageView view, int resId) {
        if (resId > 0) {
            view.setImageResource(resId);
        }
    }

    @BindingAdapter("NoRepeatClick")
    public static void setNoRepeatClickListener(View view,View.OnClickListener listener){
        view.setOnClickListener(new NoRepeatClickListener(listener));
    }

    @BindingAdapter({"itemCount", "itemPos"})
    public static void setItemBackground(View view, int itemCount, int itemPos) {
        if (itemCount <= 1) {
            view.setBackgroundResource(R.drawable.bg_selector_item_rounded);
            return;
        }
        if (itemPos == 0) {
            view.setBackgroundResource(R.drawable.bg_selector_item_top);
        } else if (itemPos == itemCount - 1) {
            view.setBackgroundResource(R.drawable.bg_selector_item_bottom);
        } else {
            view.setBackgroundResource(R.drawable.bg_selector_item_normal);
        }
    }
}
