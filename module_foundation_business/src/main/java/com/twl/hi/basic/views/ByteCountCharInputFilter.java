package com.twl.hi.basic.views;

import android.text.InputFilter;
import android.text.Spanned;

/**
 * <AUTHOR>
 * @date 2020/3/18.
 */
public class ByteCountCharInputFilter implements InputFilter {
    private int maxLen = 16;//中英文混合长度，中文占两个长度，英文占一个

    public ByteCountCharInputFilter(int maxLen) {
        this.maxLen = maxLen;
    }

    @Override
    public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
        if (maxLen > 0) {
            int destLength = calculateLength(dest.toString());
            if (destLength >= maxLen) {
                return "";
            }
            int sourceLength = calculateLength(source.toString());
            if (destLength + sourceLength > maxLen) {
                int sourceEnd = calculateSourEndSplitIndex(source.toString(), maxLen - destLength);
                if (sourceEnd < 0) {
                    sourceEnd = 0;
                }
                if (sourceEnd > source.length()) {
                    sourceEnd = source.length();
                }
                return source.subSequence(0, sourceEnd);
            }
        }
        return null;
    }

    /**
     * 计算中英文的长度
     *
     * @param etString
     * @return
     */
    private int calculateLength(String etString) {
        char[] ch = etString.toCharArray();
        int varlength = 0;
        for (int i = 0; i < ch.length; i++) {
            if ((ch[i] >= 0x2E80 && ch[i] <= 0xFE4F)
                    || (ch[i] >= 0xA13F && ch[i] <= 0xAA40)
                    || ch[i] >= 0x80) {
                varlength = varlength + 2;
            } else {
                varlength++;
            }
        }
        return varlength;
    }

    /**
     * 输入中英文混排，计算需要截取的字符的位置
     *
     * @param etString
     * @param length
     * @return
     */
    private int calculateSourEndSplitIndex(String etString, int length) {
        char[] ch = etString.toCharArray();
        int varlength = 0;
        for (int i = 0; i < ch.length; i++) {
            if ((ch[i] >= 0x2E80 && ch[i] <= 0xFE4F)
                    || (ch[i] >= 0xA13F && ch[i] <= 0xAA40)
                    || ch[i] >= 0x80) {
                varlength = varlength + 2;
                if (varlength > length) {
                    return i;
                }
            } else {
                varlength++;
                if (varlength > length) {
                    return i;
                }
            }
        }
        return ch.length;
    }
}
