package com.twl.hi.basic.model;

import android.text.TextUtils;

import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.model.message.MessageForVideo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/10/21.
 */
public class WebVideoPlayBean extends WebViewBaseBean implements Serializable {
    private static final long serialVersionUID = 6884453426042926955L;
    private VideoOptionBean videoOptionBean;
    private MessageForVideo.VideoInfo videoInfo;

    public void setVideoOptionBean(VideoOptionBean videoOptionBean) {
        this.videoOptionBean = videoOptionBean;
    }

    public String getChatId() {
        if (videoOptionBean == null) {
            return "";
        }
        return videoOptionBean.getChatId();
    }

    public int getChatType() {
        if (videoOptionBean == null) {
            return MessageConstants.MSG_SINGLE_CHAT;
        }
        return videoOptionBean.getChatType();
    }

    public boolean isCantGoPicVideo() {
        if (videoOptionBean == null) {
            return false;
        }
        return videoOptionBean.isCantGoPicVideo();
    }

    public long getMid() {
        if (videoOptionBean == null) {
            return 0L;
        }
        return videoOptionBean.getMid();
    }

    public long getSeq() {
        if (videoOptionBean == null) {
            return 0L;
        }
        return videoOptionBean.getSeq();
    }

    public MessageForVideo.VideoInfo getVideoInfo() {
        return videoInfo;
    }

    public void setVideoInfo(MessageForVideo.VideoInfo videoInfo) {
        this.videoInfo = videoInfo;
        setPath(videoInfo.getLocalPath());
        setUrl(videoInfo.getUrl());
    }

    public String getLocalPath() {
        if (!TextUtils.isEmpty(path)) {
            return path;
        }
        return "";
    }

    public String getThumbnailUrl() {
        if (videoInfo != null && videoInfo.getThumbnail() != null) {
            return videoInfo.getThumbnail().getUrl();
        }
        return "";
    }
}
