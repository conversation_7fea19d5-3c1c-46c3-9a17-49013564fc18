package com.twl.hi.basic.views;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.drawerlayout.widget.DrawerLayout;

/**
 * Created by ChaiJiangpeng on 2020/7/7
 * Describe:
 */
public class HiDrawerListener implements DrawerLayout.DrawerListener {

    private Runnable runnable;

    @Override
    public void onDrawerSlide(@NonNull View drawerView, float slideOffset) {

    }

    @Override
    public void onDrawerOpened(@NonNull View drawerView) {

    }

    @Override
    public void onDrawerClosed(@NonNull View drawerView) {

    }

    @Override
    public void onDrawerStateChanged(int newState) {
        if (runnable != null && newState == DrawerLayout.STATE_IDLE) {
            runnable.run();
            runnable = null;
        }

    }

    public void runWhenIdle(Runnable runnable) {
        this.runnable = runnable;
    }

}
