package com.twl.hi.basic.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.basic.api.response.AvatorUploadResponse;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.AbsUploadApiRequest;
import com.twl.http.config.RequestMethod;

import java.io.File;

public class AvatorUploadRequest extends AbsUploadApiRequest<AvatorUploadResponse> {

    @Expose
    public File file;

    public AvatorUploadRequest(AbsRequestCallback<AvatorUploadResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_UPLOAD_AVATAR;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
