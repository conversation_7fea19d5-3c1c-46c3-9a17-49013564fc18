package com.twl.hi.basic.api.request;

import com.twl.hi.basic.api.response.PayChannelListQueryResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * 查询支付渠道信息
 */
public class PayChannelListQueryRequest extends BaseApiRequest<PayChannelListQueryResponse> {

    public PayChannelListQueryRequest(BaseApiRequestCallback<PayChannelListQueryResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_PAY_CHANNEL_INFO;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
