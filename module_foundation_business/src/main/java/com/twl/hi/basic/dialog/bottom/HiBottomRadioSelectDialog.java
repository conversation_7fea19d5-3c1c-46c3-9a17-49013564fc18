package com.twl.hi.basic.dialog.bottom;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.twl.hi.basic.BR;
import com.twl.hi.basic.BottomSheetBehaviorBaseDialog;
import com.twl.hi.basic.R;
import com.twl.hi.basic.databinding.ViewDialogBottomRadioSelectBinding;

import java.util.List;

import lib.twl.common.base.BaseViewModel;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.views.adapter.BaseQuickAdapter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/30
 * Describe: 底部弹框单选
 */
public class HiBottomRadioSelectDialog extends BottomSheetBehaviorBaseDialog<ViewDialogBottomRadioSelectBinding, BaseViewModel> implements BottomRadioSelectCallback {
    public static final String TAG = "BottomRadioSelectDialog";
    private List<HiBottomSelectItemBean> mItems;
    private HiBottomRadioSelectAdapter mAdapter;
    private String title;
    private OnClickSureListener mClickSureListener;

    private int mResCheckedIc = R.drawable.hd_icon_check;
    private int mHighlightColor = R.color.image_color_black;

    /**是否是二次确认模式，底部有按钮确认*/
    private boolean mIsDoubleCheckMode = true;
    /**
     * 是否添加分割线
     */
    private boolean mIsAddDividerLine = false;

    private final ObservableBoolean mShowIndicate = new ObservableBoolean(false);

    public HiBottomRadioSelectDialog() {
    }

    public HiBottomRadioSelectDialog(List<HiBottomSelectItemBean> items, String title) {
        this.mItems = items;
        this.title = title;
    }

    public HiBottomRadioSelectDialog(boolean isDoubleCheckMode, List<HiBottomSelectItemBean> items, String title) {
        mIsDoubleCheckMode = isDoubleCheckMode;
        this.mItems = items;
        this.title = title;

        if (!mIsDoubleCheckMode) { // 单选模式，不显示指示器
            mItems.stream().forEach(item -> item.setChecked(false));
        }
    }


    @Override
    public int getContentLayoutId() {
        return R.layout.view_dialog_bottom_radio_select;
    }

    @Override
    protected void initFragment() {
        initData();

        if (!mIsDoubleCheckMode) {
            mDataBinding.clBottomBt.setVisibility(View.GONE);
        }
        getBehavior().setBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {

            @Override
            public void onStateChanged(@NonNull View view, int i) {
                if (i == BottomSheetBehavior.STATE_HIDDEN) {
                    dismiss();
                    if (getHideCallback() != null) {
                        getHideCallback().onHide();
                    }
                }
            }

            @Override
            public void onSlide(@NonNull View view, float v) {

            }
        });

        if (mItems == null) {
            dismiss();
        }
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return 0;
    }

    private void initData() {
        getDataBinding().tvTitle.setText(title);
        mAdapter = new HiBottomRadioSelectAdapter(mItems);
        getDataBinding().recycler.setLayoutManager(new LinearLayoutManager(getContext()));
        getDataBinding().setShowIndicator(mShowIndicate);
        if (mIsAddDividerLine) {
            setDivider();
        }
        mAdapter.bindToRecyclerView(getDataBinding().recycler);
        mAdapter.setResCheckIc(mResCheckedIc);
        mAdapter.setHighlightColor(mHighlightColor);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                HiBottomSelectItemBean itemBean = mItems.get(position);
                if (mIsDoubleCheckMode) {
                    for (HiBottomSelectItemBean itemBean1 : mItems) {
                        itemBean1.setChecked(false);
                    }
                    itemBean.setChecked(true);
                    mAdapter.notifyDataSetChanged();
                } else {
                    dismiss();
                    mClickSureListener.onClickSure(itemBean.getKey(), itemBean.getValue());
                }
            }
        });
    }

    /**
     * 设置选中图标
     */
    public void setResCheckIc(@DrawableRes int res) {
        if (mAdapter != null) {
            mAdapter.setResCheckIc(res);
        }
        mResCheckedIc = res;
    }

    /**
     * 设置高亮颜色
     */
    public void setHighlightColor(int color) {
        if (mAdapter != null) {
            mAdapter.setHighlightColor(color);
        }
        mHighlightColor = color;
    }

    /**
     * 设置是否展示顶部indicate
     */
    public void setShowIndicate(boolean show) {
        mShowIndicate.set(show);
    }

    public void showDialog(FragmentActivity activity, String keyKey) {
        if (isAdded() || (activity.getSupportFragmentManager().findFragmentByTag(TAG) != null)) {
            return;
        }
        for (HiBottomSelectItemBean itemBean : mItems) {
            itemBean.setChecked(TextUtils.equals(keyKey, itemBean.getKey()));
        }
        show(activity.getSupportFragmentManager(), TAG);
    }

    @Override
    public void onClickDismiss() {
        dismiss();
    }

    @Override
    public void onClickSure() {
        dismiss();
        if (mClickSureListener != null) {
            String key = "";
            String value = "";
            for (HiBottomSelectItemBean itemBean : mItems) {
                if (itemBean.isChecked()) {
                    key = itemBean.getKey();
                    value = itemBean.getValue();
                    break;
                }
            }
            mClickSureListener.onClickSure(key, value);
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (getHideCallback() != null) {
            getHideCallback().onHide();
        }
    }

    public void setNewData(List<HiBottomSelectItemBean> items) {
        if (mAdapter != null) {
            mAdapter.setNewData(items);
        }
        mItems = items;
    }

    @Override
    public int getBottomSheetLayoutParamsHeight() {
        if (mItems != null && mItems.size() >= 5) {
            return QMUIDisplayHelper.getScreenHeight(getContext()) / 2;
        } else {
            return super.getBottomSheetLayoutParamsHeight();
        }
    }

    private void setDivider() {
        ColorDrawable colorDrawable = new ColorDrawable(Color.parseColor("#F2F4F5"));
        colorDrawable.setBounds(0, 0, 0, 1);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(getContext(), DividerItemDecoration.VERTICAL);
        dividerItemDecoration.setDrawable(colorDrawable);
        getDataBinding().recycler.addItemDecoration(dividerItemDecoration);
    }

    public void setAddDividerLine(boolean addDividerLine) {
        mIsAddDividerLine = addDividerLine;
    }

    public void setClickSureListener(OnClickSureListener clickSureListener) {
        mClickSureListener = clickSureListener;
    }

    public interface OnClickSureListener {

        void onClickSure(String key, String value);
    }
}
