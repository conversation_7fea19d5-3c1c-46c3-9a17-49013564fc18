package com.twl.hi.basic.download;

import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 文件分片上传
 */
public class OkRenewalUpload {

    private ExecutorService executorService;


    public static OkRenewalUpload getInstance() {
        return OkUploadHolder.instance;
    }

    private static class OkUploadHolder {
        private static final OkRenewalUpload instance = new OkRenewalUpload();
    }

    private OkRenewalUpload() {
    }


    public static RenewalUploadTask request(String tag, File file) {
        return new RenewalUploadTask(tag, file);
    }


    public ExecutorService getExecutor() {
        if (executorService == null) {
            executorService = Executors.newCachedThreadPool();
        }
        return executorService;
    }
}
