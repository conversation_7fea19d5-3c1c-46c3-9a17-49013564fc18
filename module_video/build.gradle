apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

apply from: rootProject.file('bzl-push.gradle')

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion build_versions.build_tools

    defaultConfig {
        minSdkVersion build_versions.min_sdk
        targetSdkVersion build_versions.target_sdk
        versionCode rootProject.ext.appVersionCode
        versionName rootProject.ext.appShowVersionName
        resourcePrefix 'video_'
    }
    dataBinding {
        enabled = true
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
        freeCompilerArgs += "-Xjvm-default=all"
    }
}

dependenciesForImplementation(project, ":module_foundation_business", deps.commonLibs.module_foundation_business)

dependenciesForImplementation(project, ":export_module_select", deps.commonLibs.export_module_select)
dependenciesForImplementation(project, ":export_module_work", deps.commonLibs.export_module_work)
dependenciesForImplementation(project, ":export_module_chat", deps.commonLibs.export_module_chat)
dependenciesForImplementation(project, ":export_module_webview", deps.commonLibs.export_module_webview)
dependenciesForImplementation(project, ":export_module_audio_shorthand", deps.commonLibs.export_module_audio_shorthand)
dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    implementation deps.afantyPlayer
}