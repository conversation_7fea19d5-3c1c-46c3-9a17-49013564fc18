# module_video

## 基础信息

### 模块名称和主要用途
module_video 是视频业务模块，提供视频播放、录制、编辑等视频相关功能。

### 系统定位和作用
- 作为项目的核心业务模块之一
- 依赖module_foundation_business等基础模块
- 提供视频处理功能
- 管理视频数据和状态

### 技术栈和框架
- MVVM架构
- Kotlin协程
- DataBinding
- Lifecycle组件
- Room数据库
- ExoPlayer
- MediaCodec
- CameraX
- OpenGL ES
- 缓存管理

### 核心依赖项
- module_foundation_business：业务基础模块
- lib_foundation_service：基础服务
- lib_http：网络请求
- lib_file：文件处理
- lib_cache：缓存服务
- lib_secret：加密服务
- export_module_organization：组织架构导出
- export_module_file：文件导出

## 功能描述

### 主要功能
1. 视频播放
   - 本地视频播放
   - 在线视频播放
   - 直播流播放
   - 播放控制
2. 视频录制
   - 普通录制
   - 高清录制
   - 慢动作录制
   - 延时摄影
3. 视频编辑
   - 视频剪辑
   - 滤镜效果
   - 文字叠加
   - 特效处理
4. 视频管理
   - 视频分类
   - 视频检索
   - 视频分享
   - 视频备份

### 关键业务流程
1. 视频播放流程
   - 资源加载
   - 解码渲染
   - 播放控制
   - 状态同步
2. 视频录制流程
   - 相机初始化
   - 预览展示
   - 录制处理
   - 视频保存
3. 视频编辑流程
   - 视频导入
   - 编辑操作
   - 效果预览
   - 导出保存
4. 视频管理流程
   - 视频扫描
   - 分类整理
   - 搜索查询
   - 操作处理

### 业务规则和约束
1. 播放规则
   - 格式支持
   - 清晰度控制
   - 网络适应
   - 播放优化
2. 录制规则
   - 质量设置
   - 时长限制
   - 资源控制
   - 存储管理
3. 编辑规则
   - 操作限制
   - 效果支持
   - 资源占用
   - 导出选项
4. 安全规则
   - 数据加密
   - 访问控制
   - 隐私保护
   - 内容审核

### 与其他模块交互
- 依赖基础模块
  - module_foundation_business
  - lib_foundation_service
- 依赖功能模块
  - lib_http
  - lib_file
  - lib_cache
  - lib_secret
- 依赖导出接口
  - export_module_organization
  - export_module_file

## 技术架构

### 整体架构设计
```
module_foundation_business (业务基础模块)
            ↑
module_video (视频模块)
    ↑    ↑    ↑
UI层  业务层  数据层
```

### 核心组件及关系
1. UI组件
   - 播放器组件
   - 录制组件
   - 编辑组件
   - 管理组件
2. 业务组件
   - 播放管理器
   - 录制管理器
   - 编辑管理器
   - 存储管理器
3. 数据组件
   - 视频存储
   - 配置存储
   - 缓存存储
   - 统计存储
4. 工具组件
   - 解码工具
   - 编码工具
   - 处理工具
   - 特效工具

### 数据流转过程
1. 播放处理
   - 资源加载
   - 解码处理
   - 画面渲染
   - 音频同步
2. 录制处理
   - 相机预览
   - 画面捕获
   - 编码处理
   - 文件保存
3. 编辑处理
   - 解码读取
   - 特效处理
   - 重新编码
   - 文件输出

### 设计模式使用
1. MVVM模式：界面交互
2. 单例模式：管理器类
3. 策略模式：播放策略
4. 观察者模式：状态监听
5. 工厂模式：播放器创建
6. 建造者模式：配置构建
7. 命令模式：编辑操作

## 代码结构

### 目录组织
```
module_video/
├── src/main/java/com/twl/hi/video/
│   ├── ui/           # UI实现
│   │   ├── player/   # 播放界面
│   │   ├── record/   # 录制界面
│   │   ├── edit/     # 编辑界面
│   │   └── manage/   # 管理界面
│   ├── business/     # 业务实现
│   │   ├── player/   # 播放业务
│   │   ├── record/   # 录制业务
│   │   ├── edit/     # 编辑业务
│   │   └── manage/   # 管理业务
│   ├── data/         # 数据处理
│   │   ├── db/       # 数据库
│   │   ├── cache/    # 缓存
│   │   └── config/   # 配置
│   ├── utils/        # 工具类
│   └── model/        # 数据模型
```

### 关键类说明
- PlayerManager: 播放管理器
- RecordManager: 录制管理器
- EditManager: 编辑管理器
- ManageManager: 管理管理器
- PlayerActivity: 播放界面
- RecordActivity: 录制界面
- EditActivity: 编辑界面
- VideoDB: 视频数据库

### 代码分层
1. 表现层
   - 界面实现
   - 交互处理
2. 业务层
   - 播放管理
   - 录制管理
3. 数据层
   - 视频数据
   - 配置数据
4. 工具层
   - 业务工具
   - 通用工具
