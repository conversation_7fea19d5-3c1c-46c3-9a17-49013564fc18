package com.twl.hi.kzmp.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.kzmp.api.response.KZMPTmpCodeResponse;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * Author : Xuweixiang .
 * Date   : On 2022/9/7
 * Email  : Contact <EMAIL>
 * Desc   : 小程序 login 接口
 */

public class KZMPTmpCodeRequest extends BaseApiRequest<KZMPTmpCodeResponse> {

    @Expose
    public String appid;

    public KZMPTmpCodeRequest(AbsRequestCallback<KZMPTmpCodeResponse> callback) {
        mCallback = callback;
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_KZMP_TMP_CODE;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
