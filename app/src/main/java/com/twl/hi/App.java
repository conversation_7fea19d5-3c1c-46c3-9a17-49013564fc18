package com.twl.hi;

import static lib.twl.common.util.TimeTag.APP_START_DRAW;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.Build;
import android.text.TextUtils;
import android.webkit.WebView;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.soloader.SysUtil;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.DefaultRefreshFooterCreator;
import com.scwang.smartrefresh.layout.api.DefaultRefreshHeaderCreator;
import com.scwang.smartrefresh.layout.api.RefreshFooter;
import com.scwang.smartrefresh.layout.api.RefreshHeader;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.footer.ClassicsFooter;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.facade.AttendanceRepository;
import com.twl.hi.foundation.logic.AppLifecycleService;
import com.twl.hi.foundation.logic.AttendanceService;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.monitor.MainThreadMonitor;
import com.twl.hi.push.PushSdkManager;
import com.twl.hi.splash.compile.BatteryReceiver;
import com.twl.hi.splash.compile.ChargeReceiver;
import com.twl.hi.utils.NotifyUtil;
import com.twl.startup.CompileIdleHandler;
import com.twl.startup.Startup;
import com.twl.utils.kv.KV;
import com.twl.utils.network.NetSpeedUtils;
import com.twl.utils.network.NetworkHelper;
import com.twl.utils.process.ProcessUtil;

import org.apache.commons.lang3.StringUtils;

import hi.kernel.HiKernel;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.util.AppForegroundListener;
import lib.twl.common.util.BossHiMapLocation;
import lib.twl.common.util.DeviceStatusUtil;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.PointClearUtils;
import lib.twl.common.util.ProcessHelper;
import lib.twl.common.util.SafeLaunchHelper;
import lib.twl.common.util.TimeDifferenceUtil;

/**
 * 　　　　　　　 ┏┓       ┏┓+ +
 * 　　　　　　　┏┛┻━━━━━━━┛┻┓ + +
 * 　　　　　　　┃　　　　　　 ┃
 * 　　　　　　　┃　　　━　　　┃ ++ + + +
 * 　　　　　　 █████━█████  ┃+
 * 　　　　　　　┃　　　　　　 ┃ +
 * 　　　　　　　┃　　　┻　　　┃
 * 　　　　　　　┃　　　　　　 ┃ + +
 * 　　　　　　　┗━━┓　　　 ┏━┛
 * 　　　　　　　　　┃　　  ┃
 * 　　　　　　　　　┃　　  ┃ + + + +
 * 　　　　　　　　　┃　　　┃　Code is far away from bug with the animal protecting
 * 　　　　　　　　　┃　　　┃ + 　　　　         神兽保佑,代码无bug
 * 　　　　　　　　　┃　　　┃
 * 　　　　　　　　　┃　　　┃　　+
 * 　　　　　　　　　┃　 　 ┗━━━┓ + +
 * 　　　　　　　　　┃ 　　　　　┣┓
 * 　　　　　　　　　┃ 　　　　　┏┛
 * 　　　　　　　　　┗┓┓┏━━━┳┓┏┛ + + + +
 * 　　　　　　　　　 ┃┫┫　 ┃┫┫
 * 　　　　　　　　　 ┗┻┛　 ┗┻┛+ + + +
 */

/**
 * ！！！注意事项：1：新建版本分支之后，首先修改appVersionCode、appVersionName、tinkerId等
 * 2：发版时，保存所打的包及对应文件（之后Jenkins添加该功能后可使用Jenkins上保存的包和文件）
 */
public class App extends BaseApplication implements AppForegroundListener, SensorEventListener {

    static {
        //设置全局的Header构建器
        SmartRefreshLayout.setDefaultRefreshHeaderCreator(new DefaultRefreshHeaderCreator() {
            @Override
            public RefreshHeader createRefreshHeader(Context context, RefreshLayout layout) {
                //指定为经典Header，默认是 贝塞尔雷达Header
                return new ClassicsHeader(context).setEnableLastTime(false);
            }
        });
        //设置全局的Footer构建器
        SmartRefreshLayout.setDefaultRefreshFooterCreator(new DefaultRefreshFooterCreator() {
            @Override
            public RefreshFooter createRefreshFooter(Context context, RefreshLayout layout) {
                //指定为经典Footer，默认是 BallPulseFooter
                return new ClassicsFooter(context);
            }
        });
    }

    private static final String TAG = "App";

    private CompileIdleHandler mCompileIdleHandler;
    // 充电监听
    private ChargeReceiver mChargeReceiver;
    // 是否注册广播监听
    private boolean hasRegisterReceiver;

    private BossHiMapLocation mBossHiMapLocation;

    private SensorManager mSensorManager;
    private Sensor mGravitySensor;
    private Sensor mGyroscopeSensor;
    private BatteryReceiver mBatteryReceiver;

    @Override
    public boolean isDebug() {
        return BuildConfig.DEBUG;
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        SafeLaunchHelper.INSTANCE.appLaunchIn(this);
        /* *
         * 保存应用启动时间，用于记录启动时长
         */

        TimeDifferenceUtil.getInstance().start(APP_START_DRAW);
        // 进程启动后1200ms还未出现在前台，视为自启动导致的进程启动，不做冷启动埋点
        ExecutorFactory.getMainHandler().postDelayed(mRemoveLaunchPointRunnable, 1200);
        KV.init(this);
        //安装tinker
        PatchHelper.init(base);
        ProcessHelper.setContext(this, ProcessUtil.getProcessName());
        HiStartup.getInstance().init(this);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        // 执行核心初始化流程
        Startup.onAppCreate();
        mActivityLifecycleCallbacks.addForegroundListener(this);

        // android O以下不做处理
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mCompileIdleHandler = new CompileIdleHandler(this);
            mChargeReceiver = new ChargeReceiver(mCompileIdleHandler);
        }

        // 为不同进程使用不同目录，避免多进程导致多个 WebView 访问同一份文件出错
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            String processName = ProcessHelper.getProcessName();
            String packageName = getPackageName();
            if (!packageName.equals(processName)) {
                WebView.setDataDirectorySuffix(processName);
            }
        }
    }

    private void initSensor() {
        if (!AttendanceRepository.mIsMoved) {
            mSensorManager = (SensorManager) getSystemService(SENSOR_SERVICE);
            if (mSensorManager != null) {
                mGravitySensor = mSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER);
                if (mGravitySensor != null) {
                    mSensorManager.registerListener(this, mGravitySensor, SensorManager.SENSOR_DELAY_NORMAL);
                }
                mGyroscopeSensor = mSensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE);
                if (mGyroscopeSensor != null) {
                    mSensorManager.registerListener(this, mGyroscopeSensor, SensorManager.SENSOR_DELAY_NORMAL);
                }
            }
        }
        mBatteryReceiver = new BatteryReceiver();
        IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        registerReceiver(mBatteryReceiver, intentFilter);
    }

    @Override
    public void onAppForeground() {
        TLog.info(TAG, "onAppForeground");
        NetworkHelper.setProcessState(true);
        // 出现在前台后，移除延迟消息
        ExecutorFactory.getMainHandler().removeCallbacks(mRemoveLaunchPointRunnable);
        MainThreadMonitor.getInstance().start();
        NotifyUtil.clearNotifications();
        PushSdkManager.getInstance().setForeground(true);
        if (hasRegisterReceiver) {
            unregisterReceiver(mChargeReceiver);
            hasRegisterReceiver = false;
        }

        AppLifecycleService.setForeground(true);
        AttendanceService.autoAttendance();
        pointOnForeground();

        if (TextUtils.equals(ProcessUtil.getProcessName(), getApplication().getPackageName())) {
            if (mBossHiMapLocation == null) {
                mBossHiMapLocation = new BossHiMapLocation(this);
            }
            mBossHiMapLocation.startLocationWithoutPrompt(null);
            initSensor();
        }
    }

    /**
     * 系统内存不足回调
     */
    @Override
    public void onLowMemory() {
        super.onLowMemory();
        ExecutorFactory.execWorkTask(new Runnable() {
            @Override
            public void run() {
                TLog.info(TAG, "onLowMemory " + DeviceStatusUtil.getInstance().getSystemMemInfo(getContext()));
            }
        });
    }

    /**
     * 越高时被杀掉概率越大
     *
     * @param level
     */
    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        ExecutorFactory.execWorkTask(new Runnable() {
            @Override
            public void run() {
                if (level >= TRIM_MEMORY_MODERATE) {
                    TLog.info(TAG, "onTrimMemory [%d] - clearing Fresco memory cache", level);
                    // 清理Fresco图片缓存，避免OOM
                    try {
                        Fresco.getImagePipeline().clearMemoryCaches();
                        // 强制GC
                        System.gc();
                        TLog.info(TAG, "Fresco memory cache cleared successfully");
                    } catch (Exception e) {
                        TLog.error(TAG, "Failed to clear Fresco memory cache: " + e.getMessage());
                    }
                } else {
                    TLog.info(TAG, "onTrimMemory [%d]", level);
                }
            }
        });
    }

    /**
     * 初始化充电广播接收
     */
    private void initDexCompileReceiver() {
        // android O以下不做处理
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            return;
        }
        // 已经编译的版本，不再继续执行
        if (mCompileIdleHandler.compliedVersion()) {
            return;
        }

        IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        registerReceiver(mChargeReceiver, intentFilter);
        hasRegisterReceiver = true;
    }


    /**
     * 登录态时，每次展示在前台都进行上报
     */
    public void pointOnForeground() {
        try {
            if (HiKernel.getHikernel().getAccount() != null && StringUtils.isNotEmpty(HiKernel.getHikernel().getAccount().getUserId())) {
                PointUtils.BuilderV4 builder = new PointUtils.BuilderV4();
                builder.params("cpuType", SysUtil.is64Bit() ? 64 : 32);
                builder.name("user-active").withSafeParamsWithoutLocation().point();
            }
        } catch (Exception e) {
            // 未初始化Hikernel时，说明没有登录,不用上报
        }
    }

    @Override
    public void onAppBackground() {
        TLog.info(TAG, "onBackground");
        NetworkHelper.setProcessState(false);
        mColdLaunch = false;
        MainThreadMonitor.getInstance().stop();
        // 若启动时中途退出到后台，则不统计此次启动时长
        PointClearUtils.INSTANCE.resetOnBackground();

        PushSdkManager.getInstance().setForeground(false);
        AppLifecycleService.setForeground(false);
        initDexCompileReceiver();
        //如果应用切换到后台或者其它应用，内部的网速统计就没有意义了
        NetSpeedUtils.trafficReset();

        if (TextUtils.equals(ProcessUtil.getProcessName(), getApplication().getPackageName())) {
            if (mSensorManager != null) {
                mSensorManager.unregisterListener(this);
            }

            if (mBatteryReceiver != null) {
                unregisterReceiver(mBatteryReceiver);
            }
        }
    }

    private RemoveLaunchPointRunnable mRemoveLaunchPointRunnable = new RemoveLaunchPointRunnable();

    @Override
    public void onSensorChanged(SensorEvent event) {
        if (event.sensor.getType() == Sensor.TYPE_ACCELEROMETER) {
            // 获取加速度值
            float x = event.values[0];
            float y = event.values[1];
            float z = event.values[2];

            float accelerationMagnitude = (float) Math.sqrt(x * x + y * y + z * z);

            float linearAccelerationMagnitude = accelerationMagnitude - SensorManager.STANDARD_GRAVITY;

            // 如果线性加速度的大小超过设定的阈值，则认为设备在移动
            if (linearAccelerationMagnitude > 1.0f) {
                AttendanceRepository.mIsMoved = true;
                mSensorManager.unregisterListener(this);
            }
        } else if (event.sensor.getType() == Sensor.TYPE_GYROSCOPE) {
            // 获取陀螺仪数据：绕X、Y、Z轴的旋转速率
            float angularX = event.values[0];
            float angularY = event.values[1];
            float angularZ = event.values[2];

            // 计算总的旋转速率（模）
            float angularRateMagnitude = (float) Math.sqrt(angularX * angularX + angularY * angularY + angularZ * angularZ);

            // 如果旋转速率超过设定的阈值，则认为设备在旋转
            if (angularRateMagnitude > 0.1f) {
                AttendanceRepository.mIsMoved = true;
                mSensorManager.unregisterListener(this);
            }
        }
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {

    }

    /**
     * 移除埋点
     */
    public class RemoveLaunchPointRunnable implements Runnable {

        @Override
        public void run() {
            PointClearUtils.INSTANCE.resetLaunchPoint();
        }
    }

}
