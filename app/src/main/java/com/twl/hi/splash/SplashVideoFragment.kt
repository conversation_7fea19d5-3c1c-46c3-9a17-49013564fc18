package com.twl.hi.splash

import androidx.lifecycle.Observer
import com.techwolf.lib.tlog.TLog
import com.twl.hi.R
import com.twl.hi.BR
import com.twl.hi.HiSplashActivity
import com.twl.hi.databinding.FragmentSplashVideoBinding
import com.twl.hi.foundation.base.fragment.FoundationVMFragment
import com.twl.hi.foundation.facade.ProfileRepository
import com.twl.hi.splash.callback.SplashVideoCallback
import com.twl.hi.splash.viewmodel.SplashVideoViewModel
import com.twl.hi.video.view.OnPlayErrorListener
import com.twl.hi.video.view.OnProgressListener
import java.io.File

class SplashVideoFragment :
    FoundationVMFragment<FragmentSplashVideoBinding, SplashVideoViewModel>(), SplashVideoCallback {

    companion object {
        const val TAG = "SplashVideoFragment"
        fun newInstance() = SplashVideoFragment()
    }

    override fun getContentLayoutId() = R.layout.fragment_splash_video

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel

    override fun initFragment() {
        super.initFragment()
        TLog.info(TAG, "initFragment")
        initVideoScaleType();

        lifecycle.addObserver(dataBinding.videoView)
        dataBinding.videoView.setLoop(false)
        dataBinding.videoView.mIsNoMenuMode = true
        dataBinding.videoView.setVideoDownloadProgress(100)

        viewModel.videoPlayStatus.observe(this, Observer {
            TLog.info(TAG, "videoPlayStatus change to $it")
            when (it) {
                SplashVideoViewModel.playIng -> {
                }

                SplashVideoViewModel.playEnd -> {

                }

                SplashVideoViewModel.playAgain -> {
                    dataBinding.skipBtn.setProgress(0f)
                    dataBinding.videoView.reStartPlay()
                }

                SplashVideoViewModel.playExit -> {
                    goToMainPage()
                }
            }
        })

        dataBinding.videoView.setOnPlayErrorListener(object : OnPlayErrorListener {
            override fun onError(eventId: Int) {
                TLog.info(TAG, "onError eventId $eventId")
                goToMainPage()
            }
        })
        dataBinding.videoView.setOnProgressListener(object : OnProgressListener {
            override fun onProgressPlaying(progress: Long, duration: Long) {
                dataBinding.skipBtn.setProgress(progress * 1.0f / duration)
            }

            override fun onProgressPlayEnd() {
                dataBinding.skipBtn.setProgress(1f)
                viewModel.videoPlayStatus.postValue(SplashVideoViewModel.playEnd)
            }

            override fun onProgressPlayBegin() {
                viewModel.videoPlayStatus.postValue(SplashVideoViewModel.playIng)
            }

            override fun onProgressBufferEnd() {
            }

            override fun onProgressBuffering() {
            }
        })

        ProfileRepository.sSplashVideoPath.value.let {
            when (File(it).exists()) {
                true -> {
                    viewModel.setVideoPath(it)
                }

                false -> {
                    TLog.error(TAG, "file not exist!")
                    goToMainPage()
                }
            }
        }
    }

    /**
     * 设置视频拉伸方向
     */
    private fun initVideoScaleType() {
        activity?.let { activity ->
            dataBinding.videoView.setScaleTypeFill()
        }
    }

    private fun goToMainPage() {
        dataBinding.videoView.pause()
        var activityLocal = activity
        val isHiSplashActivity = activityLocal is HiSplashActivity
        if (isHiSplashActivity) {
            TLog.info(TAG, "goToMainPage ")
            val hiSplashActivity = activityLocal as HiSplashActivity;
            hiSplashActivity.onComplete()
        }
    }

}