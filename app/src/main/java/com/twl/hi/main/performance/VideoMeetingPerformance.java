package com.twl.hi.main.performance;

import android.os.Handler;
import android.os.Message;
import android.os.SystemClock;
import android.text.format.DateUtils;
import android.view.Gravity;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.Observer;

import com.sankuai.waimai.router.Router;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.BR;
import com.twl.hi.R;
import com.twl.hi.basic.util.PointParamsUtils;
import com.twl.hi.databinding.PopInvitationBinding;
import com.twl.hi.export.video.meeting.router.VideoAndAudioPageRouter;
import com.twl.hi.export.video.meeting.service.VideoMeetingEntranceHelper;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.response.MeetingCheckResponse;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.JoinScheduleVideoMeeting;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.main.MainTabActivity;
import com.twl.hi.videomeeting.api.request.MeetingCheckInMeetingRequest;
import com.twl.hi.videomeeting.api.response.MeetingCheckInMeetingResponse;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import lib.twl.common.util.PointClearUtils;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.widget.HiPopupWindow;

/**
 * 视频会议邀请弹框相关逻辑
 *
 */
public class VideoMeetingPerformance extends BaseMainPerformance{
    //默认时间
    private static final long MEETING_INVITATION_TIMEOUT = 5 * DateUtils.MINUTE_IN_MILLIS;
    //实际时间，可能动态下发更改
    public static long MeetingInvitationTimeOut = MEETING_INVITATION_TIMEOUT;

    private HiPopupWindow invitationPopWindow;
    private PopInvitationBinding invitationBinding;
    /**
     * 记录当前会议邀请记录，主要是用于OnResume时重置handler的延时关闭任务
     * 因为简单使用Handler计时在锁屏后变得不准确或者不执行，其它场景也可能存在问题
     */
    private JoinScheduleVideoMeeting mJoinScheduleVideoMeeting;
    private Handler mHandler;

    public VideoMeetingPerformance(Handler mHandler,MainTabActivity activity) {
        super(activity);
        this.mHandler = mHandler;
    }


    @Override
    public void onCreate(@NonNull LifecycleOwner owner) {
        super.onCreate(owner);
        ServiceManager.getInstance().getScheduleService().getVideoMeetingInvitation().observe(owner, invitation -> {
            /*
             * 订阅入会邀请的事件，当收到入会邀请时需在主页面弹出邀请弹窗
             *
             * 当入会邀请的状态为会议进行中并且会议开始在 1 分钟内才弹窗，弹窗后 1 分钟自动隐藏
             */
            if (invitation == null || invitation.status != 1) {
                hideMeetingInvitation(invitation);
            } else {
                long elapsed = Math.max(0, System.currentTimeMillis() - invitation.timestamp);
                if (elapsed < MeetingInvitationTimeOut) {
                    showMeetingInvitation(invitation);
                    mHandler.removeMessages(MainTabActivity.MESSAGE_WHAT_MEETING_INVITATION);
                    Message message = mHandler.obtainMessage(MainTabActivity.MESSAGE_WHAT_MEETING_INVITATION, invitation);
                    mHandler.sendMessageDelayed(message, MeetingInvitationTimeOut);
                    mJoinScheduleVideoMeeting = invitation;
                    mJoinScheduleVideoMeeting.closeTime = SystemClock.elapsedRealtime() + MeetingInvitationTimeOut;
                } else {
                    hideMeetingInvitation(invitation);
                }
            }
        });
    }

    @Override
    public void onResume(@NonNull LifecycleOwner owner) {
        super.onResume(owner);
        checkCloseVideoInviteDialog();
    }

    /**
     * 入会邀请优先级高于加急弹窗，对 PopupWindow 使用的 WindowLayoutType 大于加急弹窗的 WindowLayoutType
     */
    private void showMeetingInvitation(@NonNull JoinScheduleVideoMeeting join) {
        if (invitationPopWindow != null && invitationPopWindow.isShowing() && invitationBinding != null) {
            JoinScheduleVideoMeeting present = invitationBinding.getInvitation();
            if (present != null && present.roomId.equals(join.roomId)) {
                return;
            }
        }
        checkMeeting();
        TLog.info(MainTabActivity.TAG, "显示入会提醒，" + join.roomId);
        if (invitationPopWindow == null) {
            invitationBinding = PopInvitationBinding.inflate(activity.getLayoutInflater());
            invitationBinding.reject.setOnClickListener(v -> rejectMeeting(invitationBinding.getInvitation()));
            invitationBinding.accept.setOnClickListener(v -> joinMeeting(invitationBinding.getInvitation()));
            invitationPopWindow = new HiPopupWindow.Builder(activity)
                    .setAnimationStyle(R.style.pop_anim_style_alpha)
                    .setContentView(invitationBinding.getRoot())
                    .setShadow(activity.getWindow(), 1.0f)
                    .setFocusable(false)
                    .setOutsideTouchable(false)
                    .build();
            // 保证弹窗层级在加急消息之上
            invitationPopWindow.getPopupWindow().setWindowLayoutType(WindowManager.LayoutParams.TYPE_APPLICATION_PANEL + 2);
        }
        PointClearUtils.INSTANCE.resetLaunchPoint();
        PointClearUtils.INSTANCE.resetLoginPoint();
        invitationBinding.setInvitation(join);
        invitationPopWindow.showAtLocation(activity.getWindow().getDecorView(), Gravity.TOP, QMUIDisplayHelper.dpToPx(25), QMUIDisplayHelper.dpToPx(25));
    }

    public void hideMeetingInvitation(JoinScheduleVideoMeeting join) {
        hideMeetingInvitation(join, null);
    }


    private void checkMeeting() {
        HttpExecutor.execute(new MeetingCheckInMeetingRequest(new BaseApiRequestCallback<MeetingCheckInMeetingResponse>() {

            @Override
            public void onSuccess(ApiData<MeetingCheckInMeetingResponse> data) {
                invitationBinding.setVariable(BR.isInMeeting, data.resp.getInMeeting());
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        }));
    }

    private boolean hideMeetingInvitation(@Nullable JoinScheduleVideoMeeting join, @Nullable Boolean accepted) {
        if (invitationPopWindow != null && invitationPopWindow.isShowing() && invitationBinding != null) {
            JoinScheduleVideoMeeting present = invitationBinding.getInvitation();
            if (join == null || (present != null && present.roomId.equals(join.roomId))) {
                mHandler.removeMessages(MainTabActivity.MESSAGE_WHAT_MEETING_INVITATION);
                TLog.info(MainTabActivity.TAG, "隐藏入会提醒，" + (join == null ? "主动隐藏" : join.roomId));
                new PointUtils.BuilderV4()
                        .name("calendar-meeting-popup")
                        .params("type", accepted == null ? "none" : accepted ? "yes" : "no")
                        .point();
                invitationPopWindow.dismiss();
                return true;
            }
        }
        return false;
    }

    private void rejectMeeting(JoinScheduleVideoMeeting meeting) {
        getViewModel().rejectVideoMeetingInvitation(meeting.roomId);
        if (hideMeetingInvitation(meeting, false)) {
            ServiceManager.getInstance().getScheduleService().getVideoMeetingInvitation().postValue(null);
        }
    }

    private void joinMeeting(JoinScheduleVideoMeeting meeting) {
        LiveData<Boolean> checkClose = getViewModel().checkClose();
        if (checkClose == null) {
            innerJoinMeeting(meeting);
        } else {
            checkClose.observe(activity, new Observer<Boolean>() {
                @Override
                public void onChanged(Boolean aBoolean) {
                    innerJoinMeeting(meeting);
                }
            });
        }
    }

    private void innerJoinMeeting(JoinScheduleVideoMeeting meeting) {
        Router.getService(VideoMeetingEntranceHelper.class, VideoAndAudioPageRouter.SERVICE_VIDEO_MEETING_ENTRANCE)
                .joinVideoMeeting(activity, meeting.roomId, "", new VideoMeetingEntranceHelper.BootCallback() {
                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFail() {

                    }

                    @Override
                    public void onSuccess() {
                        PointParamsUtils.push(
                                PointParamsUtils.SOURCE_ENTER_MEETING,
                                MeetingCheckResponse.SOURCE_REMINDER
                        );
                    }
                });
        if (hideMeetingInvitation(meeting, true)) {
            ServiceManager.getInstance().getScheduleService().getVideoMeetingInvitation().postValue(null);
        }
    }

    /**
     * 检查是否需要关闭视频会议邀请弹窗
     * 因为会议邀请弹窗的关闭是由handler延时发送的，会出现锁屏时延时不准确甚至不准确的问题
     */
    private void checkCloseVideoInviteDialog() {
        if (invitationPopWindow != null
                && invitationPopWindow.isShowing()) {
            if (SystemClock.elapsedRealtime() > mJoinScheduleVideoMeeting.closeTime) { //超过关闭时间,直接关闭
                invitationPopWindow.dismiss();
            } else { //未超过关闭时间, 重置延时关闭消息
                mHandler.removeMessages(MainTabActivity.MESSAGE_WHAT_MEETING_INVITATION);
                Message message = mHandler.obtainMessage(MainTabActivity.MESSAGE_WHAT_MEETING_INVITATION, mJoinScheduleVideoMeeting);
                mHandler.sendMessageDelayed(message, mJoinScheduleVideoMeeting.closeTime - SystemClock.elapsedRealtime());
            }
        }
    }

}
