package com.twl.hi.main.performance

import android.os.Bundle
import android.text.TextUtils
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.twl.hi.export.chat.router.ChatPageRouter
import com.twl.hi.export.organization.router.OrganizationPageRouter
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.main.MainTabActivity
import com.twl.hi.main.model.ConversationVisibleBean
import com.twl.utils.SettingBuilder
import hi.kernel.BundleConstants
import hi.kernel.HiKernel
import lib.twl.common.util.AppUtil

class ChatPerformance(act: MainTabActivity) : BaseMainPerformance(act) {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        ServiceManager.getInstance().groupService.initContactToGroupCache()
        if (TextUtils.equals(
                HiKernel.getHikernel().account.companyId,
                SettingBuilder.getInstance().twlDefaultId
            )
        ) {
            ServiceManager.getInstance().redCircleService.friendApplyLiveData.observe(activity) { response ->
                activity.setMainUnReadCount(response.`in`)
            }
        }
        viewModel.conversationVisibleLiveData.observe(activity) { conversationVisibleBean: ConversationVisibleBean? ->
            if (conversationVisibleBean != null) {
                val bundle = Bundle()
                if (conversationVisibleBean.chatType == MessageConstants.MSG_SINGLE_CHAT) {
                    if (conversationVisibleBean.isVisible) { //单聊可见范围跳转会话
                        bundle.putString(
                            BundleConstants.BUNDLE_USER_ID,
                            conversationVisibleBean.chatId
                        )
                        AppUtil.startUri(
                            activity,
                            ChatPageRouter.SINGLE_CHAT_ACTIVITY,
                            bundle
                        )
                    } else { //单聊不可见范围跳转个人信息
                        bundle.putString(
                            BundleConstants.BUNDLE_DATA_LONG,
                            conversationVisibleBean.chatId
                        )
                        AppUtil.startUri(
                            activity,
                            OrganizationPageRouter.USER_INFO_ACTIVITY,
                            bundle
                        )
                    }
                } else { //群聊
                    bundle.putString(
                        BundleConstants.BUNDLE_CHAT_ID,
                        conversationVisibleBean.chatId
                    )
                    AppUtil.startUri(
                        activity,
                        ChatPageRouter.GROUP_CHAT_ACTIVITY,
                        bundle
                    )
                }
            }
        }
    }
}