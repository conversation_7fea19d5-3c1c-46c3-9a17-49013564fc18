package com.twl.hi;

import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.database.sqlite.SQLiteException;
import android.net.ConnectivityManager;
import android.net.Network;
import android.os.Build;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import com.bzl.dz.autotest.localserver.ATLocalServerManager;
import com.bzl.sdk.voice.VoiceConfig;
import com.bzl.sdk.voice.VoiceManager;
import com.bzl.sdk.voice.interfaces.Supplier;
import com.facebook.drawee.backends.pipeline.DraweeConfig;
import com.facebook.imagepipeline.decoder.ImageDecoderConfig;
import com.github.piasy.biv.BigImageViewer;
import com.github.piasy.biv.loader.fresco.FrescoImageLoader;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.components.DefaultLogger;
import com.sankuai.waimai.router.core.Debugger;
import com.sankuai.waimai.router.core.UriCallback;
import com.sankuai.waimai.router.core.UriInterceptor;
import com.sankuai.waimai.router.core.UriRequest;
import com.techwolf.lib.tlog.TLog;
import com.techwolf.lib.tlog.config.TLogConfig;
import com.techwolf.lib.tlog.logs.ILog;
import com.tencent.bugly.crashreport.CrashReport;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.twl.hi.basic.api.config.base.HttpClientInit;
import com.twl.hi.basic.util.EncryptUtil;
import com.twl.hi.config.UrlConfigManager;
import com.twl.hi.debugger.XDebugger;
import com.twl.hi.export.video.meeting.router.VideoAndAudioPageRouter;
import com.twl.hi.foundation.api.base.BaseConfigParam;
import com.twl.hi.foundation.api.base.HostConfig;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.facade.SyncDispatch;
import com.twl.hi.foundation.facade.VerifySdkHelper;
import com.twl.hi.foundation.helper.HostSwitchHelper;
import com.twl.hi.foundation.logic.AttendanceService;
import com.twl.hi.foundation.logic.DatabaseService;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.push.PushRegisterToken;
import com.twl.hi.foundation.ui.Navigation;
import com.twl.hi.foundation.utils.Singleton;
import com.twl.hi.foundation.utils.sensitive.DesensitizeLog;
import com.twl.hi.kzmp.BossHiModule;
import com.twl.hi.libsecret.SecretManager;
import com.twl.hi.login.LoginActivity;
import com.twl.hi.login.TeamSelectActivity;
import com.twl.hi.main.MainTabActivity;
import com.twl.hi.manager.ApmManager;
import com.twl.hi.monitor.CrashHandler;
import com.twl.hi.monitor.MainThreadMonitor;
import com.twl.hi.push.PushSdkManager;
import com.twl.hi.router.Config;
import com.twl.hi.router.HiGlobalOnCompleteListener;
import com.twl.hi.router.base.RouterBaseConstant;
import com.twl.hi.router.handler.ActivityDefaultHandler;
import com.twl.hi.router.handler.HiRootUriHandler;
import com.twl.hi.router.intercepter.ReplaceDefaultHandlerOfPathHandlerInterceptor;
import com.twl.hi.utils.NotifyUtil;
import com.twl.hi.verify.VerifySdkConfigImpl;
import com.twl.hi.webview.WebViewDataLock;
import com.twl.hi.webview.router.handler.WebViewHandler;
import com.twl.http.HttpExecutor;
import com.twl.http.config.HttpConfig;
import com.twl.http.httpswitch.HttpSwitchManager;
import com.twl.kzmp.KZMP;
import com.twl.kzmp.module.FoundationModule;
import com.twl.kzmp.module.scaffold.WIFIExtKt;
import com.twl.kzmp.trace.KZTraceKt;
import com.twl.monitor.DebugAccountLifecycle;
import com.twl.startup.Startup;
import com.twl.startup.StartupLifecycle;
import com.twl.utils.SettingBuilder;
import com.twl.utils.network.NetSpeedUtils;
import com.twl.utils.network.NetworkHelper;
import com.twl.utils.process.ProcessUtil;
import hi.kernel.Account;
import hi.kernel.Constants;
import hi.kernel.HiKernel;
import hi.kernel.ProcessInfo;
import hi.kernel.api.AccountLifecycle;
import io.dcloud.common.util.AESHelper;
import lib.twl.common.AppBuildConfig;
import lib.twl.common.LBase;
import lib.twl.common.activity.AsyncInflateManager;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.util.*;
import lib.twl.common.util.image.fresco.BHCacheKeyFactory;
import lib.twl.common.util.level.DeviceYearClass;

import java.io.File;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeoutException;

/**
 * 启动任务管理类，封装类一些app启动时需要执行的任务
 */
public final class HiStartup {

    public static final String TAG = "HiStartup";

    public static final String TAG_APM = "apm";

    private static final long WAIT_PERMISSION_TIME = 50;

    private final CountDownLatch countDownLatch = new CountDownLatch(2);

    private static Singleton<HiStartup> hiStartupSingleton = new Singleton<HiStartup>() {
        @Override
        protected HiStartup create() {
            return new HiStartup();
        }
    };

    public static HiStartup getInstance() {
        return hiStartupSingleton.get();
    }

    // 主页启动后会自动加载的view布局
    private final int[] mAsyncInflateLayouts = new int[]{
            //主页
            R.layout.activity_main_tab,
            //f1会话
            R.layout.chat_fragment_conversation_main_tab,

            //我的
            R.layout.me_fragment_drawer,
            //个人设置
            R.layout.schedule_fragment_schedule_drawer_setting,
            //日历
            R.layout.schedule_main_fragment,
            //联系人
            R.layout.organization_fragment_contacts,
            //工作台
            R.layout.work_fragment,
    };

    private HiStartup() {
    }

    /**
     * 初始化入口
     *
     * @param application
     */
    public void init(Application application) {
        Startup.StartupStrategy startupStrategy = new Startup.StartupStrategy(HiSplashActivity.class);
        startupStrategy.withLifecycle(mStartupLifecycle);
        Startup.initialize(application, ProcessUtil.getProcessName(), startupStrategy);
    }

    private StartupLifecycle mStartupLifecycle = new StartupLifecycle() {
        private boolean isMainProcess;

        @Override
        public void onAppCreate(boolean isMainProcess) {
            this.isMainProcess = isMainProcess;
            Application context = (Application) ProcessHelper.getContext();
            new SettingBuilder.Builder()
                    .setFlavor(BuildConfig.FLAVOR)
                    .setDebug(BuildConfig.DEBUG)
                    .setType(BuildConfig.BUILD_TYPE)
                    .setVersionCode(BuildConfig.VERSION_CODE)
                    .setVersionName(BuildConfig.VERSION_NAME)
                    .setBuildNumber(BuildConfig.BUILD_NUMBER) // 热修包上报热修构建号, 原始包上报原始构建号
                    .setConfigType(BuildConfig.CONFIG)
                    .build();
            initRouter(context);
            //日志初始化
            initTLog(context);
            NetworkHelper.INSTANCE.init(context);
            NetSpeedUtils.init(context);
            // 记录崩溃信息
            Thread.setDefaultUncaughtExceptionHandler(new CrashHandler(Thread.getDefaultUncaughtExceptionHandler()));
            TLog.info(TAG, "onAppCreate version[%s] flavor[%s] buildType[%s] processName[%s], jenkinsBuild[%b], buildTime[%s], buildNumber[%s], buildNumberMaster[%s]",
                    BuildConfig.VERSION_NAME, BuildConfig.FLAVOR, BuildConfig.BUILD_TYPE, ProcessUtil.getProcessName(), BuildConfig.JENKINS_BUILD,
                    BuildConfig.BUILD_TIME, BuildConfig.BUILD_NUMBER, getRealBuildNumber());
            if (isMainProcess) {
                // 处理 webview_data.lock 文件
                WebViewDataLock.handleWebViewDataFile(context);
                VoiceConfig config = new VoiceConfig.Builder()
                        .enableLog(true)
                        .ticketNameSupplier(new Supplier<String>() {
                            @NonNull
                            @Override
                            public String get() {
                                return "asr-token";
                            }
                        })
                        .ticketValueSupplier(new Supplier<String>() {
                            @NonNull
                            @Override
                            public String get() {
                                return "3b4c49a7b8a310b1631d93b66c3e3754f4d6f60728ecb2d3040ec97ef2a07904";
                            }
                        }).build();
                VoiceManager.init(config);
                initAndroid10InternalNetworkReceiver(context);
                // 初始化自动化测试本地服务，需要在主进程下初始化
                if (BuildConfig.DEBUG) {
                    ATLocalServerManager.init(context);
                }
                XDebugger.INSTANCE.initXKit();
                HttpConfig.getInstance().interceptorList.add(XDebugger.INSTANCE.qaInterceptor());
            } else {
                KZMP.registerModule("BossHiModule", BossHiModule.class);
                KZMP.registerModule("KZMPModule", FoundationModule.class);
            }
        }

        @Override
        public void onUpgrade() {

        }

        @Override
        public void onBackground() {
            TLog.info(TAG, "HiStartup -> onBackground start " + ProcessUtil.getProcessName());
            Process.setThreadPriority(Process.THREAD_PRIORITY_FOREGROUND);
            Application context = (Application) ProcessHelper.getContext();
            // url 的配置，必须是第一位的！！！网络相关配置依赖此项
            UrlConfigManager.initConfig(context);
            initLBase(context);
            // 依赖 HiKernel 以及 Service 业务的一些初始化

            if (isMainProcess) {
                initUploader();
                initHttpSwitch();
                ExecutorFactory.addErrorListener(new ExecutorFactory.ExecErrorListener() {
                    @Override
                    public void onError(Throwable e) {
                        Throwable cause = e.getCause();
                        String message = cause == null ? e.getMessage() : cause.getMessage();
                        if (message != null && (message.contains(Constants.WCDB_ERROR) || message.contains(Constants.WCDB_DISK_MALFORMED)) && cause instanceof SQLiteException) {
                            SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_DATA_BASE_ERROR);
                        }
                    }
                });
                ExecutorFactory.execWorkTask(new Runnable() {
                    @Override
                    public void run() {
                        // try catch避免出现异常导致无法进入首页
                        try {
                            // 第三方 SDK 以及部分剩余业务初始化
                            initBigImageViewerSDK(context);
                            Navigation.initialized(new Navigation.Info().setHomeClass(MainTabActivity.class).setLoginClass(LoginActivity.class).setCompanyClass(TeamSelectActivity.class));

                            DeviceYearClass.getInstance().initDeviceLevel(context);
                            AsyncInflateManager.getInstance().init(context);

                            FrameLayout frameLayout = new FrameLayout(context);
                            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                            frameLayout.setLayoutParams(params);
                            // item inflate需要父view设置宽度
                            AsyncInflateManager.getInstance().asyncRepeatInflate(R.layout.chat_item_conversation, frameLayout, getRepeatNumByLevel(DeviceYearClass.getInstance().getDeviceLevel()));
                            AsyncInflateManager.getInstance().asyncInflate(mAsyncInflateLayouts, null);

                            SecretManager.checkEnv(context);
                            printPermissionList(context);

                            TLog.info(TAG, "Devices info model[%s] brand[%s] details[%s] rom version[%s]", Build.MODEL, Build.BRAND, Build.VERSION.BASE_OS, System.getProperty("os.version"));
                        } catch (Exception e) {
                            // 程序出现异常，主动退出
                            ProcessHelper.getToolPreferences().putString(Constants.KEY_APP_ERROR_OCCUR_WHEN_START_UP, LDate.getDate());
                            TLog.error(TAG, e.getMessage());
                            System.exit(0);
                            return;
                        }
                        countDownLatch.countDown();
                        TLog.info(TAG, "first task finish.");
                    }
                });

                ExecutorFactory.execWorkTask(new Runnable() {
                    @Override
                    public void run() {
                        // try catch避免出现异常导致无法进入首页
                        try {
//                            Router.lazyInit();
                            registerWeChat();
                            // bugly，网络等初始化完再执行才能监控
                            ExecutorFactory.execMainTask(new Runnable() {
                                @Override
                                public void run() {
                                    // 需要在主线程初始化对象
                                    MainThreadMonitor.getInstance().start();
                                }
                            });
                            SecretManager.checkEnv(context);
                        } catch (Exception e) {
                            // 程序出现异常，主动退出
                            ProcessHelper.getToolPreferences().putString(Constants.KEY_APP_ERROR_OCCUR_WHEN_START_UP, LDate.getDate());
                            TLog.error(TAG, e.getMessage());
                            System.exit(0);
                            return;
                        }
                        countDownLatch.countDown();
                        TLog.info(TAG, "second task finish.");
                    }
                });

                ExecutorFactory.execMainTask(new Runnable() {
                    @Override
                    public void run() {
                        initApm(context);
                        ExecutorFactory.execWorkTask(new Runnable() {
                            @Override
                            public void run() {
                                ApmManager.sync();
                            }
                        });
                    }
                });

                // 安全校验sdk初始化
                VerifySdkHelper.init(VerifySdkConfigImpl.get());

                try {
                    kz.log.TLog.INSTANCE.init(new kz.log.ILog() {
                        @Override
                        public void e(@NonNull String s, @NonNull Throwable throwable, @NonNull String s1) {
                            TLog.error(s, throwable, s1);
                        }

                        @Override
                        public void d(@NonNull String tag, @NonNull String content) {
                            TLog.debug(tag, content);
                        }

                        @Override
                        public void e(@NonNull String tag, @NonNull String content) {
                            TLog.error(tag, content);
                        }

                        @Override
                        public void i(@NonNull String tag, @NonNull String content) {
                            TLog.info(tag, content);
                        }

                        @Override
                        public void w(@NonNull String tag, @NonNull String content) {
                            TLog.important(tag, content);
                        }
                    });
                    // 当前主要耗时阶段，触发 service 中账号，公司相关初始化
                    HiKernel.initialized(new ProcessInfo() {
                        @Override
                        public List<AccountLifecycle> getLifecycleList() {
                            return Arrays.asList(ServiceManager.getInstance().getAccountLifecycle(), DebugAccountLifecycle.getInstance());
                        }
                    });
                    ExecutorFactory.execMainTask(new Runnable() {
                        @Override
                        public void run() {
                            HttpSwitchManager.setHttpConfig(ServiceManager.getInstance().getSystemGrayConfigService().getSystemGrayConfig());
                        }
                    });
                    TLog.info(TAG, "HiKernel.initialized success!");

                    // 恢复聊天附件缓存文件（解决重启后图片显示问题）
                    ExecutorFactory.execWorkTask(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ServiceManager.getInstance().getChatAttachmentService().restoreAttachmentCaches();
                                TLog.info(TAG, "Chat attachment caches restoration initiated");
                            } catch (Throwable e) {
                                TLog.error(TAG, "Failed to restore attachment caches: " + e.getMessage());
                            }
                        }
                    });

                } catch (Throwable e) {
                    TLog.error(TAG, e.getMessage());
                    CrashReport.postCatchedException(e);
                }
                ExecutorFactory.execWorkTask(new Runnable() {
                    @Override
                    public void run() {
                        // 触发进入前台埋点
                        App app = (App) context;
                        app.pointOnForeground();
                        // 依赖 HiKernel
                        AttendanceService.autoAttendance();
                        //依赖 HiKernel
                        initPushSdk(context);
                        // initPushSdk 一定要在 NotifyUtil.registerNotify(); 之前
                        NotifyUtil.registerNotify();
                        // 处理小米的推送点击埋点
                        MiPushClickPointManager.getInstance().miPushMsgClickPoint();
                        TLog.info(TAG, "startup success!");
                    }
                });
                try {
                    /**
                     * 等待所有任务初始化完成再结束
                     * 执行结束后会进入 MainTabActivity
                     */
                    countDownLatch.await();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else {
                try {
                    //当前主要耗时阶段，触发service中账号，公司相关初始化
                    HiKernel.initialized(new ProcessInfo() {
                        @Override
                        public List<AccountLifecycle> getLifecycleList() {
                            // 子进程只初始化账号相关数据，不做后置其余操作
                            return new ArrayList<>();
                        }
                    });
                    if (ProcessHelper.getProcessName().contains("kzmp")) {
                        kz.log.TLog.INSTANCE.init(new kz.log.ILog() {
                            @Override
                            public void e(@NonNull String s, @NonNull Throwable throwable, @NonNull String s1) {
                                TLog.error(s, throwable, s1);
                            }

                            @Override
                            public void d(@NonNull String tag, @NonNull String content) {
                                TLog.debug(tag, content);
                            }

                            @Override
                            public void e(@NonNull String tag, @NonNull String content) {
                                TLog.error(tag, content);
                            }

                            @Override
                            public void i(@NonNull String tag, @NonNull String content) {
                                TLog.info(tag, content);
                            }

                            @Override
                            public void w(@NonNull String tag, @NonNull String content) {
                                TLog.important(tag, content);
                            }
                        });
                    }
                    KZTraceKt.initMPTrace(context, BuildConfig.DEBUG);
                    if (ProcessHelper.getProcessName().contains("vpn")) {
                        ServiceManager.getInstance().getVpnService().registerLogImpl();
                    }
                    ExecutorFactory.execWorkTask(new Runnable() {
                        @Override
                        public void run() {
                            initBigImageViewerSDK(context);
                        }
                    });
                } catch (Throwable e) {
                    TLog.error(TAG, e.getMessage());
                    CrashReport.postCatchedException(e);
                }
            }
            ExecutorFactory.execWorkTask(new Runnable() {
                @Override
                public void run() {
                    initBugly(context);
                    ignoreFinalizeTimeOut();
                    if (BuildConfig.DEBUG) {
                        initDebugDatabase(DatabaseService.DATABASE_NAME,
                                (ProcessHelper.getCompanyRoot() != null ? ProcessHelper.getCompanyRoot() : ProcessHelper.getUserRoot()) +
                                        File.separator + DatabaseService.DATABASE_NAME);
                    }
                }
            });
            TLog.info(TAG, "HiStartup Success! " + ProcessUtil.getProcessName());
        }

        @Override
        public boolean onComplete() {
            return false;
        }
    };


    private void initUploader(){
        // 初始化上传器工厂（解决账号切换时上传器丢失问题）
        ExecutorFactory.execWorkTask(new Runnable() {
            @Override
            public void run() {
                try {
                    com.twl.hi.basic.helpers.UploaderFactoryImpl uploaderFactory =
                            new com.twl.hi.basic.helpers.UploaderFactoryImpl();
                    com.twl.hi.foundation.logic.ChatAttachmentService.setUploaderFactory(uploaderFactory);
                    TLog.info(TAG, "Uploader factory initialized");
                } catch (Throwable e) {
                    TLog.error(TAG, "Failed to initialize uploader factory: " + e.getMessage());
                }
            }
        });
    }

    private void initHttpSwitch() {
        HttpSwitchManager.init(HostConfig.getCONFIG().getApiAddr(),
                Arrays.asList(404),
                Arrays.asList(
                        HostConfig.API_PATH_PREFIX + URLConfig.URL_SMS_LOGIN,
                        HostConfig.API_PATH_PREFIX + URLConfig.URL_GEE_CAPTCHA_REGISTER,
                        HostConfig.API_PATH_PREFIX + URLConfig.URL_GEE_CAPTCHA_VALIDATE,
                        HostConfig.API_PATH_PREFIX + URLConfig.URL_CLOCK_GET,
                        HostConfig.API_PATH_PREFIX + URLConfig.URL_CLOCK_GET_CARE_CONFIG,
                        HostConfig.API_PATH_PREFIX + URLConfig.URL_CLOCK_UPDATE,
                        HostConfig.API_PATH_PREFIX + URLConfig.URL_CLOCK_SURVEY_SUBMIT
                ));
        HttpSwitchManager.INSTANCE.setListener(new HttpSwitchManager.OnHttpHostSwitchListener() {
            @NonNull
            @Override
            public String onHttpHostCheckSwitch(@NonNull HttpSwitchManager.Strategy strategy, @NonNull HttpSwitchManager.Strategy currentState) {
                String oldHost = HostConfig.getCONFIG().getApiAddr().replaceAll("https://", "");
                HostConfig.moveNextApiHost();
                String newAddr = HostConfig.getCONFIG().getApiAddr();
                ServiceManager.getInstance().getEmailService().updateMailDomain(newAddr);
                String newHost = newAddr.replaceAll("https://", "");
                HostSwitchHelper.pointHostSwitch(
                        HostSwitchHelper.REASON_SWITCH_CHECK,
                        oldHost,
                        newHost,
                        strategy,
                        currentState
                );
                return newHost;
            }

            @NonNull
            @Override
            public String onHttpHostLoginSwitch() {
                String oldHost = HostConfig.getCONFIG().getApiAddr().replaceAll("https://", "");
                HostConfig.moveNextApiHost();
                String newAddr = HostConfig.getCONFIG().getApiAddr();
                ServiceManager.getInstance().getEmailService().updateMailDomain(newAddr);
                String newHost = newAddr.replaceAll("https://", "");
                HostSwitchHelper.pointHostSwitch(
                        HostSwitchHelper.REASON_SWITCH_LOGIN,
                        oldHost,
                        newHost
                );
                return newHost;
            }

            @Override
            public void onHttpChangeTargetHost(@NonNull String host) {
                String oldHost = HostConfig.getCONFIG().getApiAddr().replaceAll("https://", "");
                HostConfig.changeTargetHost(host);
                HostSwitchHelper.pointHostSwitch(
                        HostSwitchHelper.REASON_SWITCH_DATA_SYNC,
                        oldHost,
                        host
                );
                String newAddr = HostConfig.getCONFIG().getMqttAddr();
                ServiceManager.getInstance().getEmailService().updateMailDomain(newAddr);
            }
        });

    }

    // 返回真实构建号
    private static String getRealBuildNumber() {
        // 测试版本
        if (TextUtils.equals("None", BuildConfig.BUILD_NUMBER)) {
            return BuildConfig.BUILD_NUMBER;
        }

        String buildNumber = ProcessHelper.getToolPreferences().getString("BUILD_NUMBER", "None");
        if (TextUtils.equals("None", buildNumber)) {
            ProcessHelper.getToolPreferences().putString("BUILD_NUMBER", BuildConfig.BUILD_NUMBER);
            return BuildConfig.BUILD_NUMBER;
        }

        try {
            int saveNumber = Integer.parseInt(buildNumber);
            int pkgNumber = Integer.parseInt(BuildConfig.BUILD_NUMBER);
            // 主构建号正常都是大于热修构建号, 除非修改了构建任务
            if (pkgNumber > saveNumber) {
                ProcessHelper.getToolPreferences().putString("BUILD_NUMBER", BuildConfig.BUILD_NUMBER);
                return BuildConfig.BUILD_NUMBER;
            } else {
                return buildNumber;
            }
        } catch (NumberFormatException e) {
        }
        return "None";
    }

    /**
     * 初始化大图查看三方库
     */
    private void initBigImageViewerSDK(Application context) {
        // TODO: Fresco 3.x兼容性问题 - 暂时注释掉SVG功能
        /*
        ImageDecoderConfig imageDecoderConfig = ImageDecoderConfig.newBuilder()
                .addDecodingCapability(
                        SvgDecoderExample.SVG_FORMAT, new SvgDecoderExample.SvgFormatChecker(), new SvgDecoderExample.SvgDecoder())
                .build();
        DraweeConfig draweeConfig = DraweeConfig.newBuilder()
                .addCustomDrawableFactory(new SvgDecoderExample.SvgDrawableFactory())
                .build();
        */

        // 简化配置，移除SVG支持
        ImageDecoderConfig imageDecoderConfig = ImageDecoderConfig.newBuilder().build();
        DraweeConfig draweeConfig = DraweeConfig.newBuilder().build();
        BigImageViewer.initialize(
                // fresco初始化
                FrescoImageLoader.with(
                        context,
                        HttpExecutor.initFrescoConfig(context, imageDecoderConfig, new BHCacheKeyFactory()),
                        draweeConfig
                )
        );
    }

    private void printPermissionList(Context context) {
        StringBuilder hasPermissionList = new StringBuilder("hasPermissionList : \n");
        StringBuilder noPermissionList = new StringBuilder("noPermissionList : \n");
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), PackageManager.GET_PERMISSIONS);
            if (packageInfo.requestedPermissions != null) {
                for (String permission : packageInfo.requestedPermissions) {
                    boolean hasPermission = PackageManager.PERMISSION_GRANTED == pm.checkPermission(permission, context.getPackageName());
                    if (hasPermission) {
                        hasPermissionList.append(permission).append(" \n");
                    } else {
                        noPermissionList.append(permission).append(" \n");
                    }
                }
            }
            TLog.info(TAG, hasPermissionList.toString());
            TLog.info(TAG, noPermissionList.toString());
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
    }

    private void initDebugDatabase(String name, String path) {
        try {
            Class<?> debugSQLiteInit = Class.forName("com.twl.debug.sqlite.DebugSQLiteInit");
            Method constructor = debugSQLiteInit.getDeclaredMethod("init", String.class, String.class);
            constructor.invoke(null, name, path);
        } catch (Exception e) {
            TLog.debug(TAG, "initDebugDatabase error : " + e.getMessage());
        }
    }

    /**
     * 忽略某些异常上报，要放在bugly初始化之后做，若放在之前，bugly 的 uncaughtExceptionHandler会先走，起不到
     * 过滤作用
     */
    private void ignoreFinalizeTimeOut() {
        Thread.UncaughtExceptionHandler defaultUncaughtExceptionHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
            @Override
            public void uncaughtException(Thread t, Throwable e) {
                if (TextUtils.equals(t.getName(), "FinalizerWatchdogDaemon") && e instanceof TimeoutException) {
                    //忽略finalize 超时bug
                    TLog.error(TAG, "ignore bug FinalizerWatchdogDaemon " + e.getMessage());
                } else {
                    TLog.error(TAG, e.getMessage());
                    defaultUncaughtExceptionHandler.uncaughtException(t, e);
                }
            }
        });
    }

    /**
     * 根据设备分级，获取对应数量
     *
     * @param deviceLevel
     * @return
     */
    private int getRepeatNumByLevel(int deviceLevel) {
        if (deviceLevel == DeviceYearClass.DeviceLevel.LEVEL_OLD_RANGE_DEVICE) {
            return 3;
        } else if (deviceLevel == DeviceYearClass.DeviceLevel.LEVEL_MID_RANGE_DEVICE) {
            return 5;
        } else if (deviceLevel == DeviceYearClass.DeviceLevel.LEVEL_HIGH_RANGE_DEVICE) {
            return 7;
        } else {
            return 9;
        }
    }

    private void initRouter(Context context) {
        // 自定义Logger
        DefaultLogger logger = new DefaultLogger() {
            @Override
            protected void handleError(Throwable t) {
                if (Debugger.isEnableDebug()) {
                    // 此处上报Fatal级别的异常
                    TLog.error(Debugger.LOG_TAG, t.getMessage());
                    CrashReport.postCatchedException(t);
                    super.handleError(t);
                }
            }
        };


        // 设置Logger
        Debugger.setLogger(logger);

        // Log开关，建议测试环境下开启，方便排查问题。
        Debugger.setEnableLog(BuildConfig.DEBUG);

        // 调试开关，建议测试环境下开启。调试模式下，严重问题直接抛异常，及时暴漏出来。
        Debugger.setEnableDebug(BuildConfig.DEBUG);
        Config.INSTANCE.setFlavorConfig(BuildConfig.CONFIG);

        // 创建RootHandler
        HiRootUriHandler rootHandler = new HiRootUriHandler(context);

        // 设置全局跳转完成监听器，可用于跳转失败时统一弹Toast提示，做埋点统计等。
        rootHandler.setGlobalOnCompleteListener(new HiGlobalOnCompleteListener());

        // 初始化
        Router.init(rootHandler);
        rootHandler.addInterceptor(new UriInterceptor() {
            @Override
            public void intercept(@NonNull UriRequest request, @NonNull UriCallback callback) {
                //拦截X86架构的音视频会议跳转
                if (!VideoAndAudioPageRouter.MEETING_ACTIVITY.equals(request.getUri().toString())) {
                    callback.onNext();
                    return;
                }
                String[] abis = Build.SUPPORTED_ABIS;
                boolean abiSupport = false;
                for (int i = 0; abis != null && i < abis.length; i++) {
                    String abi = abis[i];
                    if (abi != null && (abi.contains("arm64") || abi.contains("armeabi"))) {
                        abiSupport = true;
                        break;
                    }
                }
                if (abiSupport) {
                    callback.onNext();
                }
            }
        });
        /*
         * 因为依赖的对象所处的模块层级太高，不能写在HiRootUriHandler的初始化中，只能在这写
         * */
        // 都没有处理，则尝试根据配置是否直接启动Uri，顶层的WebViewHandler是默认不生效的，除非设置了tryStartUri(true)
        WebViewHandler topDefaultHandler = new WebViewHandler(false);
        rootHandler.addChildHandler(topDefaultHandler, -100);

        // 增加替换 AppLink默认Handler的 拦截器
        //AppLink层的WebViewHandler是默认生效的。也就是说只要匹配了applink的协议，如果没有匹配到具体的path，就会使用这个默认的Handler，除非设置了tryStartUri(false)
        WebViewHandler appLinkDefaultHandler = new WebViewHandler();
        appLinkDefaultHandler.addInterceptor((request, callback) -> {
            request.putField(RouterBaseConstant.WEB_STYLE, Constants.WEB_STYLE_SHARE_URL);
            callback.onNext();
        });
        rootHandler.getUriAnnotationHandler().addInterceptor(
                new ReplaceDefaultHandlerOfPathHandlerInterceptor(
                        RouterBaseConstant.APP_LINK_SCHEME, RouterBaseConstant.APP_LINK__HOST, appLinkDefaultHandler)
        );
        //替换scheme+host=”://“的分组的默认处理器，原因见 ActivityDefaultHandler注释
        rootHandler.getUriAnnotationHandler().addInterceptor(
                new ReplaceDefaultHandlerOfPathHandlerInterceptor(
                        "", "", new ActivityDefaultHandler()
                )
        );
    }

    /**
     * 注册微信相关
     */
    private void registerWeChat() {
        // 通过appId得到IWXAPI这个对象
        IWXAPI wxapi = WXAPIFactory.createWXAPI(App.getApplication(), HostConfig.getWXPayAppId());
        wxapi.registerApp(HostConfig.getWXPayAppId());
    }

    private void initLBase(Application context) {
        SecretManager.initSecret(context);

        BaseConfigParam paramOnline = new BaseConfigParam();
        long s = System.currentTimeMillis();
        paramOnline.setPsKey(AESHelper.decrypt(SecretManager.getPsKeyEncrypt(false), SecretManager.getAesPassword()));
        paramOnline.setClientData(EncryptUtil.decryptFile(context, "data1", SecretManager.getDecryptPassword(context)));
        paramOnline.setServerData(EncryptUtil.decryptFile(context, "data2", SecretManager.getDecryptPassword(context)));
        HostConfig.setBaseConfigParam1(paramOnline);

        BaseConfigParam paramQa = new BaseConfigParam();
        paramQa.setPsKey(AESHelper.decrypt(SecretManager.getPsKeyEncrypt(true), SecretManager.getAesPassword()));
        paramQa.setClientData(EncryptUtil.decryptFile(context, "data3", SecretManager.getDecryptPassword(context)));
        paramQa.setServerData(EncryptUtil.decryptFile(context, "data4", SecretManager.getDecryptPassword(context)));
        HostConfig.setBaseConfigParam2(paramQa);

        AppBuildConfig appBuildConfig = new AppBuildConfig();
        appBuildConfig.CONFIG = BuildConfig.CONFIG;
        appBuildConfig.DEBUG = BuildConfig.DEBUG;
        TwlAppProvider twlAppProvider = new TwlAppProvider();
        LBase.init((BaseApplication) context, appBuildConfig, twlAppProvider);
        HttpClientInit.init(context, new HttpClientInit.HttpConfigCallback() {
            @Override
            public void onAccountDeviceVerify(String safeInfoStr) {
                VerifySdkConfigImpl.get().accountSafetyVerify(safeInfoStr, VerifySdkHelper.KEY_ACCOUNT_DEVICE_VERIFY);
            }
        });
        TLog.info(TAG, "HiStartup -> initLBase " + (System.currentTimeMillis() - s));
    }

    /**
     * 初始化apm相关
     *
     * @param context
     */
    public void initApm(@NonNull Context context) {
        ApmManager.start(context);
    }

    private void initTLog(Application application) {
        TLogConfig config = TLogConfig.create(application);
        config.setDebug(BuildConfig.DEBUG);
        List<String> keepTagList = new ArrayList<>();
        keepTagList.add("bapi");
        ILog desensitizeLog = new DesensitizeLog(config.getConsoleLog(), keepTagList);
        try {
            desensitizeLog.init(application, BuildConfig.DEBUG, TlogHelper.getLogDirPath(), "");
            config.setFileLog(desensitizeLog);
            TLog.initializer(config);
        } catch (Throwable e) {
            Log.e(TAG, String.format("TLog init error, %s", e.getMessage()));
        }
    }

    private void initBugly(Context context) {
        boolean isMainProcess = ProcessUtil.isMainProcess(context);
        // Bugly处理器
        CrashReport.setIsDevelopmentDevice(context, BuildConfig.DEBUG);
        CrashReport.UserStrategy userStrategy = new CrashReport.UserStrategy(context);
        if (isMainProcess) {
            userStrategy.setCrashHandleCallback(new CrashReport.CrashHandleCallback() {
                /**
                 * Crash处理.
                 *
                 * @param crashType    错误类型：CRASHTYPE_JAVA_CRASH，CRASHTYPE_NATIVE 等
                 * @param errorType    错误的类型名
                 * @param errorMessage 错误的消息
                 * @param errorStack   错误的堆栈
                 * @return 返回额外的自定义信息上报
                 */
                @Override
                public synchronized Map<String, String> onCrashHandleStart(int crashType, String errorType, String errorMessage, String errorStack) {
                    // 上报x5内核相关错误到bugly
                    LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
                    try {
                        Account account = HiKernel.getHikernel().getAccount();
                        if (account != null) {
                            CrashReport.putUserData(context, "userID", String.valueOf(account.getUserId()));
                            map.put("userID", account.getUserId() + "");
                        }
                    } catch (Exception e) {
                        TLog.error(TAG, e.getMessage());
                    }
                    return map;
                }
            });
        }
        userStrategy.setUploadProcess(ProcessUtil.isMainProcess(context));
        try {
            userStrategy.setDeviceID(DeviceIdUtils.getDeviceId());
        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
        }
        userStrategy.setEnableUserInfo(true);
//        userStrategy.setBuglyLogUpload(TextUtils.equals(SettingBuilder.getInstance().getFlavor(), "prod"));
        String buglyAppKey = "db33a0b4ee";
        if (TextUtils.equals("prod", SettingBuilder.getInstance().getFlavor())) {
            buglyAppKey = "78781211ab";
        }
        CrashReport.initCrashReport(context, buglyAppKey, BuildConfig.DEBUG, userStrategy);
        Account account = HiKernel.getHikernel().getAccount();
        if (account != null) {
            CrashReport.setUserId(String.valueOf(account.getExposedUserId()));
        }
    }

    private void initPushSdk(Application application) {
        PushSdkManager.getInstance().init(application, new PushRegisterToken());
        PushSdkManager.getInstance().start();
        NotifyUtil.clearNotifications();
        PushSdkManager.getInstance().setForeground(true);
    }

    /**
     * Android 10内网连接广播 需要将当前进程绑定到 p2p连接的网络，否则主进程使用不了网络
     */
    private void initAndroid10InternalNetworkReceiver(Application application) {
        TLog.info(TAG, "initAndroid10InternalNetworkReceiver");
        IntentFilter intentFilter = new IntentFilter(WIFIExtKt.INTERNAL_NETWORK_CONNECT_ACTION);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            application.registerReceiver(new InteralNetworkReceiver(), intentFilter, Context.RECEIVER_EXPORTED);
        } else {
            application.registerReceiver(new InteralNetworkReceiver(), intentFilter);
        }
    }

    public static class InteralNetworkReceiver extends BroadcastReceiver{

        @Override
        public void onReceive(Context context, Intent intent) {
            if (WIFIExtKt.INTERNAL_NETWORK_CONNECT_ACTION.equals(intent.getAction())) {
                Network network = intent.getParcelableExtra(WIFIExtKt.INTERNAL_NETWORK);
                if (network != null) { //将主进程绑定p2p的内网
                    TLog.info(TAG, "Received internal network: " + network.toString());
                    ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
                    connectivityManager.bindProcessToNetwork(network);
                }
            }
        }
    }
}
