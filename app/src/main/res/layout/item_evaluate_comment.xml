<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.main.callback.EvaluateDialogCallback" />

        <variable
            name="viewModel"
            type="com.twl.hi.main.viewmodel.EvaluateDialogViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_corner_20_top_2_color_white"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:paddingTop="15dp">

        <include
            android:id="@+id/ll_evaluate_top"
            layout="@layout/layout_evaluate_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:position='@{"2"}'
            app:callback="@{callback}" />

        <EditText
            app:maxChineseLength="@{1000}"
            android:id="@+id/edit_comment"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_below="@+id/ll_evaluate_top"
            android:layout_marginTop="20dp"
            android:background="@drawable/bg_corner_7_color_f5f5f7"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="top"
            android:hint="@string/evaluate_comment_hint"
            android:paddingLeft="12dp"
            android:paddingTop="11dp"
            android:paddingRight="12dp"
            android:paddingBottom="11dp"
            android:text="@={viewModel.comment}"
            android:textColor="@color/color_0D0D1A"
            android:textColorHint="@color/color_B1B1B8"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_send"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/edit_comment"
            android:layout_marginTop="15dp"
            android:layout_weight="1"
            android:background="@drawable/bg_corner_7_color_5d68e8"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/submit"
            android:onClick="@{()->callback.uploadEvaluate(true)}"
            android:textColor="@color/app_white"
            android:textSize="17sp" />
    </RelativeLayout>
</layout>