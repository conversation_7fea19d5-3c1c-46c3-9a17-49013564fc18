<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <FrameLayout
            android:id="@+id/img_icon"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginTop="14dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/lav_animation"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:lottie_autoPlay="false"
                app:lottie_loop="false"
                app:lottie_speed="1.3" />

            <ImageView
                android:id="@+id/iv_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_come_back_today_new" />
        </FrameLayout>

        <TextView
            android:id="@+id/txt_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="5dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/img_icon"
            app:layout_constraintEnd_toEndOf="@id/img_icon"
            android:background="@drawable/bg_corner_9_color_fb4f63"
            android:gravity="center"
            android:maxLines="1"
            android:minWidth="16dp"
            android:minHeight="16dp"
            android:paddingHorizontal="5dp"
            android:textColor="@color/app_white"
            android:textSize="11sp"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text='99+' />

        <TextView
            android:id="@+id/tv_tab_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:singleLine="true"
            android:textColor="@color/color_677281"
            android:textSize="11sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/img_icon"
            tools:text="首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页首页" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>