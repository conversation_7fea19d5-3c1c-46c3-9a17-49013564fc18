<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="item"
            type="com.twl.hi.basic.dialog.bottom.BottomSelectItemBean" />
    </data>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="?android:attr/selectableItemBackground">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:textColor="@color/color_0D0D1A"
            android:textSize="15sp"
            android:drawablePadding="10dp"
            android:text="@{item.value}"
            tools:text="不提醒" />

        <ImageView
            android:id="@+id/iv_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="20dp"
            android:src="@drawable/schedule_ic_icon_drawer_personal_checked"
            app:visibleGone="@{item.checked}" />
    </RelativeLayout>
</layout>