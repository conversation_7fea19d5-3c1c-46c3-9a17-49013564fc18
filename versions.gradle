ext.deps = [:]
ext.versions = [:]

def versions = [:]
versions.androidx = "1.1.0"
versions.viewpager2 = "1.0.0"
versions.multidex = "2.0.1"
versions.android_gradle_plugin = "7.4.2"
versions.zptinker = "1.9.15.1.beta1"
versions.bugly_report = "4.1.9.2"
versions.bugly_tinker = "1.9.14.17"
versions.tlog = "1.1.12"
versions.r8 = "4.0.71"
versions.net_util = "0.0.3"
versions.wechat = "5.1.4"
versions.piasy = "1.8.1"
versions.fresco = "2.6.0"
versions.huawei = "2.6.3.306"
versions.qbusict = "2.2.0"
versions.soloader = "0.5.1"
versions.okhttp3 = "0.0.1"
versions.gson = "2.8.7"
versions.constraint_layout = "1.1.3"
versions.zxing = "3.5.1"
versions.espresso = "2.2.2"
versions.dcendents = "2.1"
versions.jfrog = "1.8.4"
versions.google_protobuf = "0.8.2"
versions.wcdb_room = "1.0.8"
versions.wcdb_android = "1.0.0"
versions.gallery = "0.3.11"
versions.lottie = "3.0.7"
versions.protobuf = "3.6.1"
versions.smartrefresh = "1.1.0"
versions.room = "2.5.0"
versions.stetho = "1.5.1"
versions.protobuf_format = "1.4"
versions.shortcutbadger = "1.1.22@aar"
versions.booster_version = "0.4.4"
versions.startup = "0.0.3"
versions.magic_indicator = "1.6.0"
versions.commonMarkVersion = "0.13.0"
versions.timeMonitor = "0.0.3"
versions.luban = "1.1.8"
versions.liteav = "7.2.8927"
versions.huawei_push = "6.7.0.300"
versions.honor_push = "8.0.12.307"
versions.amap_3d = "8.1.0"
versions.amap_search = "9.2.0"
versions.amap_location = "6.1.0"
versions.wmrouter = "1.2.1"
versions.wmrouter_bzl = "0.0.5-beta1"
//versions.hi_vpn = "3.18.0.4-SNAPSHOT"
versions.hi_vpn = "3.18.0.0"
//versions.mails_sdk = "3.20.0.17"
versions.mails_sdk = "3.37.0.0"
versions.flexbox = "2.0.1"
versions.apm = "1.3.70-alpha1"
versions.anti = "1.2.6"
versions.twlsafe = "0.0.0.1.0"
versions.mmkv = "1.2.13"
versions.afanty_player = "2.1.3.1111"
versions.lifecycle = "2.2.0"
versions.leakcanary = "2.11"
versions.fastjson = "1.2.66"
versions.security_verify = "0.4.2-hi"
versions.security_collection = "2.0.4"
versions.desugar_jdk_libs = "2.0.3"
versions.kzmp = "1.2.3.9.9_debug1"
versions.blur_view = "1.2.1"
versions.lib_voice = "0.3.9.18_hi-SNAPSHOT"
versions.lib_commonmark = "0.0.2"
versions.module_markwon_core = "0.0.9"
versions.exoplayer = "1.3.1"
versions.patch_tracking = "0.0.3"

//组件化代码中的lib module
versions.lib_twl_utils = "0.0.5"
versions.lib_common = "0.0.5"
versions.lib_http = "0.0.2"
versions.lib_db = "0.0.1"
versions.lib_emotion = "0.0.1"
versions.lib_kernel = "0.0.2"
versions.lib_push = "0.0.1"
versions.lib_image = "0.0.1"
versions.lib_crypto = "0.0.1"
versions.lib_push_oppo_sdk = "0.0.1"
versions.lib_push_vivo_sdk = "0.0.1"
versions.lib_push_xiaomi_sdk = "0.0.1"
versions.lib_mobile_mqtt_service = "0.0.1"
versions.lib_foundation_service = "0.0.1"
versions.lib_startup = "0.0.1"
versions.lib_secret = "0.0.1"
versions.lib_richtext = "0.0.2"
versions.lib_webeditor = "0.0.2"
versions.lib_router = "0.0.1"
versions.lib_batch_tracking_upload = "0.0.1"
versions.lib_security = "0.0.1"
versions.onekit = "3.7.14.9-bzl-1.1.1"

versions.module_foundation_business = "0.0.1"
versions.module_select = "0.0.1"
versions.module_webview = "0.0.1"
versions.module_email = "0.0.1"
versions.module_viewer = "0.0.1"
versions.module_video = "0.0.1"
versions.module_organization = "0.0.1"
versions.module_work = "0.0.1"
versions.module_schedule = "0.0.1"
versions.module_me = "0.0.1"
versions.module_video_meeting = "0.0.1"
versions.module_chat = "0.0.2"
versions.module_scan = "0.0.1"
versions.module_login = "0.0.1"
versions.module_office_preview = "0.0.2"
versions.module_audio = "0.0.1"

versions.export_module_chat = "0.0.1"
versions.export_module_email = "0.0.1"
versions.export_module_main = "0.0.1"
versions.export_module_me = "0.0.1"
versions.export_module_organization = "0.0.1"
versions.export_module_schedule = "0.0.1"
versions.export_module_select = "0.0.1"
versions.export_module_video_meeting = "0.0.1"
versions.export_module_webview = "0.0.1"
versions.export_module_work = "0.0.1"
versions.export_module_workflow = "0.0.1"
versions.export_module_office_preview = "0.0.2"
versions.export_module_audio = "0.0.1"


ext.versions = versions

ext.versions = versions

def deps = [:]
deps.fastjson = "com.alibaba:fastjson:$versions.fastjson"

deps.mmkv = "com.tencent:mmkv:$versions.mmkv"
deps.kzmp = "com.bzl.bosshi:kzmp:$versions.kzmp"
deps.jsbridge = "com.bzl.bosshi:lib-jsbridge:$versions.kzmp"

def lifecycle = [:]
lifecycle.viewmodel = "androidx.lifecycle:lifecycle-viewmodel:$versions.lifecycle"
lifecycle.livedata = "androidx.lifecycle:lifecycle-livedata:$versions.lifecycle"
lifecycle.livedata_ktx = "androidx.lifecycle:lifecycle-livedata-ktx:$versions.lifecycle"
lifecycle.runtime = "androidx.lifecycle:lifecycle-runtime:$versions.lifecycle"
lifecycle.viewmodel_savedstate = "androidx.lifecycle:lifecycle-viewmodel-savedstate:$versions.lifecycle"
lifecycle.java8_common = "androidx.lifecycle:lifecycle-common-java8:$versions.lifecycle"
deps.lifecycle = lifecycle

def androidx = [:]
androidx.annotations = "androidx.annotation:annotation:$versions.androidx"
androidx.app_compat = "androidx.appcompat:appcompat:$versions.androidx"
androidx.recyclerview = "androidx.recyclerview:recyclerview:1.2.1"
androidx.paging3 = "androidx.paging:paging-runtime-ktx:3.1.1"
androidx.cardview = 'androidx.cardview:cardview:1.0.0'
androidx.design = 'com.google.android.material:material:1.0.0'
androidx.core = "androidx.core:core:1.5.0"
androidx.core_ktx = 'androidx.core:core-ktx:1.5.0'
deps.androidx = androidx
deps.viewpager2 = "androidx.viewpager2:viewpager2:$versions.viewpager2"

def espresso = [:]
espresso.core = 'androidx.test.espresso:espresso-core:3.1.0'
espresso.contrib = 'androidx.test.espresso:espresso-contrib:3.1.0'
deps.espresso = espresso

def atsl = [:]
atsl.runner = "androidx.test:runner:$versions.atsl_runner"
atsl.rules = "androidx.test:rules:$versions.atsl_runner"
deps.atsl = atsl

def bugly = [:]
bugly.report = "com.tencent.bugly:crashreport:$versions.bugly_report"
bugly.bugly_tinker = "com.tencent.tinker:tinker-android-lib:$versions.bugly_tinker"
deps.bugly = bugly

def piasy = [:]
piasy.viewer = "com.github.piasy:BigImageViewer:$versions.piasy"
piasy.loader = "com.github.piasy:FrescoImageLoader:$versions.piasy"
piasy.factory = "com.github.piasy:FrescoImageViewFactory:$versions.piasy"
deps.piasy = piasy

def fresco = [:]
fresco.fresco = "com.facebook.fresco:fresco:$versions.fresco"
fresco.animated_gif = "com.facebook.fresco:animated-gif:$versions.fresco"
fresco.okhttp = "com.facebook.fresco:imagepipeline-okhttp3:$versions.fresco"
fresco.imagepipeline = "com.facebook.fresco:imagepipeline:$versions.fresco"
deps.fresco = fresco

def room = [:]
room.runtime = "androidx.room:room-runtime:$versions.room"
room.runtime_ktx = "androidx.room:room-ktx:$versions.room"
room.compiler = "androidx.room:room-compiler:$versions.room"
room.paging = "androidx.room:room-paging:$versions.room"
deps.room = room

def huawei = [:]
huawei.push = "com.huawei.android.hms:push:$versions.huawei"
huawei.base = "com.huawei.android.hms:base:$versions.huawei"
deps.huawei = huawei

def commonmark = [:]
commonmark.commonmark = "com.atlassian.commonmark:commonmark:$versions.commonMarkVersion"
commonmark.commonmark_strikethrough = "com.atlassian.commonmark:commonmark-ext-gfm-strikethrough:$versions.commonMarkVersion"
commonmark.commonmark_table = "com.atlassian.commonmark:commonmark-ext-gfm-tables:$versions.commonMarkVersion"
deps.commonmark = commonmark

//组件化本地代码中的lib module打包的远程aar依赖
def commonLibs = [:]
commonLibs.lib_twl_utils = "com.bzl.bosshi:lib-twl-utils:$versions.lib_twl_utils"
commonLibs.lib_common = "com.bzl.bosshi:lib-common:$versions.lib_common"
commonLibs.lib_http = "com.bzl.bosshi:lib-http:$versions.lib_http"
commonLibs.lib_db = "com.bzl.bosshi:lib-db:$versions.lib_db"
commonLibs.lib_kernel = "com.bzl.bosshi:lib-kernel:$versions.lib_kernel"
commonLibs.lib_push = "com.bzl.bosshi:lib-push:$versions.lib_push"
commonLibs.lib_image = "com.bzl.bosshi:lib-image:$versions.lib_image"
commonLibs.lib_crypto = "com.bzl.bosshi:lib-crypto:$versions.lib_crypto"
commonLibs.lib_startup = "com.bzl.bosshi:lib-startup:$versions.lib_startup"
commonLibs.lib_secret = "com.bzl.bosshi:lib-secret:$versions.lib_secret"
commonLibs.lib_push_oppo_sdk = "com.bzl.bosshi:lib-push-oppo-sdk:$versions.lib_push_oppo_sdk"
commonLibs.lib_push_vivo_sdk = "com.bzl.bosshi:lib-push-vivo-sdk:$versions.lib_push_vivo_sdk"
commonLibs.lib_push_xiaomi_sdk = "com.bzl.bosshi:lib-push-xiaomi-sdk:$versions.lib_push_xiaomi_sdk"
commonLibs.lib_mobile_mqtt_service = "com.bzl.bosshi:lib-mobile-mqtt-service:$versions.lib_mobile_mqtt_service"
commonLibs.lib_foundation_service = "com.bzl.bosshi:lib-foundation-service:$versions.lib_foundation_service"
commonLibs.lib_richtext = "com.bzl.bosshi:lib-richtext:$versions.lib_richtext"
commonLibs.lib_webeditor = "com.bzl.bosshi:lib-webeditor:$versions.lib_webeditor"
commonLibs.lib_emotion = "com.bzl.bosshi:lib-emotion:$versions.lib_emotion"
commonLibs.lib_batch_tracking_upload = "com.bzl.bosshi:lib_batch_tracking_upload:$versions.lib_batch_tracking_upload"
commonLibs.lib_security = "com.bzl.bosshi:lib_security:$versions.lib_security"


commonLibs.module_foundation_business = "com.bzl.bosshi:lib-foundation-business:$versions.module_foundation_business"
commonLibs.module_select = "com.bzl.bosshi:module-select:$versions.module_select"
commonLibs.module_webview = "com.bzl.bosshi:module-webview:$versions.module_webview"
commonLibs.module_email = "com.bzl.bosshi:module-email:$versions.module_email"
commonLibs.module_viewer = "com.bzl.bosshi:module-image:$versions.module_viewer"
commonLibs.module_video = "com.bzl.bosshi:module-video:$versions.module_video"
commonLibs.module_organization = "com.bzl.bosshi:module-organization:$versions.module_organization"
commonLibs.module_work = "com.bzl.bosshi:module-work:$versions.module_work"
commonLibs.module_schedule = "com.bzl.bosshi:module-schedule:$versions.module_schedule"
commonLibs.module_me = "com.bzl.bosshi:module-me:$versions.module_me"
commonLibs.module_video_meeting = "com.bzl.bosshi:module-video-meeting:$versions.module_video_meeting"
commonLibs.module_chat = "com.bzl.bosshi:module-chat:$versions.module_chat"
commonLibs.module_scan = "com.bzl.bosshi:module-scan:$versions.module_scan"
commonLibs.module_login = "com.bzl.bosshi:export-module-chat:$versions.module_login"
commonLibs.module_office_preview = "com.bzl.bosshi:module_office_preview:$versions.module_office_preview"
commonLibs.module_audio = "com.bzl.bosshi:module-audio:$versions.module_audio"

commonLibs.export_module_chat = "com.bzl.bosshi:export-module-chat:$versions.export_module_chat"
commonLibs.export_module_email = "com.bzl.bosshi:export-module-email:$versions.export_module_email"
commonLibs.export_module_main = "com.bzl.bosshi:export-module-main:$versions.export_module_main"
commonLibs.export_module_me = "com.bzl.bosshi:export-module-me:$versions.export_module_me"
commonLibs.export_module_organization = "com.bzl.bosshi:export-module-organization:$versions.export_module_organization"
commonLibs.export_module_schedule = "com.bzl.bosshi:export-module-schedule:$versions.export_module_schedule"
commonLibs.export_module_select = "com.bzl.bosshi:export-module-select:$versions.export_module_select"
commonLibs.export_module_video_meeting = "com.bzl.bosshi:export-module-video-meeting:$versions.export_module_video_meeting"
commonLibs.export_module_webview = "com.bzl.bosshi:export-module-webview:$versions.export_module_webview"
commonLibs.export_module_work = "com.bzl.bosshi:export-module-work:$versions.export_module_work"
commonLibs.export_module_workflow = "com.bzl.bosshi:export-module-workflow:$versions.export_module_workflow"
commonLibs.export_module_office_preview = "com.bzl.bosshi:export_module_office_preview:$versions.export_module_office_preview"
commonLibs.export_module_audio = "com.bzl.bosshi:export-module-audio:$versions.export_module_audio"
deps.commonLibs = commonLibs


deps.exoplayer = "androidx.media3:media3-exoplayer:$versions.exoplayer"
deps.exoplayer_okhttp = "androidx.media3:media3-datasource-okhttp:$versions.exoplayer"
deps.multidex = "androidx.multidex:multidex:$versions.multidex"
deps.android_svg = "com.caverock:androidsvg:1.4"
deps.android_gif = "pl.droidsonroids.gif:android-gif-drawable:1.2.15"
deps.zxing = "com.google.zxing:core:$versions.zxing"
deps.stetho = "com.facebook.stetho:stetho:$versions.stetho"
deps.tlog = "com.techwolf.lib:tlog:$versions.tlog"
deps.net_util = "com.twl.zhipin:net-util:$versions.net_util"
deps.wechat = "com.tencent.mm.opensdk:wechat-sdk-android-with-mta:$versions.wechat"
deps.qbusict = "nl.qbusict:cupboard:$versions.qbusict"
deps.soloader = "com.facebook.soloader:soloader:$versions.soloader"
//deps.okhttp3 = "com.squareup.okhttp3:okhttp:$versions.okhttp3"
deps.okhttp3 = "com.bzl.bosshi:hi-okhttp:$versions.okhttp3"

deps.gson = "com.google.code.gson:gson:$versions.gson"
deps.constraint_layout = "androidx.constraintlayout:constraintlayout:$versions.constraint_layout"
deps.gallery = "com.bzl.bosshi:module-gallery:$versions.gallery"
deps.onekit = "com.bzl.onekit:dokit:$versions.onekit"

deps.dcendents = "com.github.dcendents:android-maven-gradle-plugin:$versions.dcendents"
deps.jfrog = "com.jfrog.bintray.gradle:gradle-bintray-plugin:$versions.jfrog"

deps.android_gradle_plugin = "com.android.tools.build:gradle:$versions.android_gradle_plugin"
deps.r8 = "com.android.tools:r8:$versions.r8"
deps.zptinker = "com.twl.zhipin:tinker-build:$versions.zptinker"
deps.zptinker_runtime = "com.twl.zhipin:tinker-runtime:$versions.zptinker"

deps.google_protobuf = "com.google.protobuf:protobuf-gradle-plugin:$versions.google_protobuf"

deps.wcdb_room = "com.tencent.wcdb:room:$versions.wcdb_room"
deps.wcdb_android = "com.tencent.wcdb:wcdb-android:$versions.wcdb_android"

deps.lottie = "com.airbnb.android:lottie:$versions.lottie"

deps.protobuf = "com.google.protobuf:protobuf-java:$versions.protobuf"

deps.smartrefresh = "com.scwang.smartrefresh:SmartRefreshLayout:$versions.smartrefresh"

deps.stetho = "com.facebook.stetho:stetho:$versions.stetho"
deps.stetho_okhttp = "com.facebook.stetho:stetho-okhttp3:$versions.stetho"
deps.protobuf_format = "com.googlecode.protobuf-java-format:protobuf-java-format:$versions.protobuf_format"
deps.shortcutbadger = "me.leolin:ShortcutBadger:$versions.shortcutbadger"

deps.magic_indicator = "com.github.hackware1993:MagicIndicator:$versions.magic_indicator"
deps.luban = "top.zibin:Luban:$versions.luban"

deps.liteav = "com.tencent.liteav:LiteAVSDK_Professional:$versions.liteav"

deps.huawei_push = "com.huawei.hms:push:$versions.huawei_push"
deps.honor_push = "com.hihonor.mcs:push:$versions.honor_push"

deps.map_3d = "com.amap.api:3dmap:$versions.amap_3d"
deps.map_search = "com.amap.api:search:$versions.amap_search"
deps.map_location = "com.amap.api:location:$versions.amap_location"

deps.wmrouter = "io.github.meituan-dianping:router:$versions.wmrouter"
deps.wmrouter_compiler = "io.github.meituan-dianping:compiler:$versions.wmrouter"
deps.wmrouter_bzl = "com.bzl.plugins.oneapt:wm_router_apt:$versions.wmrouter_bzl"

deps.mails_sdk = "com.twl.bosshi:mails-sdk:$versions.mails_sdk"
deps.hi_vpn = "com.bzl.bosshi:hivpn:$versions.hi_vpn"
deps.blur_view = "com.github.mmin18:realtimeblurview:$versions.blur_view"
deps.lib_voice = "com.bzl.sdk:lib_voice:$versions.lib_voice"
deps.zp_sdk_avmodule = "com.twl.sdk.file:zp-sdk-avmodule:$versions.zp_sdk_avmodule"
deps.media_kit = "com.twl.sdk.file:media-kit:$versions.media_kit"

deps.wmrouter_gradle_plugin = "io.github.meituan-dianping:plugin:$versions.wmrouter"

deps.flexbox = "com.google.android:flexbox:$versions.flexbox"

deps.apm = "com.hpbr.bosszhipin:module-apm:$versions.apm"
deps.apm_settings = "com.hpbr.bosszhipin:module-apm-settings:$versions.apm_settings"

deps.twl_safe = "com.hpbr.bosszhipin.twlsafe:twlsafe-detect:$versions.twlsafe"

deps.afantyPlayer = "com.twl.sdk.file:afanty-player:${versions.afanty_player}@aar"

deps.leakcanary = "com.squareup.leakcanary:leakcanary-android:$versions.leakcanary"
deps.leakcanary_plugin = "com.squareup.leakcanary:leakcanary-deobfuscation-gradle-plugin:$versions.leakcanary"
deps.security_verify = "com.bzl.security:verify:$versions.security_verify"
deps.security_collection = "com.twl.anti:anti:$versions.security_collection"

deps.desugar_jdk_libs = "com.android.tools:desugar_jdk_libs:$versions.desugar_jdk_libs"

deps.lib_commonmark = "com.bzl.bosshi:lib-commonmark:$versions.lib_commonmark"
deps.module_markwon_core = "com.bzl.bosshi:module-markwon-core:$versions.module_markwon_core"
deps.patch_tracking = "com.bzl.dz:batch-tracking:$versions.patch_tracking"

ext.deps = deps

def build_versions = [:]
build_versions.min_sdk = 29
build_versions.target_sdk = 33
build_versions.build_tools = "33.0.1"
ext.build_versions = build_versions

def addPluginRepos(RepositoryHandler handler) {
    handler.maven { url "https://android3.weizhipin.com/nexus/repository/public/" }
    handler.maven {
        url "https://maven.aliyun.com/repository/jcenter"
    }
    handler.maven {
        url "https://maven.aliyun.com/repository/google"
    }
    handler.maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }

    //友盟
//    handler.maven { url 'https://dl.bintray.com/umsdk/release' }

    handler.maven { url 'https://developer.huawei.com/repo/' }
    handler.maven { url 'https://developer.hihonor.com/repo' }
}

def addRepos(RepositoryHandler handler) {
    handler.mavenLocal()
    handler.flatDir { dirs "$projectDir/libs" }
    handler.flatDir { dirs "$projectDir/lib_push/libs" }
    handler.maven { url "https://android3.weizhipin.com/nexus/repository/public/" }
    handler.maven { url "https://android3.weizhipin.com/nexus/repository/maven-snapshots/" }
    handler.maven {
        allowInsecureProtocol = true
        url 'http://maven.aliyun.com/nexus/content/groups/public/'
    }
    handler.maven { url 'https://maven.aliyun.com/repository/central' }
    handler.maven { url 'https://maven.aliyun.com/repository/google' }
    handler.maven { url 'https://maven.aliyun.com/repository/jcenter' }
    // Techwolf Maven服务器URL

//    handler.maven { url "http://dl.bintray.com/piasy/maven" }
    handler.maven { url 'https://jitpack.io' }
    handler.maven {
        allowInsecureProtocol = true
        url 'http://developer.huawei.com/repo/'
    }
    //友盟
//    handler.maven { url 'https://dl.bintray.com/umsdk/release' }
    handler.maven { url 'https://developer.huawei.com/repo/' }
    handler.maven { url 'https://developer.hihonor.com/repo' }
//    handler.jcenter()
//    handler.mavenCentral()
//    handler.google()
}

ext.addRepos = this.&addRepos
ext.addPluginRepos = this.&addPluginRepos