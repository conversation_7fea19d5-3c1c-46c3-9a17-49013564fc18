package com.twl.hi.foundation.facade;

import com.twl.hi.foundation.api.request.emotion.EmotionSyncRequest;
import com.twl.hi.foundation.api.response.EmotionSyncResponse;
import com.twl.hi.foundation.utils.emotion.EmotionGlobalHelper;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;

import hi.kernel.HiKernel;

public class <PERSON>otionSyncHand<PERSON> extends BaseHandler<EmotionSyncResponse> {

    @Override
    protected void syncData() {
        EmotionSyncRequest request = new EmotionSyncRequest(getRequestCallback());
        HttpExecutor.execute(request);
    }

    @Override
    public void handleInChildThread(ApiData<EmotionSyncResponse> data) {
        super.handleInChildThread(data);
        String userId = HiKernel.getHikernel().getAccount().getUserId();
        EmotionGlobalHelper.updateAllEmotionData(userId, data.resp.result);
        SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_STICKER_SYNC_COMPLETE);
    }
}
