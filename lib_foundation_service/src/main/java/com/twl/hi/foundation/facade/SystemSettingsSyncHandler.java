package com.twl.hi.foundation.facade;

import android.text.TextUtils;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.request.SystemSettingRequest;
import com.twl.hi.foundation.api.request.emotion.EmojiSyncRequest;
import com.twl.hi.foundation.api.response.EmojiSyncResponse;
import com.twl.hi.foundation.api.response.SystemSettingResponse;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.utils.emotion.EmotionGlobalHelper;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.GsonUtils;
import com.twl.utils.jurisdiction.JurisdictionUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

import hi.kernel.Constants;
import hi.kernel.HiKernel;
import lib.twl.common.util.LList;
import lib.twl.common.util.ProcessHelper;

public class SystemSettingsSyncHandler extends BaseHandler<SystemSettingResponse> {

    private static final String TAG = "SystemSettingsSync";

    public SystemSettingsSyncHandler() {
        int clockOnlyPush = ProcessHelper.getUserCompanyPreferences().getInt(Constants.KEY_CLOCK_ONLY_PUSH, JurisdictionUtils.IDENTITY_VISIBLE);
        SyncDispatch.getInstance().setEvent(new SyncDispatch.SyncIntent(SyncDispatch.EVENT_CLOCK_ONLY_PUSH_COMPLETE, clockOnlyPush));
        //展示常用语开关初始化
        int comWordsShow = ProcessHelper.getUserCompanyPreferences().getInt(Constants.KEY_COMMON_WORDS_SHOW, JurisdictionUtils.IDENTITY_VISIBLE);
        SyncDispatch.getInstance().setEvent(new SyncDispatch.SyncIntent(SyncDispatch.EVENT_COMMON_WORDS_SHOW_COMPLETE, comWordsShow));
    }

    @Override
    protected void syncData() {
        SystemSettingRequest request = new SystemSettingRequest(this);
        HttpExecutor.execute(request);
    }

    @Override
    public void handleInChildThread(ApiData<SystemSettingResponse> data) {
        super.handleInChildThread(data);
        SyncDispatch.getInstance().setEvent(new SyncDispatch.SyncIntent(SyncDispatch.EVENT_CLOCK_ONLY_PUSH_COMPLETE, data.resp.clockSendMsg));
        ProcessHelper.getUserCompanyPreferences().edit().putInt(Constants.KEY_CLOCK_ONLY_PUSH, data.resp.clockSendMsg).apply();
        //展示常用语开关设置
        SyncDispatch.getInstance().setEvent(new SyncDispatch.SyncIntent(SyncDispatch.EVENT_COMMON_WORDS_SHOW_COMPLETE, data.resp.comWordsShow));
        ProcessHelper.getUserCompanyPreferences().edit().putInt(Constants.KEY_COMMON_WORDS_SHOW, data.resp.comWordsShow).apply();
        ProcessHelper.getUserPreferences().edit().putInt(Constants.KEY_SCHEDULE_TIME_VISIBLE, data.resp.calendarTimeVisible).apply();
        ProcessHelper.getUserPreferences().edit().putInt(Constants.KEY_SCHEDULE_VIDEO_MEETING_TYPE, data.resp.scheduleLinkMeeting).apply();
        ProcessHelper.getUserCompanyPreferences().edit().putInt(Constants.KEY_CHAT_VISIBLE_SWITCH, data.resp.imChatVisibleSwitch).apply();
        ProcessHelper.getUserCompanyPreferences().edit().putInt(Constants.KEY_WORK_WIDGET_SWITCH, data.resp.dataElementSwitch).apply();
        ServiceManager.getInstance().getScheduleService().setShouldShowLunar(data.resp.chineseLunarCalendar == 1);
        String scheduleAlertMins = data.resp.scheduleAlertMins;
        if (!TextUtils.isEmpty(scheduleAlertMins)) {
            ProcessHelper.getUserPreferences().edit().putString(Constants.KEY_SCHEDULE_ALERT_MINS, scheduleAlertMins).apply();
        }
        ProcessHelper.getUserPreferences().edit().putInt(Constants.KEY_CALENDAR_WEEK_START, data.resp.firstDayWeek).apply();
        SyncDispatch.getInstance().setEvent(new SyncDispatch.SyncIntent(SyncDispatch.EVENT_WEEK_START_SET_COMPLETE, data.resp.firstDayWeek));
        SyncDispatch.getInstance().setEvent(new SyncDispatch.SyncIntent(SyncDispatch.EVENT_HIDE_REJECT_SCHEDULE_COMPLETE, data.resp.hideRejectSchedule));
        if (data.resp.zhishuLinkRuleList != null) {
            ProcessHelper.getUserPreferences().edit().putStringSet(Constants.KEY_ZHISHU_URL_PATTERNS, new HashSet<>(data.resp.zhishuLinkRuleList));
        }
        if (data.resp.msgContentMaxLength > 0) {
            ProcessHelper.getUserPreferences().edit().putInt(Constants.KEY_ZHISHU_URL_MAX_LENGTH, data.resp.msgContentMaxLength);
        }
        if (!LList.isEmpty(data.resp.needEnhanceUrlRuleList)) {
            HashMap<Integer, Set<String>> map = new HashMap<>();
            for (SystemSettingResponse.EnhancePattern enhancePattern : data.resp.needEnhanceUrlRuleList) {
                Set<String> patterns = map.get(enhancePattern.type);
                if (patterns == null) {
                    patterns = new HashSet<>();
                    map.put(enhancePattern.type, patterns);
                }
                patterns.add(enhancePattern.patternStr);
            }
            ProcessHelper.getUserPreferences().edit().putString(Constants.KEY_HYPERTEXT_PATTERNS, GsonUtils.getGson().toJson(map));
        }
        if (!TextUtils.isEmpty(data.resp.urlLinkRule)) {
            ProcessHelper.getUserPreferences().edit().putString(Constants.KEY_URL_PATTERNS, data.resp.urlLinkRule);
        }
        if (!TextUtils.isEmpty(data.resp.ipLinkRule)) {
            ProcessHelper.getUserPreferences().edit().putString(Constants.KEY_IP_ADDRESS_PATTERNS, data.resp.ipLinkRule);
        }
        if (!TextUtils.isEmpty(data.resp.emailLinkRule)) {
            ProcessHelper.getUserPreferences().edit().putString(Constants.KEY_EMAIL_PATTERNS, data.resp.emailLinkRule);
        }
        ProcessHelper.getUserPreferences().edit().putInt(Constants.KEY_MEETING_COMMENT_TIMES, data.resp.linkMeetingFeedbackThreshold);
        ProcessHelper.getUserPreferences().edit().putLong(Constants.KEY_URL_RULE_VALID_TIME, data.resp.urlRuleValidTime);
        ProcessHelper.getUserPreferences().edit().putInt(Constants.KEY_MARKDOWN_TEXT_MAX_LENGTH, data.resp.msgContentSendMaxLength);
        ProcessHelper.getUserPreferences().edit().putString(Constants.KEY_ZHISHU_FILE_TRANSFER, GsonUtils.getGson().toJson(data.resp.zhishuTransferSettingList));
        handleEmojiUpdate(data.resp.imEmojiVersion);
    }

    private void handleEmojiUpdate(int emojiVersion) {
        int localVersion = ProcessHelper.getUserCompanyPreferences().getInt(Constants.KEY_EMOJI_VERSION, -1);
        TLog.info(TAG, "handleEmojiUpdate,local:" + localVersion + ",latest:" + emojiVersion);
        if (emojiVersion > localVersion) {
            EmojiSyncRequest emojiSyncRequest = new EmojiSyncRequest(new BaseApiRequestCallback<EmojiSyncResponse>() {

                @Override
                public void handleInChildThread(ApiData<EmojiSyncResponse> data) {
                    super.handleInChildThread(data);
                    EmotionGlobalHelper.updateAllEmojiData(HiKernel.getHikernel().getAccount().getUserId(), data.resp.emojiList);
                    ProcessHelper.getUserCompanyPreferences().edit().putInt(Constants.KEY_EMOJI_VERSION, emojiVersion).apply();
                }

                @Override
                public void onStart() {
                    super.onStart();
                    TLog.info(TAG, "EmojiSyncRequest start");
                }

                @Override
                public void onSuccess(ApiData<EmojiSyncResponse> data) {

                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            HttpExecutor.execute(emojiSyncRequest);
        }
    }
}
