package com.twl.hi.foundation

import androidx.annotation.Keep
import com.google.gson.GsonBuilder
import com.techwolf.lib.tlog.TLog
import com.twl.hi.foundation.EmojiObject.Companion.toDbEntity
import com.twl.hi.foundation.EmojiObject.Companion.toDomainModel
import com.twl.hi.foundation.ShiningInfo.Companion.toDbEntity
import com.twl.hi.foundation.ShiningInfo.Companion.toDomainModel
import com.twl.hi.foundation.db.MessageEntity
import com.twl.hi.foundation.db.MessageStatusEntity
import com.twl.hi.foundation.db.MessageTableView
import com.twl.hi.foundation.model.message.ChatMessage
import com.twl.hi.foundation.model.message.MessageForAction
import com.twl.hi.foundation.model.message.MessageForAppCard
import com.twl.hi.foundation.model.message.MessageForAudio
import com.twl.hi.foundation.model.message.MessageForChatShare
import com.twl.hi.foundation.model.message.MessageForEmpty
import com.twl.hi.foundation.model.message.MessageForFile
import com.twl.hi.foundation.model.message.MessageForGroupCard
import com.twl.hi.foundation.model.message.MessageForHint
import com.twl.hi.foundation.model.message.MessageForImageCard
import com.twl.hi.foundation.model.message.MessageForLink
import com.twl.hi.foundation.model.message.MessageForLinkCall
import com.twl.hi.foundation.model.message.MessageForMarkdownText
import com.twl.hi.foundation.model.message.MessageForOnlineFile
import com.twl.hi.foundation.model.message.MessageForPic
import com.twl.hi.foundation.model.message.MessageForRedEnvelope
import com.twl.hi.foundation.model.message.MessageForRichText
import com.twl.hi.foundation.model.message.MessageForSticker
import com.twl.hi.foundation.model.message.MessageForSystemCard
import com.twl.hi.foundation.model.message.MessageForTaskCommentCard
import com.twl.hi.foundation.model.message.MessageForText
import com.twl.hi.foundation.model.message.MessageForUnknown
import com.twl.hi.foundation.model.message.MessageForUserCard
import com.twl.hi.foundation.model.message.MessageForVideo
import com.twl.hi.foundation.model.message.MessageForVideoMeetingCard
import com.twl.hi.foundation.model.message.Visitor
import com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard
import com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard.ExtensionCardInfo
import com.twl.hi.foundation.model.message.extensioncard.adapter.ElementTypeAdapter
import com.twl.hi.foundation.model.message.extensioncard.adapter.LineElementTypeAdapter
import com.twl.hi.foundation.model.message.extensioncard.adapter.ModuleTypeAdapter
import com.twl.hi.foundation.model.message.extensioncard.adapter.NoteElementTypeAdapter
import com.twl.hi.foundation.model.message.extensioncard.data.element.Element
import com.twl.hi.foundation.model.message.extensioncard.data.element.LineElement
import com.twl.hi.foundation.model.message.extensioncard.data.element.NoteElement
import com.twl.hi.foundation.model.message.extensioncard.data.modules.Module
import com.twl.utils.GsonUtils
import lib.twl.common.util.DbFieldEncryptUtil.decrypt
import lib.twl.common.util.DbFieldEncryptUtil.encrypt

private const val TAG = "MessagePersistenceVisitor"

private fun Any?.toJson(): String? {
    return if (this == null) null else GsonUtils.getGson().toJson(this)
}

private inline fun <reified T : Any> String?.fromJson(): T? {
    return GsonUtils.getGson().fromJson(this, T::class.java)
}

class MessageDbWritingVisitor(private val isEncryptedData: Boolean) : Visitor {

    private lateinit var entity: MessageEntity

    override fun visit(message: MessageForText) {
        entity.extStr = message.extraInfo.toJson()
    }

    override fun visit(message: MessageForRichText) {
        entity.extStr = message.richInfo.toJson()
    }

    override fun visit(message: MessageForMarkdownText) {
        entity.extStr = message.extraInfo.toJson()
    }

    override fun visit(message: MessageForPic) {
        entity.content = message.picInfo.toJson()
    }

    override fun visit(message: MessageForUserCard) {
        entity.content = message.userId
    }

    override fun visit(message: MessageForVideo) {
        entity.content = message.videoInfo.toJson()
    }

    override fun visit(message: MessageForAudio) {
        entity.content = message.audioInfo.toJson()
    }

    override fun visit(message: MessageForVideoMeetingCard) {
        entity.content = message.videoMeetingInfo.toJson()
    }

    override fun visit(message: MessageForFile) {
        entity.content = message.fileInfo.toJson()
    }

    override fun visit(message: MessageForSticker) {
        entity.content = message.stickerInfo.toJson()
    }

    override fun visit(message: MessageForChatShare) {
        entity.content = message.chatShareInfo.toJson()
    }

    override fun visit(message: MessageForRedEnvelope) {
        entity.content = message.redEnvelope.toJson()
    }

    override fun visit(message: MessageForLink) {
        entity.content = message.linkInfo.toJson()
    }

    override fun visit(message: MessageForImageCard) {
        entity.content = message.imageCardInfo.toJson()
    }

    override fun visit(message: MessageForLinkCall) {
        entity.extStr = message.linkInfo.toJson()
    }

    override fun visit(message: MessageForOnlineFile) {
        entity.content = message.fileInfo.toJson()
    }

    override fun visit(message: MessageForGroupCard) {
        entity.content = message.groupCard.toJson()
    }

    override fun visit(message: MessageForAppCard) {
        entity.content = message.hiAppCardInfo.toJson()
    }

    override fun visit(message: MessageForAction) {
        entity.extStr = message.body.toJson()
    }

    override fun visit(message: MessageForHint) {
        entity.extStr = message.hintInfo.toJson()
    }

    override fun visit(message: MessageForExtensionCard) {
        entity.extStr = message.cardInfo.toJson()
    }

    override fun visit(message: MessageForEmpty) {}

    override fun visit(message: MessageForTaskCommentCard) {
        entity.content = message.info.toJson()
    }

    override fun visit(message: MessageForSystemCard) {
        entity.content = message.cardInfo.toJson()
        if (message.scheduleCardInfo != null) {
            entity.extStr = message.scheduleCardInfo.toJson()
        } else if (message.taskInfo != null) {
            entity.extStr = message.taskInfo.toJson()
        }
    }

    override fun visit(message: MessageForUnknown) {}

    fun encode(message: ChatMessage): Pair<MessageEntity, MessageStatusEntity> {
        val record = MessageEntity(
            mid = message.mid,
            seq = message.seq,
            mediaType = message.mediaType,
            senderId = message.sender.senderId,
            senderType = message.sender.senderType,
            chatId = message.chatId,
            chatType = message.type,
            cmid = message.cmid,
            time = message.time,
            isShow = message.isShow,
            replyId = message.messageReply?.replyId ?: 0,
            replySnapshot = message.messageReply?.replySnapshot,
            topId = message.messageReply?.topId ?: 0,
            replyCount = message.replyCount,
            transformer = message.transformer,
            version = message.version,
            updateTime = message.updateTime,
            isDeleted = message.isDeleted,
            isBadged = message.isBadged,
            isWithdrawn = message.isWithdrawn,
        ).apply {
            content = message.content
            extension = if (message.markInfo != null
                || message.urgency != null
                || message.emojiReplies.isNotEmpty()
                || message.replyUserIds.isNotEmpty()
                || message.nlpFeedBack != null
            ) {
                ExtensionInfo(
                    message.emojiReplies.map { it.toDbEntity() },
                    message.urgency?.toDbEntity(),
                    MarkInfo(message.markTime, message.markUserId),
                    ReplyIds(message.replyUserIds),
                    message.nlpFeedBack?.let { NlpFeedBack(it.nlpId, it.feedBackType) },
                    pbExtension = message.pbExtension
                ).toJson()
            } else ""
        }
        entity = record
        message.accept(this)
        // 加密
        record.content = if (isEncryptedData) encrypt(record.content) else record.content
        record.extStr = if (isEncryptedData) encrypt(record.extStr) else record.extStr
        return record to MessageStatusEntity(message.mid, message.status, true)
    }

    fun encode(messages: List<ChatMessage>): List<Pair<MessageEntity, MessageStatusEntity>> {
        return messages.map { encode(it) }
    }
}

@Keep
private class ExtensionInfo(
    val innerEmojiObjects: List<EmojiObject>?,
    val shining: ShiningInfo?,
    val markInfo: MarkInfo?,
    val reply: ReplyIds?,
    val nlpFeedBack: NlpFeedBack?,
    val pbExtension:String? = ""
)

@Keep
private class EmojiObject(
    val type: Int,
    val userIds: List<String>?,
) {
    companion object {
        fun EmojiObject.toDomainModel(): ChatMessage.EmojiReply {
            return ChatMessage.EmojiReply(type, userIds?.toMutableList() ?: mutableListOf())
        }

        fun ChatMessage.EmojiReply.toDbEntity(): EmojiObject {
            return EmojiObject(type, userIds)
        }
    }
}

@Keep
private class ShiningInfo(
    val senderId: String?,
    val shiningId: String?,
    val users: List<ShiningUserInfo>?,
    val shiningType: Int, // 0:非加急； 1:自己发送的加急消息； 2:别人加急给我的
    val shiningStatus: Int // 0:未处理； 1:已处理； 2:稍后处理
) {
    companion object {
        fun ShiningInfo.toDomainModel(): ChatMessage.Urgency? {
            return senderId?.let {
                ChatMessage.Urgency(
                    senderId,
                    shiningId ?: "",
                    users?.map { ChatMessage.UrgencyTarget(it.id ?: "", it.status) } ?: emptyList(),
                    shiningType,
                    shiningStatus
                )
            }
        }

        fun ChatMessage.Urgency.toDbEntity(): ShiningInfo {
            return ShiningInfo(
                senderId,
                shiningId,
                users.map { ShiningUserInfo(it.id, it.status) },
                urgencyType,
                urgencyStatus
            )
        }
    }
}

@Keep
//加急消息接收人信息
private class ShiningUserInfo(
    val id: String?, // 用户id
    val status: Int // 处理状态:0-未处理，1-已处理，2-稍后处理
)

@Keep
private class MarkInfo(
    val markTime: Long,
    val markUserId: String?
)

@Keep
private class ReplyIds(val userIds: List<String>?)

@Keep
private class NlpFeedBack(
    val nlpId: String?,
    val feedBackType: Int? // 评价结果 0-已解决 1-未解决 无-都显示
)

class MessageDbReadingVisitor(private val isEncryptedData: Boolean) : Visitor {

    private lateinit var entity: MessageEntity

    override fun visit(message: MessageForText) {
        message.extraInfo = entity.extStr.fromJson()
    }

    override fun visit(message: MessageForRichText) {
        message.richInfo = entity.extStr.fromJson()
    }

    override fun visit(message: MessageForMarkdownText) {
        message.extraInfo = entity.extStr.fromJson()
    }

    override fun visit(message: MessageForPic) {
        val picInfo = entity.content.fromJson<MessageForPic.PicInfo>()
        message.picInfo.original = picInfo?.original
        message.picInfo.tiny = picInfo?.tiny
        message.picInfo.local = picInfo?.local
        message.picInfo.saveUrl = picInfo?.saveUrl
    }

    override fun visit(message: MessageForUserCard) {
        message.userId = entity.content
    }

    override fun visit(message: MessageForVideo) {
        message.videoInfo = entity.content.fromJson()
    }

    override fun visit(message: MessageForAudio) {
        message.audioInfo = entity.content.fromJson()
    }

    override fun visit(message: MessageForVideoMeetingCard) {
        message.videoMeetingInfo = entity.content.fromJson()
    }

    override fun visit(message: MessageForFile) {
        message.fileInfo = entity.content.fromJson()
    }

    override fun visit(message: MessageForSticker) {
        message.stickerInfo = entity.content.fromJson()
    }

    override fun visit(message: MessageForChatShare) {
        message.chatShareInfo = entity.content.fromJson()
    }

    override fun visit(message: MessageForRedEnvelope) {
        message.redEnvelope = entity.content.fromJson()
    }

    override fun visit(message: MessageForLink) {
        message.linkInfo = entity.content.fromJson()
    }

    override fun visit(message: MessageForImageCard) {
        message.imageCardInfo = entity.content.fromJson()
    }

    override fun visit(message: MessageForLinkCall) {
        message.linkInfo = entity.extStr.fromJson()
    }

    override fun visit(message: MessageForOnlineFile) {
        message.fileInfo = entity.content.fromJson()
    }

    override fun visit(message: MessageForGroupCard) {
        message.groupCard = entity.content.fromJson()
    }

    override fun visit(message: MessageForAppCard) {
        message.hiAppCardInfo = entity.content.fromJson()
    }

    override fun visit(message: MessageForAction) {
        message.body = MessageForAction.createActionBody(entity.content ?: "", entity.extStr)
        message.isShow = false
    }

    override fun visit(message: MessageForHint) {
        message.hintInfo = entity.extStr.fromJson()
    }

    override fun visit(message: MessageForExtensionCard) {
        try {
            val gson = GsonBuilder()
                .registerTypeAdapter(Module::class.java, ModuleTypeAdapter())
                .registerTypeAdapter(Element::class.java, ElementTypeAdapter())
                .registerTypeAdapter(LineElement::class.java, LineElementTypeAdapter())
                .registerTypeAdapter(NoteElement::class.java, NoteElementTypeAdapter())
                .create()
            message.cardInfo = gson.fromJson(entity.extStr, ExtensionCardInfo::class.java)
        } catch (e: Exception) {
            TLog.error(TAG, "decode extension card error: ${message.mid} ,$e")
        }

    }

    override fun visit(message: MessageForEmpty) {
        message.isShow = false
    }

    override fun visit(message: MessageForTaskCommentCard) {
        message.info = entity.content.fromJson()
    }

    override fun visit(message: MessageForSystemCard) {
        message.cardInfo = entity.content.fromJson()
        message.createExtBody(entity.extStr)
    }

    override fun visit(message: MessageForUnknown) {}

    fun decode(messageView: MessageTableView): ChatMessage {
        val record = messageView.record
        val message = MessageFactory.createMessage(record.mediaType).apply {
            mid = record.mid
            seq = record.seq
            mediaType = record.mediaType
            sender = ChatMessage.MessageSender(record.senderId, record.senderType)
            chatId = record.chatId
            type = record.chatType
            isShow = record.isShow
            isBadged = record.isBadged
            isWithdrawn = record.isWithdrawn
            status = messageView.status
            // 解密
            content = if (isEncryptedData) decrypt(record.content) else record.content
            cmid = record.cmid
            time = record.time
            if (record.replyId > 0) {
                messageReply =
                    ChatMessage.MessageReply(record.topId, record.replyId, record.replySnapshot)
            }
            replyCount = record.replyCount
            isDeleted = record.isDeleted
            record.extension.fromJson<ExtensionInfo>()?.let { ext ->
                emojiReplies = ext.innerEmojiObjects?.map { it.toDomainModel() } ?: emptyList()
                urgency = ext.shining?.toDomainModel()
                markInfo = ext.markInfo?.let { ChatMessage.MarkInfo(it.markUserId ?: "", it.markTime) }
                replyUserIds = ext.reply?.userIds ?: emptyList()
                nlpFeedBack = ChatMessage.NlpFeedBack().apply {
                    nlpId = ext.nlpFeedBack?.nlpId
                    feedBackType = ext.nlpFeedBack?.feedBackType
                }
                pbExtension = ext.pbExtension
            }
        }
        record.content = if (isEncryptedData) decrypt(record.content) else record.content
        record.extStr = if (isEncryptedData) decrypt(record.extStr) else record.extStr
        this.entity = record
        try {
            message.accept(this)
        } catch (e: Exception) {
            TLog.error(TAG, e, "消息数据解析异常")
        }
        return message
    }

    fun decode(records: List<MessageTableView>): List<ChatMessage> {
        return records.map { decode(it) }
    }
}
