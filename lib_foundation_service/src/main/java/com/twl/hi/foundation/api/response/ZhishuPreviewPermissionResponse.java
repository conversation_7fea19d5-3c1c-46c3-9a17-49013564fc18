package com.twl.hi.foundation.api.response;

import com.twl.http.client.HttpResponse;

import java.util.List;

public class ZhishuPreviewPermissionResponse extends HttpResponse {

    public List<Bean> data;

    public static class Bean {

        // "visitCode": "asdfadsf", // 文档唯一标识
        // "title": "直书链接适配方案设计",
        // "icon": "https://asdfasd.icon",  // icon地址
        // "fileType": 1, // 1:文档（doc、docx、轻文档）   2:表格（xls、xlsx） 3:ppt （ppt、pptx） 4:图片（png、jpg） 5:pdf  6:其他
        // "previewUrl": "https://www.zhishu.zhipin.com/preview-test.png",
        // "status": 1, // 0 删除 1正常
        // "curPermission": 1, // 拥有权限 1:可查看 2:可编辑
        // "permissions": [1, 2] // 可赋予权限列表 1:可查看 2:可编辑
        // "otherPermission": 2 // 接收者拥有的权限 -1:无权限 1:可查看 2:可编辑

        public String visitCode;
        public String title;
        public String icon;
        public int fileType;
        public String previewUrl;
        public int status;
        public int curPermission;
        public int[] permissions;
        public int otherPermission;
    }

}
