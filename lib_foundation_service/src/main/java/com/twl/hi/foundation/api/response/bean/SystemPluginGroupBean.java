package com.twl.hi.foundation.api.response.bean;

import java.io.Serializable;
import java.util.List;

public class SystemPluginGroupBean implements Serializable, Cloneable {
    private static final long serialVersionUID = -8936855936087789467L;
    public static final int ATTENDANCE_PLUGIN_ID = -2;
    public static final int FAVORITE_PLUGIN_ID = -1;
    public static final long ALL_PLUGIN_ID = 99;
    public String pluginGroupId;// 插件分组id
    public String pluginGroupName;// 插件分组名称
    public List<SystemPluginBean> pluginList;
}