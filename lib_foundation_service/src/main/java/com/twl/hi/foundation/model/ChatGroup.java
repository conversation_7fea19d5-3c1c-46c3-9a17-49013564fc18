package com.twl.hi.foundation.model;

import androidx.annotation.NonNull;
import androidx.room.Entity;

import java.io.Serializable;

import lib_twl_db_config.DBConstants;

/**
 * <AUTHOR>
 * @date 2022/5/26
 * description:会话标签
 */
@Entity(tableName = DBConstants.TAB_CHAT_GROUP, primaryKeys = {"id"})
public class ChatGroup implements Serializable {

    @NonNull
    private String id = "";

    private String name;

    private int index;

    private String avatar;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String conversationGroupName) {
        this.name = conversationGroupName;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    @Override
    public String toString() {
        return "ChatGroup{" +
                "id=" + id +
                ", index=" + index +
                '}';
    }
}
