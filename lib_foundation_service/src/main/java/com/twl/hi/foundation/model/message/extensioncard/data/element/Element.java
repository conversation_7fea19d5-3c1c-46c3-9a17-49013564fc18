package com.twl.hi.foundation.model.message.extensioncard.data.element;

import com.twl.hi.foundation.model.message.extensioncard.data.element.parser.Parser;
import com.zhipin.bosshi.mqtt.codec.ChatProtocolStr;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/8
 * description:消息卡片 元素基类
 */
public class Element implements Parser, Serializable {
    /**
     * "text", "文本"
     */
    public static final int ELEMENT_TAG_TEXT = 1;
    /**
     * 图片
     */
    public static final int ELEMENT_TAG_IMAGE = 2;
    /**
     * 多文本
     */
    public static final int ELEMENT_TAG_FIELD = 3;
    /**
     * 回传参数
     */
    public static final int ELEMENT_TAG_CALLBACK = 4;
    /**
     * 二次确认
     */
    public static final int ELEMENT_TAG_CONFIRM = 5;
    /**
     * 差异化跳转
     */
    public static final int ELEMENT_TAG_MULTI_URL = 6;
    /**
     * 选项
     */
    public static final int ELEMENT_TAG_OPTION = 7;
    /**
     * 按钮
     */
    public static final int ELEMENT_TAG_BUTTON = 8;
    /**
     * 下拉列表
     */
    public static final int ELEMENT_LIST_SELECTOR = 9;
    /**
     * 文本输入框
     */
    public static final int ELEMENT_TEXT_INPUT = 10;
    public static final int ELEMENT_STOP_STREAM = 11;
    public static final int ELEMENT_PRAISE_STREAM = 12;
    public static final int ELEMENT_THUMBS_DOWN_STREAM = 13;
    public static final int ELEMENT_COPY_STREAM = 14;
    public static final int ELEMENT_REFRESH_STREAM = 15;

    /**
     * 元素类型
     */
    private int elementTag;

    public int getElementTag() {
        return elementTag;
    }

    public void setElementTag(int elementTag) {
        this.elementTag = elementTag;
    }

    @Override
    public Element parseFromHiElement(ChatProtocolStr.HiCardElementStr hiCardElement) {
        elementTag = hiCardElement.getElementTag();
        return this;
    }

    public boolean needRefresh(){
        return false;
    }

}
