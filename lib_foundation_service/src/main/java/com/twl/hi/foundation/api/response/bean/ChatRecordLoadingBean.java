package com.twl.hi.foundation.api.response.bean;

import android.view.animation.Interpolator;

/**
 * Author : <PERSON><PERSON><PERSON><PERSON> .
 * Date   : On 2024/1/5
 * Email  : Contact <EMAIL>
 * Desc   :
 */

public class ChatRecordLoadingBean {

    private int durationPerRound;

    private Interpolator loadingInterpolator;

    public Interpolator getLoadingInterpolator() {
        return loadingInterpolator;
    }

    public void setLoadingInterpolator(Interpolator loadingInterpolator) {
        this.loadingInterpolator = loadingInterpolator;
    }

    public int getDurationPerRound() {
        return durationPerRound;
    }

    public void setDurationPerRound(int durationPerRound) {
        this.durationPerRound = durationPerRound;
    }
}
