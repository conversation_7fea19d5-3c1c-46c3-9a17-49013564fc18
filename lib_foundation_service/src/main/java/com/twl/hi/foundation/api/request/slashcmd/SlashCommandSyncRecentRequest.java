package com.twl.hi.foundation.api.request.slashcmd;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.response.SlashCommandSyncRecentResponse;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * 最近使用斜杠指令
 * <p>
 * Created by tanshicheng on 2023/7/3
 */
public class SlashCommandSyncRecentRequest extends BaseApiRequest<SlashCommandSyncRecentResponse> {

    @Expose
    public String chatId = "";

    @Expose
    public Integer chatType = 1;

    public SlashCommandSyncRecentRequest(BaseApiRequestCallback<SlashCommandSyncRecentResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_SLASH_COMMAND_SYNC_RECENT;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}

