package com.twl.hi.foundation.db;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.twl.hi.foundation.model.SearchRecord;

import java.util.List;

/**
 * Created by ChaiJiangpeng on 2020/8/25
 * Describe:
 */
@Dao
public interface SearchRecordDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(SearchRecord searchRecord);

    @Query("select count(*) from tb_search_record")
    int getCount();

    @Query("delete from tb_search_record where modifyTime = (select modifyTime from tb_search_record order by modifyTime limit 1)")
    void deleteOldData();

    @Query("delete from tb_search_record")
    void deleteAllData();

    @Query("select * from tb_search_record where searchKey = :key order by modifyTime desc limit 3")
    List<SearchRecord> searchLastSearchRecord(String key);
}
