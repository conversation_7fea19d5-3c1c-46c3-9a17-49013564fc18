package com.twl.hi.foundation.api.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.hi.foundation.api.response.EmailSummaryResponse
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.http.client.BaseApiRequest
import com.twl.http.config.RequestMethod

/**
 * Author : Xuweixiang .
 * Date   : On 2023/5/24
 * Email  : Contact <EMAIL>
 * Desc   : 生成邮件总结
 *
 */

class EmailGenerateSummaryRequest(callback: BaseApiRequestCallback<EmailSummaryResponse>) :
    BaseApiRequest<EmailSummaryResponse>(callback) {

    @Expose
    @JvmField
    var aiSummaryId: String? = ""

    // 公共邮箱地址，邮箱公共账号使用该地址
    @Expose
    @JvmField
    var email = ServiceManager.getInstance().emailService.currentCacheAccountAddress

    override fun getUrl(): String {
        return URLConfig.URL_EMAIL_GENERATE_SUMMARY
    }

    override fun getMethod(): RequestMethod {
        return RequestMethod.GET
    }
}