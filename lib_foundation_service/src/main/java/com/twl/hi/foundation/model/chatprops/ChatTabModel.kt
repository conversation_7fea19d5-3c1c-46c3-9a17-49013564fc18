package com.twl.hi.foundation.model.chatprops

import androidx.room.Entity
import lib_twl_db_config.DBConstants

/**
 *
 * chat tab 基础数据模型
 *
 * Created by tanshicheng on 2022/11/17
 */
@Entity(
    tableName = DBConstants.TAB_CHAT_TAB,
    primaryKeys = ["id"]
)
class ChatTabModel {
    var id: String = ""
    var name: String? = null
    var tabContent: String? = null
    var icon: String? = null
    var sortIndex: Int? = null
    var type: Int? = null

    var chatId: String? = null
    var chatType: Int? = null
}

class ChatTabRequestModel {
    var id: String? = null
    var name: String? = null
    var tabContent: String? = null
    var icon: String? = null
    var sortIndex: Int? = null
    var type: Int? = null

    var companyId: String? = null
    var chatId: String? = null
    var chatType: Int? = null
}

