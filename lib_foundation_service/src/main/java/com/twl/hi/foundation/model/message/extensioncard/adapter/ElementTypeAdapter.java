package com.twl.hi.foundation.model.message.extensioncard.adapter;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.twl.hi.foundation.model.message.extensioncard.data.element.Element;
import com.twl.hi.foundation.model.message.extensioncard.data.element.ElementFactory;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2023/3/8
 * description: Gson使用 Element适配器
 */
public class ElementTypeAdapter implements JsonDeserializer<Element> {

    @Override
    public Element deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        JsonObject asJsonObject = json.getAsJsonObject();
        int tag = asJsonObject.get("elementTag").getAsInt();
        return ElementFactory.createElement(tag, json);
    }
}
