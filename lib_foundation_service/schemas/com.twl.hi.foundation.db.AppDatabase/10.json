{"formatVersion": 1, "database": {"version": 10, "identityHash": "da1810c734ba4b96e005b7dd6c1ee0c5", "entities": [{"tableName": "tb_data_sync", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` INTEGER NOT NULL, `extension` TEXT, `version` INTEGER NOT NULL, `isRealTimeData` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "extension", "columnName": "extension", "affinity": "TEXT", "notNull": false}, {"fieldPath": "version", "columnName": "version", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isRealTimeData", "columnName": "isRealTimeData", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_message", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`mid` INTEGER NOT NULL, `mediaType` INTEGER NOT NULL, `senderId` TEXT NOT NULL, `senderType` INTEGER NOT NULL DEFAULT 1, `chatId` TEXT NOT NULL, `chatType` INTEGER NOT NULL, `seq` INTEGER NOT NULL, `isBadged` INTEGER NOT NULL, `isWithdrawn` INTEGER NOT NULL, `cmid` INTEGER NOT NULL, `time` INTEGER NOT NULL, `updateTime` INTEGER NOT NULL, `isDeleted` INTEGER NOT NULL, `isShow` INTEGER NOT NULL, `replyId` INTEGER NOT NULL, `topId` INTEGER NOT NULL, `replyCount` INTEGER NOT NULL, `transformer` INTEGER NOT NULL, `version` INTEGER NOT NULL, `content` TEXT, `extension` TEXT, `extStr` TEXT, PRIMARY KEY(`mid`))", "fields": [{"fieldPath": "mid", "columnName": "mid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mediaType", "columnName": "mediaType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderId", "columnName": "senderId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "senderType", "columnName": "senderType", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "chatId", "columnName": "chatId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatType", "columnName": "chatType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "seq", "columnName": "seq", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isBadged", "columnName": "isBadged", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isWithdrawn", "columnName": "isWithdrawn", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cmid", "columnName": "cmid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateTime", "columnName": "updateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDeleted", "columnName": "isDeleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isShow", "columnName": "isShow", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "replyId", "columnName": "replyId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "topId", "columnName": "topId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "replyCount", "columnName": "replyCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "transformer", "columnName": "transformer", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "version", "columnName": "version", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": false}, {"fieldPath": "extension", "columnName": "extension", "affinity": "TEXT", "notNull": false}, {"fieldPath": "extStr", "columnName": "extStr", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["mid"]}, "indices": [{"name": "index_tb_message_chatId_chatType_seq", "unique": false, "columnNames": ["chatId", "chatType", "seq"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_tb_message_chatId_chatType_seq` ON `${TABLE_NAME}` (`chatId`, `chatType`, `seq`)"}], "foreignKeys": []}, {"tableName": "tb_message_status", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`mid` INTEGER NOT NULL, `status` INTEGER NOT NULL, `isSynced` INTEGER NOT NULL, PRIMARY KEY(`mid`))", "fields": [{"fieldPath": "mid", "columnName": "mid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["mid"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_zhishu", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uniqueId` TEXT NOT NULL, `title` TEXT, `type` INTEGER NOT NULL, `iconUrl` TEXT, `previewUrl` TEXT, `status` INTEGER NOT NULL, `myPermission` INTEGER NOT NULL, `dirty` INTEGER NOT NULL, PRIMARY KEY(`uniqueId`))", "fields": [{"fieldPath": "uniqueId", "columnName": "uniqueId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iconUrl", "columnName": "iconUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "previewUrl", "columnName": "previewUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "myPermission", "columnName": "myPermission", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dirty", "columnName": "dirty", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uniqueId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_zhishu_permission", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`chatId` TEXT NOT NULL, `type` INTEGER NOT NULL, `visitCode` TEXT NOT NULL, `permissions` TEXT, `assigned` INTEGER NOT NULL, `dirty` INTEGER NOT NULL, PRIMARY KEY(`chatId`, `type`, `visitCode`), FOREIGN KEY(`chatId`, `type`) REFERENCES `tb_conversation`(`chatId`, `type`) ON UPDATE NO ACTION ON DELETE NO ACTION )", "fields": [{"fieldPath": "chatId", "columnName": "chatId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "visitCode", "columnName": "visitCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "permissions", "columnName": "permissions", "affinity": "TEXT", "notNull": false}, {"fieldPath": "assigned", "columnName": "assigned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dirty", "columnName": "dirty", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["chatId", "type", "visitCode"]}, "indices": [], "foreignKeys": [{"table": "tb_conversation", "onDelete": "NO ACTION", "onUpdate": "NO ACTION", "columns": ["chatId", "type"], "referencedColumns": ["chatId", "type"]}]}, {"tableName": "tb_message_hyperlink", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`resourceId` TEXT NOT NULL, `type` INTEGER NOT NULL, `hideIcon` INTEGER NOT NULL, `displayTitle` TEXT, `destination` TEXT, `imagePreview` TEXT, `textPreview` TEXT, `status` INTEGER NOT NULL, `dirty` INTEGER NOT NULL, PRIMARY KEY(`resourceId`, `type`))", "fields": [{"fieldPath": "resourceId", "columnName": "resourceId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hideIcon", "columnName": "hideIcon", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "displayTitle", "columnName": "displayTitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "destination", "columnName": "destination", "affinity": "TEXT", "notNull": false}, {"fieldPath": "imagePreview", "columnName": "imagePreview", "affinity": "TEXT", "notNull": false}, {"fieldPath": "textPreview", "columnName": "textPreview", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dirty", "columnName": "dirty", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["resourceId", "type"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_conversation", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`chatId` TEXT NOT NULL, `type` INTEGER NOT NULL, `miniSeq` INTEGER NOT NULL, `top` INTEGER NOT NULL, `silence` INTEGER NOT NULL, `archive` INTEGER NOT NULL, `finished` INTEGER NOT NULL, `marked` INTEGER NOT NULL, `markTime` INTEGER NOT NULL, `sortVersion` INTEGER NOT NULL, `deleted` INTEGER NOT NULL, PRIMARY KEY(`chatId`, `type`))", "fields": [{"fieldPath": "chatId", "columnName": "chatId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "miniSeq", "columnName": "miniSeq", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "top", "columnName": "top", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "silence", "columnName": "silence", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "archive", "columnName": "archive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "finished", "columnName": "finished", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "marked", "columnName": "marked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "markTime", "columnName": "markTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sortVersion", "columnName": "sortVersion", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["chatId", "type"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_conversation_receipt", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`chatId` TEXT NOT NULL, `type` INTEGER NOT NULL, `lastVisitMid` INTEGER NOT NULL, `latestMessageId` INTEGER NOT NULL, `latestMessageSeq` INTEGER NOT NULL, `latestMessageTime` INTEGER NOT NULL, `unreadCount` INTEGER NOT NULL, `atMeCount` INTEGER NOT NULL, `atMeMethod` TEXT NOT NULL, `hasFocus` INTEGER NOT NULL, `calculateTime` INTEGER NOT NULL, PRIMARY KEY(`chatId`, `type`), FOREIGN KEY(`chatId`, `type`) REFERENCES `tb_conversation`(`chatId`, `type`) ON UPDATE NO ACTION ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED)", "fields": [{"fieldPath": "chatId", "columnName": "chatId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastVisitMid", "columnName": "lastVisitMid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "latestMessageId", "columnName": "latestMessageId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "latestMessageSeq", "columnName": "latestMessageSeq", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "latestMessageTime", "columnName": "latestMessageTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unreadCount", "columnName": "unreadCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "atMeCount", "columnName": "atMeCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "atMeMethod", "columnName": "atMeMethod", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hasFocus", "columnName": "hasFocus", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "calculateTime", "columnName": "calculateTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["chatId", "type"]}, "indices": [], "foreignKeys": [{"table": "tb_conversation", "onDelete": "NO ACTION", "onUpdate": "NO ACTION", "columns": ["chatId", "type"], "referencedColumns": ["chatId", "type"]}]}, {"tableName": "tb_conversation_draft", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`chatId` TEXT NOT NULL, `type` INTEGER NOT NULL, `modifyTime` INTEGER NOT NULL, `markdownDraft` TEXT, PRIMARY KEY(`chatId`, `type`))", "fields": [{"fieldPath": "chatId", "columnName": "chatId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifyTime", "columnName": "modifyTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "markdownDraft", "columnName": "markdownDraft", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["chatId", "type"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_chat_group", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT, `index` INTEGER NOT NULL, `avatar` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "index", "columnName": "index", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_conversation_group", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`chatId` TEXT NOT NULL, `type` INTEGER NOT NULL, `chatGroupId` TEXT NOT NULL, PRIMARY KEY(`chatId`, `type`, `chatGroupId`), FOREIGN KEY(`chatId`, `type`) REFERENCES `tb_conversation`(`chatId`, `type`) ON UPDATE NO ACTION ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED)", "fields": [{"fieldPath": "chatId", "columnName": "chatId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chatGroupId", "columnName": "chatGroupId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["chatId", "type", "chatGroupId"]}, "indices": [], "foreignKeys": [{"table": "tb_conversation", "onDelete": "NO ACTION", "onUpdate": "NO ACTION", "columns": ["chatId", "type"], "referencedColumns": ["chatId", "type"]}]}, {"tableName": "tb_chat_tab", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT, `tabContent` TEXT, `icon` TEXT, `sortIndex` INTEGER, `type` INTEGER, `chatId` TEXT, `chatType` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tab<PERSON>ontent", "columnName": "tab<PERSON>ontent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sortIndex", "columnName": "sortIndex", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chatId", "columnName": "chatId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatType", "columnName": "chatType", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_chat_top_message", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`chatId` TEXT NOT NULL, `chatType` INTEGER NOT NULL, `topMessage` TEXT, PRIMARY KEY(`chatId`, `chatType`))", "fields": [{"fieldPath": "chatId", "columnName": "chatId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatType", "columnName": "chatType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "topMessage", "columnName": "topMessage", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["chatId", "chatType"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_scheduled_msg", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `msgType` INTEGER NOT NULL, `msgContent` TEXT, `sendTime` INTEGER, `replyId` INTEGER, `chatId` TEXT, `chatType` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "msgType", "columnName": "msgType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "msg<PERSON><PERSON>nt", "columnName": "msg<PERSON><PERSON>nt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sendTime", "columnName": "sendTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "replyId", "columnName": "replyId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chatId", "columnName": "chatId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatType", "columnName": "chatType", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_contact", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `userName` TEXT, `userNamePy` TEXT, `userNamePyList` TEXT, `title` TEXT, `email` TEXT, `gender` INTEGER NOT NULL, `deptIds` TEXT, `status` INTEGER NOT NULL, `userType` INTEGER NOT NULL, `aboveDeptIds` TEXT, `nickNamePyList` TEXT, `nickName` TEXT, `nickNamePy` TEXT, `remark` TEXT, `remarkPy` TEXT, `remarkPyList` TEXT, `avatarDecoration` TEXT, `avatar` TEXT, `tinyAvatar` TEXT, `showDeptIds` TEXT, `companySetting` TEXT, `introduce` TEXT, `tag` TEXT, `visible` INTEGER NOT NULL, `relation` INTEGER NOT NULL, `userNameParticiples` TEXT, `nickNameParticiples` TEXT, `remarkParticiples` TEXT, `remarkPysString` TEXT, `nickNamePysString` TEXT, `userNamePysString` TEXT, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userNamePy", "columnName": "userNamePy", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userNamePyList", "columnName": "userNamePyList", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "gender", "columnName": "gender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deptIds", "columnName": "deptIds", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userType", "columnName": "userType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "aboveDeptIds", "columnName": "aboveDeptIds", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nickNamePyList", "columnName": "nickNamePyList", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nickNamePy", "columnName": "nickNamePy", "affinity": "TEXT", "notNull": false}, {"fieldPath": "remark", "columnName": "remark", "affinity": "TEXT", "notNull": false}, {"fieldPath": "remarkPy", "columnName": "remarkPy", "affinity": "TEXT", "notNull": false}, {"fieldPath": "remarkPyList", "columnName": "remarkPyList", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatarDecoration", "columnName": "avatarDecoration", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tinyAvatar", "columnName": "tinyAvatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "showDeptIds", "columnName": "showDeptIds", "affinity": "TEXT", "notNull": false}, {"fieldPath": "companySetting", "columnName": "companySetting", "affinity": "TEXT", "notNull": false}, {"fieldPath": "introduce", "columnName": "introduce", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tag", "columnName": "tag", "affinity": "TEXT", "notNull": false}, {"fieldPath": "visible", "columnName": "visible", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "relation", "columnName": "relation", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userNameParticiples", "columnName": "userNameParticiples", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nickNameParticiples", "columnName": "nickNameParticiples", "affinity": "TEXT", "notNull": false}, {"fieldPath": "remarkParticiples", "columnName": "remarkParticiples", "affinity": "TEXT", "notNull": false}, {"fieldPath": "remarkPysString", "columnName": "remarkPysString", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nickNamePysString", "columnName": "nickNamePysString", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userNamePysString", "columnName": "userNamePysString", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_department", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`deptId` TEXT NOT NULL, `parentId` TEXT, `deptName` TEXT, `deptNamePy` TEXT, `status` INTEGER, `visible` INTEGER NOT NULL, `managerId` TEXT, `cateCode` INTEGER NOT NULL DEFAULT 1, `deptNameNumPy` TEXT, `sort` INTEGER NOT NULL, PRIMARY KEY(`deptId`))", "fields": [{"fieldPath": "deptId", "columnName": "deptId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "parentId", "columnName": "parentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deptName", "columnName": "deptName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deptNamePy", "columnName": "deptNamePy", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "visible", "columnName": "visible", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "managerId", "columnName": "managerId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "cateCode", "columnName": "cateCode", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "deptNameNumPy", "columnName": "deptNameNumPy", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["deptId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `userName` TEXT, `nickName` TEXT, `avatar` TEXT, `tinyAvatar` TEXT, `avatarDecoration` TEXT, `defaultComId` TEXT, `defaultComName` TEXT, `companiesString` TEXT, `companies` TEXT, `t` TEXT, `gender` INTEGER NOT NULL, `title` TEXT, `regionCode` INTEGER NOT NULL, `phone` TEXT, `phoneScope` INTEGER NOT NULL, `money` INTEGER NOT NULL, `entryDate` INTEGER NOT NULL, `workDays` INTEGER NOT NULL, `departmentsString` TEXT, `email` TEXT, `phoneNotify` INTEGER NOT NULL, `employeeNo` TEXT, `notifyWx` INTEGER NOT NULL, `zhipinPhone` TEXT, `zhipinWT` TEXT, `companySetting` TEXT, `managerIds` TEXT, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tinyAvatar", "columnName": "tinyAvatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatarDecoration", "columnName": "avatarDecoration", "affinity": "TEXT", "notNull": false}, {"fieldPath": "defaultComId", "columnName": "defaultComId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "defaultComName", "columnName": "defaultComName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "companiesString", "columnName": "companiesString", "affinity": "TEXT", "notNull": false}, {"fieldPath": "companies", "columnName": "companies", "affinity": "TEXT", "notNull": false}, {"fieldPath": "t", "columnName": "t", "affinity": "TEXT", "notNull": false}, {"fieldPath": "gender", "columnName": "gender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "regionCode", "columnName": "regionCode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": false}, {"fieldPath": "phoneScope", "columnName": "phoneScope", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "money", "columnName": "money", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "entryDate", "columnName": "entryDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "workDays", "columnName": "workDays", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "departmentsString", "columnName": "departmentsString", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "phoneNotify", "columnName": "phoneNotify", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "employeeNo", "columnName": "employeeNo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "notifyWx", "columnName": "notifyWx", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "zhipinPhone", "columnName": "zhipinPhone", "affinity": "TEXT", "notNull": false}, {"fieldPath": "zhipinWT", "columnName": "zhipinWT", "affinity": "TEXT", "notNull": false}, {"fieldPath": "companySetting", "columnName": "companySetting", "affinity": "TEXT", "notNull": false}, {"fieldPath": "managerIds", "columnName": "managerIds", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_group", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`groupId` TEXT NOT NULL, `ownerId` TEXT, `groupName` TEXT, `groupRemark` TEXT, `groupNamePy` TEXT, `avatar` TEXT, `tinyAvatar` TEXT, `bulletin` TEXT, `bulletinUpdateUid` TEXT, `bulletinUpdateTs` INTEGER NOT NULL, `top` INTEGER NOT NULL, `silence` INTEGER NOT NULL, `status` INTEGER NOT NULL, `myNickName` TEXT, `ownerAtAll` INTEGER NOT NULL, `ownerAddUser` INTEGER NOT NULL, `ownerEdit` INTEGER NOT NULL, `ownerEditChatTab` INTEGER NOT NULL, `ownerTopMessage` INTEGER NOT NULL, `managerIds` TEXT, `focusUserIds` TEXT, `enter` INTEGER NOT NULL, `deptId` TEXT, `taskGroup` TEXT, `isPublic` INTEGER NOT NULL, `lookType` INTEGER NOT NULL, `firstJoinTime` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`groupId`))", "fields": [{"fieldPath": "groupId", "columnName": "groupId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "ownerId", "columnName": "ownerId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupName", "columnName": "groupName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupRemark", "columnName": "groupRemark", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupNamePy", "columnName": "groupNamePy", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tinyAvatar", "columnName": "tinyAvatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bulletin", "columnName": "bulletin", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bulletinUpdateUid", "columnName": "bulletinUpdateUid", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bulletinUpdateTs", "columnName": "bulletinUpdateTs", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "top", "columnName": "top", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "silence", "columnName": "silence", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "myNickName", "columnName": "myNickName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ownerAtAll", "columnName": "ownerAtAll", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ownerAddUser", "columnName": "ownerAddUser", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ownerEdit", "columnName": "ownerEdit", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ownerEditChatTab", "columnName": "ownerEditChatTab", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ownerTopMessage", "columnName": "ownerTopMessage", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "managerIds", "columnName": "managerIds", "affinity": "TEXT", "notNull": false}, {"fieldPath": "focusUserIds", "columnName": "focusUserIds", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enter", "columnName": "enter", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deptId", "columnName": "deptId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "taskGroup", "columnName": "taskGroup", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isPublic", "columnName": "isPublic", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lookType", "columnName": "lookType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "firstJoinTime", "columnName": "firstJoinTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["groupId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_group_member", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `groupId` TEXT NOT NULL, `nickName` TEXT, `nickNamePy` TEXT, `status` INTEGER NOT NULL, `createTime` INTEGER NOT NULL, `deptUser` INTEGER NOT NULL, PRIMARY KEY(`groupId`, `userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "groupId", "columnName": "groupId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nickNamePy", "columnName": "nickNamePy", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deptUser", "columnName": "deptUser", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["groupId", "userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_group_robot", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`groupId` TEXT NOT NULL, `robotId` TEXT NOT NULL, `type` INTEGER NOT NULL, `status` INTEGER NOT NULL, `name` TEXT, `desc` TEXT, `avatar` TEXT, `createTime` INTEGER, PRIMARY KEY(`groupId`, `robotId`))", "fields": [{"fieldPath": "groupId", "columnName": "groupId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "robotId", "columnName": "robotId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "desc", "columnName": "desc", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["groupId", "robotId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_schedule", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`scheduleId` TEXT NOT NULL, `creatorId` TEXT, `content` TEXT, `contentEmpty` INTEGER NOT NULL, `targetDate` TEXT, `type` INTEGER NOT NULL, `status` INTEGER NOT NULL, `deleted` INTEGER NOT NULL, `beginTime` TEXT, `endTime` TEXT, `markType` INTEGER NOT NULL, `createTime` INTEGER NOT NULL, `finishTime` INTEGER NOT NULL, `meetingInfo` TEXT, `lastOpeLog` TEXT, `repeatType` INTEGER NOT NULL, `frequent` INTEGER NOT NULL, `repeatDays` TEXT, `repeatEnd` TEXT, `alertMins` TEXT, `parentId` TEXT, `separateList` TEXT, `files` TEXT, `schedule_desc` TEXT, `partnerModify` INTEGER NOT NULL, `partnerShare` INTEGER NOT NULL, `location` TEXT, `friendNum` INTEGER NOT NULL, `calenderGroupId` TEXT, `scheduleCreateGroupId` TEXT, `totalFriendCount` INTEGER NOT NULL, `linkMeetingType` INTEGER NOT NULL, `linkMeetingTag` TEXT, `linkMeetingUri` TEXT, `linkMeetingStatus` INTEGER NOT NULL, `canShare` INTEGER NOT NULL, `canEdit` INTEGER NOT NULL, `canDelete` INTEGER NOT NULL, `showCreator` INTEGER NOT NULL, `showPartner` INTEGER NOT NULL, `showButton` INTEGER NOT NULL, `showLinkMeeting` INTEGER NOT NULL, PRIMARY KEY(`scheduleId`))", "fields": [{"fieldPath": "scheduleId", "columnName": "scheduleId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "creatorId", "columnName": "creatorId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": false}, {"fieldPath": "contentEmpty", "columnName": "contentEmpty", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "targetDate", "columnName": "targetDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "beginTime", "columnName": "beginTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "endTime", "columnName": "endTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "markType", "columnName": "markType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "finishTime", "columnName": "finishTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "meetingInfo", "columnName": "meetingInfo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastOpeLog", "columnName": "lastOpeLog", "affinity": "TEXT", "notNull": false}, {"fieldPath": "repeatType", "columnName": "repeatType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "frequent", "columnName": "frequent", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "repeatDays", "columnName": "repeatDays", "affinity": "TEXT", "notNull": false}, {"fieldPath": "repeatEnd", "columnName": "repeatEnd", "affinity": "TEXT", "notNull": false}, {"fieldPath": "alertMins", "columnName": "alertMins", "affinity": "TEXT", "notNull": false}, {"fieldPath": "parentId", "columnName": "parentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "separateList", "columnName": "separateList", "affinity": "TEXT", "notNull": false}, {"fieldPath": "files", "columnName": "files", "affinity": "TEXT", "notNull": false}, {"fieldPath": "desc", "columnName": "schedule_desc", "affinity": "TEXT", "notNull": false}, {"fieldPath": "partnerModify", "columnName": "partnerModify", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "partnerShare", "columnName": "partnerShare", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "location", "columnName": "location", "affinity": "TEXT", "notNull": false}, {"fieldPath": "friend<PERSON>um", "columnName": "friend<PERSON>um", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "calenderGroupId", "columnName": "calenderGroupId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "scheduleCreateGroupId", "columnName": "scheduleCreateGroupId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "totalFriendCount", "columnName": "totalFriendCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "linkMeetingType", "columnName": "linkMeetingType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "linkMeetingTag", "columnName": "linkMeetingTag", "affinity": "TEXT", "notNull": false}, {"fieldPath": "linkMeetingUri", "columnName": "linkMeetingUri", "affinity": "TEXT", "notNull": false}, {"fieldPath": "linkMeetingStatus", "columnName": "linkMeetingStatus", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "canShare", "columnName": "canShare", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "canEdit", "columnName": "canEdit", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "canDelete", "columnName": "canDelete", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "showCreator", "columnName": "showCreator", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "showButton", "columnName": "showButton", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "showLinkMeeting", "columnName": "showLinkMeeting", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["scheduleId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_schedule_friend", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`scheduleId` TEXT NOT NULL, `friendId` TEXT NOT NULL, `friendType` INTEGER NOT NULL, `roleType` INTEGER NOT NULL, `deleted` INTEGER NOT NULL, `echoType` INTEGER NOT NULL, `echoTime` INTEGER NOT NULL, PRIMARY KEY(`scheduleId`, `friendId`, `friendType`), FOREIGN KEY(`scheduleId`) REFERENCES `tb_schedule`(`scheduleId`) ON UPDATE NO ACTION ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED)", "fields": [{"fieldPath": "scheduleId", "columnName": "scheduleId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "friendId", "columnName": "friendId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "friendType", "columnName": "friendType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "roleType", "columnName": "roleType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "echoType", "columnName": "echoType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "echoTime", "columnName": "echoTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["scheduleId", "friendId", "friendType"]}, "indices": [], "foreignKeys": [{"table": "tb_schedule", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["scheduleId"], "referencedColumns": ["scheduleId"]}]}, {"tableName": "tb_schedule_participant", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`scheduleId` TEXT NOT NULL, `partnerId` TEXT NOT NULL, `partnerType` INTEGER NOT NULL, `partnerName` TEXT, `nickName` TEXT, `avatar` TEXT, `inviteUserId` TEXT, PRIMARY KEY(`scheduleId`, `partnerId`, `partnerType`), FOREIGN KEY(`scheduleId`) REFERENCES `tb_schedule`(`scheduleId`) ON UPDATE NO ACTION ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED)", "fields": [{"fieldPath": "scheduleId", "columnName": "scheduleId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "partnerId", "columnName": "partnerId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "partnerType", "columnName": "partnerType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "partner<PERSON>ame", "columnName": "partner<PERSON>ame", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "inviteUserId", "columnName": "inviteUserId", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["scheduleId", "partnerId", "partnerType"]}, "indices": [], "foreignKeys": [{"table": "tb_schedule", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["scheduleId"], "referencedColumns": ["scheduleId"]}]}, {"tableName": "tb_ceo_email", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`threadId` INTEGER NOT NULL, `draft` TEXT, PRIMARY KEY(`threadId`))", "fields": [{"fieldPath": "threadId", "columnName": "threadId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "draft", "columnName": "draft", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["threadId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_search_record", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`searchKey` TEXT NOT NULL, `chatId` TEXT NOT NULL, `type` INTEGER NOT NULL, `modifyTime` INTEGER NOT NULL, PRIMARY KEY(`searchKey`, `chatId`, `type`))", "fields": [{"fieldPath": "search<PERSON>ey", "columnName": "search<PERSON>ey", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatId", "columnName": "chatId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifyTime", "columnName": "modifyTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["search<PERSON>ey", "chatId", "type"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_renewal_progress", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`tag` TEXT NOT NULL, `url` TEXT, `folder` TEXT, `fileName` TEXT, `totalSize` INTEGER NOT NULL, `currentSize` INTEGER NOT NULL, `status` INTEGER NOT NULL, PRIMARY KEY(`tag`))", "fields": [{"fieldPath": "tag", "columnName": "tag", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "folder", "columnName": "folder", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fileName", "columnName": "fileName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "totalSize", "columnName": "totalSize", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currentSize", "columnName": "currentSize", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["tag"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_task_comment_draft", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`taskId` TEXT NOT NULL, `draft` TEXT, `picUrls` TEXT, `fileUrls` TEXT, `modifyTime` INTEGER NOT NULL, PRIMARY KEY(`taskId`))", "fields": [{"fieldPath": "taskId", "columnName": "taskId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "draft", "columnName": "draft", "affinity": "TEXT", "notNull": false}, {"fieldPath": "picUrls", "columnName": "picUrls", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fileUrls", "columnName": "fileUrls", "affinity": "TEXT", "notNull": false}, {"fieldPath": "modifyTime", "columnName": "modifyTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["taskId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_work_plugin_tab", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`appId` TEXT NOT NULL, `name` TEXT NOT NULL, `icon` TEXT NOT NULL, `num` INTEGER NOT NULL, `description` TEXT NOT NULL DEFAULT '', PRIMARY KEY(`appId`))", "fields": [{"fieldPath": "appId", "columnName": "appId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "num", "columnName": "num", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}], "primaryKey": {"autoGenerate": false, "columnNames": ["appId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_work_plugin_group", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`pluginGroupId` TEXT NOT NULL, `pluginGroupName` TEXT NOT NULL, `groupSort` INTEGER NOT NULL, PRIMARY KEY(`pluginGroupId`))", "fields": [{"fieldPath": "pluginGroupId", "columnName": "pluginGroupId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pluginGroupName", "columnName": "pluginGroupName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "groupSort", "columnName": "groupSort", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["pluginGroupId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tb_work_plugin_group_relation", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`relationPluginGroupId` TEXT NOT NULL, `relationAppId` TEXT NOT NULL, `relationSort` INTEGER NOT NULL, PRIMARY KEY(`relationPluginGroupId`, `relationAppId`))", "fields": [{"fieldPath": "relationPluginGroupId", "columnName": "relationPluginGroupId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "relationAppId", "columnName": "relationAppId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "relationSort", "columnName": "relationSort", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["relationPluginGroupId", "relationAppId"]}, "indices": [{"name": "index_tb_work_plugin_group_relation_relationPluginGroupId", "unique": false, "columnNames": ["relationPluginGroupId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_tb_work_plugin_group_relation_relationPluginGroupId` ON `${TABLE_NAME}` (`relationPluginGroupId`)"}, {"name": "index_tb_work_plugin_group_relation_relationAppId", "unique": false, "columnNames": ["relationAppId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_tb_work_plugin_group_relation_relationAppId` ON `${TABLE_NAME}` (`relationAppId`)"}], "foreignKeys": []}, {"tableName": "tb_slash_command", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `appId` TEXT, `name` TEXT, `desc` TEXT, `tips` TEXT, `robotId` TEXT, `createTime` TEXT, `status` INTEGER, `type` INTEGER, `triggerType` INTEGER, `icon` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "appId", "columnName": "appId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "desc", "columnName": "desc", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tips", "columnName": "tips", "affinity": "TEXT", "notNull": false}, {"fieldPath": "robotId", "columnName": "robotId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "triggerType", "columnName": "triggerType", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [{"viewName": "view_conversation", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT * FROM tb_conversation c INNER JOIN tb_conversation_receipt r ON c.chatId = r.chatId AND c.type = r.type"}, {"viewName": "view_message", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT m.*, s.status FROM tb_message m LEFT JOIN tb_message_status s ON m.mid = s.mid"}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'da1810c734ba4b96e005b7dd6c1ee0c5')"]}}