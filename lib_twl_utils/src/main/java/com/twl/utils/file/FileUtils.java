package com.twl.utils.file;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.media.MediaPlayer;
import android.media.ThumbnailUtils;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.webkit.MimeTypeMap;

import com.techwolf.lib.tlog.TLog;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.util.HashMap;
import java.util.Locale;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import okio.BufferedSink;
import okio.BufferedSource;
import okio.Okio;
import okio.Sink;
import okio.Source;

/**
 * Created by wangtian on 2017/8/28.
 */

public class FileUtils {

    private static final String TAG = "FileUtils";

    public static final String KEY_TEMP_FILE_PATH = "tempFilePath"; // 临时文件目录

    /**
     * 读取出文件内容
     *
     * @param file
     *
     * @return
     */
    public static String read(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return "";
        }
        String result = "";
        BufferedSource bufferedSource = null;
        try {
            Source source = Okio.source(file);
            bufferedSource = Okio.buffer(source);
            result = bufferedSource.readUtf8();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            closeCloseable(bufferedSource);
        }

        return result;
    }

    /**
     * 字符串写入文件
     *
     * @param file
     * @param content
     *
     * @throws IOException
     */
    public static void write(File file, String content) throws IOException {
        if (file == null) {
            return;
        }
        if (!file.exists()) {
            File parent = file.getParentFile();
            if (!parent.exists()) {
                parent.mkdirs();
            }
            file.createNewFile();
        }
        Sink sink;
        BufferedSink bufferedSink = null;

        try {
            sink = Okio.sink(file);
            bufferedSink = Okio.buffer(sink);
            bufferedSink.writeUtf8(content);

            bufferedSink.flush();
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            closeCloseable(bufferedSink);
        }
    }

    /**
     * 数据写入文件。
     *
     * @param file
     * @param content
     *
     * @throws IOException
     */
    public static void write(File file, byte[] content) throws IOException {
        if (file == null) {
            return;
        }
        if (!file.exists()) {
            File parent = file.getParentFile();
            if (!parent.exists()) {
                parent.mkdirs();
            }
            file.createNewFile();
        }
        Sink sink;
        BufferedSink bufferedSink = null;

        try {
            sink = Okio.sink(file);
            bufferedSink = Okio.buffer(sink);
            bufferedSink.write(content);
            bufferedSink.flush();
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            closeCloseable(bufferedSink);
        }
    }

    /**
     * 获取指定文件流数据。
     *
     * @param file
     *
     * @return
     */
    public static byte[] getFileData(File file) {
        byte[] data = null;
        if (file == null) {
            return new byte[0];
        }

        data = new byte[(int) file.length()];

        FileInputStream fileInputStream = null;
        try {

            int len = 0;
            int sum = 0;
            fileInputStream = new FileInputStream(file);
            byte[] buffer = new byte[1024 * 2];
            while ((len = fileInputStream.read(buffer)) != -1) {
                System.arraycopy(buffer, 0, data, sum, len);
                sum += len;
            }

            return data;
        } catch (IOException e) {
            Log.e(TAG, e.getMessage());
        } finally {
            closeCloseable(fileInputStream);
        }

        return new byte[0];
    }

    /**
     * 优先获取外部存储目录，如果被挂起或移除状态，则获取内部存储目录。
     * 注：这个方法不需要EXTERNAL_STORAGE权限
     * @param context
     * @return
     */
    public static File getCacheDir(Context context) {
        File mCacheFile = null;
        if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())
                || !Environment.isExternalStorageRemovable()) {
            mCacheFile = context.getExternalCacheDir();
        }
        if (mCacheFile == null) {
            mCacheFile = context.getCacheDir();
        }
        return mCacheFile;
    }

    public static File getTempFile(File source) throws RuntimeException {
        File file = createTempFile(source);
        if (copyFile(source, file)) {
            return file;
        }
        return null;
    }

    public static File createTempFile(File source) {
        File tempFile;
        try {
            String name = source.getName();
            int index = name.lastIndexOf(".");
            String prefix = index < 0 ? name : name.substring(0, index) + "_";
            String suffix = index < 0 ? null : name.substring(index);
            File directory = source.getParentFile();
            tempFile = File.createTempFile(prefix, suffix, directory);
        } catch (Exception e) {
            TLog.error(TAG, e, "临时文件创建失败");
            return null;
        }
        return tempFile;
    }

    /**
     * 根据传入的文件夹创建对应链路上的所有文件夹
     * @param dirFile
     * @return
     */
    public static boolean createFileDir(File dirFile) {
        if (dirFile == null) {
            return false;
        } else if (dirFile.exists() && dirFile.isDirectory()) {
            return true;
        }

        File parentFile = dirFile.getParentFile();
        if (parentFile != null && !parentFile.exists()) {
            return createFileDir(parentFile) && createFileDir(dirFile);
        } else {
            boolean mkdirs = dirFile.mkdirs();
            boolean isSuccess = mkdirs || dirFile.exists();
            TLog.error(TAG, "create file dir fail: " + dirFile);
            return isSuccess;
        }
    }

    public static boolean copyFile(File source, File dest) throws RuntimeException {
        boolean ret = false;
        try {
            if (checkOnCopyFile(source, dest)) {
                Source s = null;
                BufferedSink bufferedSink = null;
                try {
                    s = Okio.source(source);
                    bufferedSink = Okio.buffer(Okio.sink(dest));
                    bufferedSink.writeAll(s);
                    bufferedSink.flush();
                    ret = true;
                } catch (IOException e) {
                    throw e;
                } finally {
                    closeCloseable(s);
                    closeCloseable(bufferedSink);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ret;
    }

    public static boolean copyFile(InputStream source, File dest) throws RuntimeException {
        boolean ret = false;
        try {
            Source s = null;
            BufferedSink bufferedSink = null;
            try {
                s = Okio.source(source);
                bufferedSink = Okio.buffer(Okio.sink(dest));
                bufferedSink.writeAll(s);
                bufferedSink.flush();
                ret = true;
            } catch (IOException e) {
                throw e;
            } finally {
                closeCloseable(s);
                closeCloseable(bufferedSink);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ret;
    }

    private static boolean checkOnCopyFile(File source, File dest) {
        if (source == null || dest == null
                || !source.isFile() || !source.exists()) {
            return false;
        }
        return true;
    }

    public static void closeCloseable(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void delete(String path, boolean ignoreDir) {
        if (path == null) {
            return;
        }
        File file = new File(path);
        delete(file, ignoreDir);
    }

    public static void delete(File file, boolean ignoreDir) {
        try {
            if (file == null || !file.exists()) {
                return;
            }
            if (file.isFile()) {
                file.delete();
                return;
            }

            File[] fileList = file.listFiles();
            if (fileList == null) {
                return;
            }

            for (File f : fileList) {
                delete(f.getAbsolutePath(), ignoreDir);
            }
            // delete the folder if need.
            if (!ignoreDir) {
                file.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //获取视频缩略图
    public static String getVideoThumbnail(Context context, String videoPath, int[] info) {
        // 强制使用内部缓存目录，避免Android 10+分区存储权限问题
        File cacheDir = context.getCacheDir();
        if (cacheDir != null) {

            final String absoluteCachePath = cacheDir.getAbsolutePath() + "/videoThumbnails";
            File folder = new File(absoluteCachePath);
            if (!folder.exists()) { // 创建视频缩略图目录，如果目录不存在
                folder.mkdirs();
            }

            String filename = new File(videoPath).getName();
            File file = new File(absoluteCachePath, filename + ".jpg");
            if (file.exists()) { // 如果有同名文件，直接删除
                //noinspection ResultOfMethodCallIgnored
                file.delete(); // 删除已有文件
                file = new File(absoluteCachePath, filename + ".jpg");
            }

            try {
                Bitmap bitmap = ThumbnailUtils.createVideoThumbnail(videoPath, MediaStore.Video.Thumbnails.FULL_SCREEN_KIND);
                if (bitmap != null) {
                    OutputStream outStream = new FileOutputStream(file);
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outStream);
                    if (info != null && info.length >= 2) {
                        info[0] = bitmap.getWidth();
                        info[1] = bitmap.getHeight();
                    }
                    outStream.flush();
                    outStream.close();
                }
            } catch (Exception e) {
                TLog.error(TAG, "getVideoThumbnail error:" + e.getMessage());
                e.printStackTrace();
            }
            return file.toString();

        }
        return null;
    }

    //获取视频时长,单位秒
    public static int getVideoDuration(Context context, Uri uri) {
        try {
            Cursor cursor = context.getContentResolver().query(uri, null, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                return cursor.getInt(cursor.getColumnIndex(MediaStore.Video.VideoColumns.DURATION)) / 1000;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    //获取视频时长,单位秒
    public static int getVideoDuration(String url) {
        MediaPlayer mediaPlayer = new MediaPlayer();
        int duration = 0;
        try {
            mediaPlayer.setDataSource(url);
            mediaPlayer.prepare();
            duration = mediaPlayer.getDuration();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return duration / 1000;
    }

    /**
     * 删除该文件或者该文件夹下的文件，不递归
     *
     * @param file
     */
    public static void delete(File file) {
        try {
            if (file == null || !file.exists()) {
                return;
            }
            if (file.isFile()) {
                file.delete();
                return;
            }

            File[] fileList = file.listFiles();
            if (fileList == null) {
                return;
            }

            for (File f : fileList) {
                if (f.isFile()) {
                    f.delete();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 保存方法
     */
    public static File saveBitmapToFile(Bitmap bitmap, String path) {
        File f = new File(path);
        if (f.exists()) {
            f.delete();
        }
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(f);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out);
            return f;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    public static String getFileSuffix(File file) {
        String end = file.getName().substring(file.getName().lastIndexOf(".") + 1, file.getName().length()).toLowerCase(Locale.ROOT);
        return end;
    }

    public static ContentValues getVideoContentValues(File paramFile, long paramLong) {
        ContentValues localContentValues = new ContentValues();
        localContentValues.put("title", paramFile.getName());
        localContentValues.put("_display_name", paramFile.getName());
        localContentValues.put("mime_type", "video/" + getFileSuffix(paramFile));
        localContentValues.put("datetaken", Long.valueOf(paramLong));
        localContentValues.put("date_modified", Long.valueOf(paramLong));
        localContentValues.put("date_added", Long.valueOf(paramLong));
        localContentValues.put("_data", paramFile.getAbsolutePath());
        localContentValues.put("_size", Long.valueOf(paramFile.length()));
        return localContentValues;
    }

    public static boolean createFolder(String folderPath) {
        if (!TextUtils.isEmpty(folderPath)) {
            File folder = new File(folderPath);
            return createFolder(folder);
        }
        return false;
    }

    public static boolean createFolder(File targetFolder) {
        if (targetFolder.exists()) {
            if (targetFolder.isDirectory()) {
                return true;
            }
            //noinspection ResultOfMethodCallIgnored
            targetFolder.delete();
        }
        return targetFolder.mkdirs();
    }

    /**
     * @param srcFile
     * @param index       从0开始
     * @param splitLength
     * @param tmpFolder
     *
     * @return
     */
    public static File splitFile(File srcFile, long index, long splitLength, String tmpFolder) {
        RandomAccessFile raf = null;
        RandomAccessFile out = null;
        File tempFile = new File(tmpFolder, srcFile.getName().split("\\.")[0] + ".tmp");
        if (tempFile.exists()) {
            tempFile.delete();
        }
        createFolder(tmpFolder);
        try {
            raf = new RandomAccessFile(srcFile, "r");
            long offSet = splitLength * index;
            long end = Math.min(splitLength * (index + 1) - 1, srcFile.length() - 1);
            out = new RandomAccessFile(tempFile, "rw");
            byte[] buffer = new byte[1024 * 4];
            int len;
            raf.seek(offSet);
            while (raf.getFilePointer() <= end && (len = raf.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (raf != null) {
                    raf.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        return tempFile;
    }

    public static File splitFile(File srcFile, String tempFileName, long index, long splitLength, String tmpFolder) {
        if (TextUtils.isEmpty(tempFileName)) {
            tempFileName = String.valueOf(System.currentTimeMillis());
        }
        RandomAccessFile raf = null;
        RandomAccessFile out = null;
        File tempFile = new File(tmpFolder, tempFileName + ".tmp");
        if (tempFile.exists()) {
            tempFile.delete();
        }
        createFolder(tmpFolder);
        try {
            raf = new RandomAccessFile(srcFile, "r");
            long offSet = splitLength * index;
            long end = Math.min(splitLength * (index + 1) - 1, srcFile.length() - 1);
            out = new RandomAccessFile(tempFile, "rw");
            byte[] buffer = new byte[1024 * 4];
            int len;
            raf.seek(offSet);
            while (raf.getFilePointer() <= end && (len = raf.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (raf != null) {
                    raf.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        return tempFile;
    }

    public static long getTotalSizeForFileDir(File file) {
        long totalSize = 0;
        try {
            if (file.exists()) {
                File[] listFiles = file.listFiles();
                if (listFiles == null) {
                    totalSize = file.length();
                } else {
                    long size;
                    for (File file2 : listFiles) {
                        if (file2.isDirectory()) {
                            size = getTotalSizeForFileDir(file2);
                        } else {
                            size = file2.length();
                        }
                        totalSize += size;
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return totalSize;
    }

    /**
     * 获取图片类型
     *
     * @param filePath
     * @return
     */
    public static String getFileType(String filePath) {
        HashMap<String, String> fileTypes = new HashMap<String, String>();
        fileTypes.put("FFD8FF", "jpg");
        fileTypes.put("89504E47", "png");
        fileTypes.put("47494638", "gif");
        fileTypes.put("49492A00", "tif");
        fileTypes.put("424D", "bmp");
        return fileTypes.get(getFileHeader(filePath));
    }

    public static String getMimeType(Context context, String filePath) {
        String mimeType = "";
        try {
            Uri uri = Uri.fromFile(new File(filePath));
            if (ContentResolver.SCHEME_CONTENT.equals(uri.getScheme())) {
                ContentResolver cr = context.getContentResolver();
                mimeType = cr.getType(uri);
            } else {
                String fileExtension = MimeTypeMap.getFileExtensionFromUrl(uri
                        .toString());
                mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(
                        fileExtension.toLowerCase());
                if (mimeType == null && filePath.contains("?")) {
                    String ext = filePath.substring(filePath.lastIndexOf(".") + 1).toLowerCase();
                    if (ext.contains("?")) {
                        mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(
                                ext.split("\\?")[0]);
                    }
                }
            }
        } catch (Exception e) {
            TLog.info(TAG, "FileUtils -> getFileExtensionFromUrl " + e.getMessage());
        }

        try {
            if (TextUtils.isEmpty(mimeType)) {
                String extension = getFileHeader(filePath);
                mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension);
            }
        } catch (Exception e) {
            TLog.info(TAG, "FileUtils -> getMimeTypeFromExtension " + e.getMessage());
        }
        if (TextUtils.isEmpty(mimeType)) {
            mimeType = "";
        }
        return mimeType;
    }

    /**
     * 获取文件头信息
     *
     * @param filePath
     * @return
     */
    public static String getFileHeader(String filePath) {
        FileInputStream is = null;
        String value = null;
        try {
            is = new FileInputStream(filePath);
            byte[] b = new byte[3];
            is.read(b, 0, b.length);
            value = bytesToHexString(b);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != is) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return value;
    }

    /**
     * 将byte字节转换为十六进制字符串
     *
     * @param src
     * @return
     */
    private static String bytesToHexString(byte[] src) {
        StringBuilder builder = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        String hv;
        for (int i = 0; i < src.length; i++) {
            hv = Integer.toHexString(src[i] & 0xFF).toUpperCase();
            if (hv.length() < 2) {
                builder.append(0);
            }
            builder.append(hv);
        }
        return builder.toString();
    }

    /**
     * 文件夹所有文件压缩为一个zip文件
     *
     * @param zipFilePath       要生成的zip文件路径
     * @param zipFileName       要生成的zip文件名称
     * @param inputFile         生成zip文件的文件夹/文件来源
     */
    public static String zip(String zipFilePath, String zipFileName, File inputFile) {
        ZipOutputStream out = null;
        BufferedOutputStream bos = null;
        try {
            if (TextUtils.isEmpty(zipFileName) || TextUtils.isEmpty(zipFilePath)) {
                return "";
            }
            File zipFile = new File(zipFilePath, zipFileName);
            if (zipFile.exists()) {
                zipFile.delete();
            }
            zipFile.createNewFile();

            out = new ZipOutputStream(new FileOutputStream(zipFile));
            bos = new BufferedOutputStream(out);
            zip(out, inputFile, inputFile.getName(), bos);

            return zipFile.getAbsolutePath();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (bos != null) {
                    bos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 压缩实现
     * @param out
     * @param f
     * @param base
     * @param bo
     */
    private static void zip(ZipOutputStream out, File f, String base,
                            BufferedOutputStream bo) { // 方法重载
        FileInputStream in = null;
        BufferedInputStream bis = null;
        try {
            if (f.isDirectory()) {
                File[] fl = f.listFiles();
                if (fl.length == 0) {
                    out.putNextEntry(new ZipEntry(base + "/")); // 创建zip压缩进入点base
                }
                for (File file : fl) {
                    zip(out, file, base + "/" + file.getName(), bo); // 递归遍历子文件夹
                }
            } else {
                out.putNextEntry(new ZipEntry(base)); // 创建zip压缩进入点base
                in = new FileInputStream(f);
                bis = new BufferedInputStream(in);
                int len = 0;
                byte[] buffer = new byte[1024];
                while ((len = bis.read(buffer)) != -1) {
                    bo.write(buffer, 0, len); // 将字节流写入当前zip目录
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
                if (bis != null) {
                    bis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 删除方法 这里只会删除某个文件夹下的文件，如果传入的directory是个文件，将不做处理 * * @param directory
     */
    public static void deleteDirFiles(File root) {
        if (root.isDirectory()) {
            File[] list = root.listFiles();
            if (list != null) {
                for (File file : list) {
                    deleteDirFiles(file);
                }
            }
        }
        // 不删除 环境配置
        if (root.getAbsolutePath().endsWith("debug_config") || root.getAbsolutePath().endsWith("debug_config.crc")) {
            return;
        }
        if (!root.delete()) {
            Log.d(TAG, "文件删除失败：" + root.getAbsolutePath());
        }
    }

    /**
     * 解析文件格式
     */
    public static String getFileFormat(String fileNameOrUrl) {
        if (TextUtils.isEmpty(fileNameOrUrl) || !fileNameOrUrl.contains(".")) {
            return "";
        }
        return fileNameOrUrl.substring(fileNameOrUrl.lastIndexOf(".") + 1);
    }
}
