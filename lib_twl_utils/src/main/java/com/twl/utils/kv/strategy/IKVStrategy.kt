package com.twl.utils.kv.strategy

import android.content.SharedPreferences

/**
 * Created by Xuweixiang on 2022/4/13 20:22
 * email : <EMAIL>
 * describe :
 */
interface IKVStrategy {

    fun putString(k: String, v: String): Boolean

    fun getString(k: String, defaultValue: String? = ""): String

    fun getStringSet(k: String, defaultValue: Set<String> = emptySet()): Set<String>

    fun putInt(k: String, v: Int): Boolean

    fun getInt(k: String, defaultValue: Int? = 0): Int

    fun putFloat(k: String, v: Float): Boolean

    fun getFloat(k: String, defaultValue: Float? = 0f): Float

    fun putDouble(k: String, v: Double): Boolean

    fun getDouble(k: String, defaultValue: Double? = 0.0): Double

    fun putLong(k: String, v: Long): Boolean

    fun getLong(k: String, defaultValue: Long? = 0L): Long

    fun putBoolean(k: String, v: Boolean): Boolean

    fun getBoolean(k: String, defaultValue: Boolean? = false): Boolean

    fun edit() : SharedPreferences.Editor

    fun remove(k: String) : Boolean

    fun contains(k: String): Boolean

    fun getAllKeys(): Array<String>
}