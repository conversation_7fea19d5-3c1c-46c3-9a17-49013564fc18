package com.twl.utils.kv

import com.tencent.mmkv.MMKV
import com.twl.utils.kv.KV.STRATEGY_TYPE_MMKV
import com.twl.utils.kv.strategy.BHKV
import com.twl.utils.kv.strategy.IKVStrategy

/**
 * Created by Xuweixiang on 2022/4/18 16:27
 * email : <EMAIL>
 * describe :
 */
internal const val KV_CONFIG_NAME = "kv_config"

// kv 策略：sp / mmkv
internal const val KV_CONFIG_STRATEGY = "kv_config_strategy"

// kv 写入策略：当 kv 策略为 mmkv 时，是否支持同时写入 sp
internal const val KV_CONFIG_WRITE_STRATEGY = "kv_config_write_strategy"
const val KV_CONFIG_WRITE_STRATEGY_SINGLE = 1
const val KV_CONFIG_WRITE_STRATEGY_BOTH = 0

class KVConfig {

    private val configKV: IKVStrategy by lazy {
        BHKV(MMKV.mmkvWithID(KV_CONFIG_NAME))
    }

    fun isMMKVStrategy(): Boolean {
        return getStrategy() == STRATEGY_TYPE_MMKV
    }

    fun isMMKVWriteBoth(): Boolean {
        return getWriteBothStrategy() == KV_CONFIG_WRITE_STRATEGY_BOTH
    }

    fun getStrategy(): Int {
        return configKV.getInt(KV_CONFIG_STRATEGY, KV.STRATEGY_TYPE_MMKV)
    }

    private fun getWriteBothStrategy(): Int {
        return configKV.getInt(KV_CONFIG_WRITE_STRATEGY, KV_CONFIG_WRITE_STRATEGY_BOTH)
    }

    fun updateStrategy(strategy: Int): Boolean {
        return configKV.putInt(KV_CONFIG_STRATEGY, strategy)
    }

    fun updateWriteStrategy(writeStrategy: Int): Boolean {
        return configKV.putInt(KV_CONFIG_WRITE_STRATEGY, writeStrategy)
    }
}