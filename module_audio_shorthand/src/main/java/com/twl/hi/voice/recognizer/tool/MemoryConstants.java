package com.twl.hi.voice.recognizer.tool;


import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * <pre>
 *     author : Wp
 *     e-mail : <EMAIL>
 *     time   : 2022/9/15 7:32 下午
 *     desc   : 单位 ConvertUtils使用
 *     version: v1013
 * </pre>
 */
public final class MemoryConstants {

    public static final int BYTE = 1;
    public static final int KB   = 1024;
    public static final int MB   = 1048576;
    public static final int GB   = 1073741824;

    @IntDef({BYTE, KB, MB, GB})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Unit {
    }
}
