package com.twl.hi.workflow.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.workflow.api.response.TaskUpdateFieldResponse;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * 编辑任务自定义字段
 */
public class TaskUpdateFieldRequest extends BaseApiRequest<TaskUpdateFieldResponse> {

    @Expose
    public String taskId;
    @Expose
    public String fieldId;
    @Expose
    public String value;
    @Expose
    public String msgId;
    @Expose
    public String text;

    public TaskUpdateFieldRequest(BaseApiRequestCallback<TaskUpdateFieldResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_TASK_UPDATE_FIELD;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}

