package com.twl.hi.workflow.viewmodel;

import android.app.Application;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.twl.hi.foundation.base.FoundationViewModel;

/**
 * Created by ChaiJiangpeng on 2020/11/2
 * Describe:
 */
public class TaskEditContentViewModel extends FoundationViewModel {

    public ObservableField<String> mInput = new ObservableField<>();
    public ObservableBoolean mSaveEnable = new ObservableBoolean();

    public TaskEditContentViewModel(Application application) {
        super(application);
    }
}
