package com.twl.hi.workflow.adapter;

import android.view.View;

import com.twl.hi.workflow.R;
import com.twl.hi.workflow.api.response.bean.WorkFlowCustomBean;
import com.twl.hi.workflow.callback.TaskDetailEditCallback;
import com.twl.hi.workflow.databinding.WorkflowItemTaskDetailCustomBinding;

import lib.twl.common.adapter.BaseDataBindingAdapter;
import lib.twl.common.adapter.BaseDataBindingViewHolder;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>gpeng on 2020/12/11
 * Describe: 任务详情自定义字段adapter
 */
public class TaskDetailCustomAdapter extends BaseDataBindingAdapter<WorkFlowCustomBean, WorkflowItemTaskDetailCustomBinding> {

    private TaskDetailEditCallback callback; //是不是详情页使用 用来判断权限

    public TaskDetailCustomAdapter() {
        super(R.layout.workflow_item_task_detail_custom, null);
    }

    public void setCallback(TaskDetailEditCallback callback) {
        this.callback = callback;
    }

    @Override
    protected void bind(BaseDataBindingViewHolder<WorkflowItemTaskDetailCustomBinding> helper, WorkflowItemTaskDetailCustomBinding binding, WorkFlowCustomBean item) {
        binding.setItem(item);
        if (callback != null) {
            binding.ivGo.setVisibility(callback.isFieldHaveAuth(item) ? View.VISIBLE : View.GONE);
        }
    }
}
