package com.twl.hi.workflow;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.lifecycle.Observer;

import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.export.select.bean.SelectBaseParams;
import com.twl.hi.export.select.bean.SelectContactsParams;
import com.twl.hi.export.select.router.SelectPageRouter;
import com.twl.hi.workflow.api.response.bean.TaskListBean;
import com.twl.hi.workflow.callback.SubTaskCreateCallback;
import com.twl.hi.workflow.databinding.WorkflowActivitySubTaskCreateBinding;
import com.twl.hi.workflow.viewmodel.SubTaskCreateViewModel;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

import hi.kernel.BundleConstants;
import hi.kernel.RequestCodeConstants;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.LList;

/**
 * 子任务创建页面
 */
public class SubTaskCreateActivity extends FoundationVMActivity<WorkflowActivitySubTaskCreateBinding, SubTaskCreateViewModel> implements SubTaskCreateCallback {

    public static Intent createIntent(Context context, String parentTaskId) {
        Intent intent = new Intent(context, SubTaskCreateActivity.class);
        intent.putExtra("parentTaskId", parentTaskId);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
        initData();
    }

    private void initData() {
        getViewModel().getCreateLiveData().observe(this, new Observer<TaskListBean>() {
            @Override
            public void onChanged(TaskListBean taskListBean) {
                if (taskListBean != null) {
                    Intent intent = new Intent();
                    intent.putExtra("data", taskListBean);
                    setResult(Activity.RESULT_OK, intent);
                    AppUtil.finishActivity(SubTaskCreateActivity.this);
                }
            }
        });
    }

    private void initView() {
        String parentTaskId = getIntent().getStringExtra("parentTaskId");
        getViewModel().setParentTaskId(parentTaskId);
        getDataBinding().icExecutor.clContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                chooseExecutor();
            }
        });
        getDataBinding().icDate.clContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                chooseDate();
            }
        });
    }

    private void chooseDate() {
        AppUtil.startActivityForResult(this, TaskTargetDateSetActivity.createIntent(this, getViewModel().targetDate,
                getViewModel().targetTime, getViewModel().startDate, getViewModel().startTime,""), RequestCodeConstants.REQUEST_CODE_TASK_EDIT_DATE);
    }


    private void setTaskDateUI(String targetDate, String targetTime, String startDate, String startTime) {
        getDataBinding().icDate.tvContent.setText(getViewModel().setShowDate(targetDate, targetTime, startDate, startTime));
    }

    private void chooseExecutor() {
        List<String> addIds = new ArrayList<>();
        String executorId = getViewModel().executorId.get();
        if (StringUtils.isNotEmpty(executorId)) {
            addIds.add(executorId);
        }
        SelectContactsParams params = new SelectContactsParams()
                .setSearchContactsAllVisible(SelectBaseParams.VISIBLE)
                .<SelectContactsParams>setMultiSelect(true)
                .<SelectContactsParams>setMaxSelectCount(1)
                .setCanChooseNone(true)
                .setAddIds(addIds)
                .<SelectContactsParams>setTitle(getString(R.string.select_contact))
                .<SelectContactsParams>setOrgVisible(SelectBaseParams.VISIBLE);
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        AppUtil.startUriForResult(this, SelectPageRouter.SELECT_CONTACT_ACTIVITY, RequestCodeConstants.REQUEST_CODE_SELECT_CONTACT_EXECUTOR, bundle, ActivityAnimType.UP_GLIDE);
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {
        getViewModel().createSubTaskRequest();
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.workflow_activity_sub_task_create;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public void editContent() {
        Intent intent = TaskEditContentActivity.createIntent(this, TaskEditContentActivity.TYPE_TASK_EDIT_CONTENT, getViewModel().content.get());
        AppUtil.startActivityForResult(this, intent, RequestCodeConstants.REQUEST_CODE_TASK_EDIT_CONTENT);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data == null || resultCode != Activity.RESULT_OK) {
            return;
        }
        switch (requestCode) {
            case RequestCodeConstants.REQUEST_CODE_TASK_EDIT_CONTENT:
                String content = data.getStringExtra("content");
                getViewModel().content.set(content);
                if (!TextUtils.isEmpty(content)) {
                    getViewModel().saveEnable.set(true);
                } else {
                    getViewModel().saveEnable.set(false);
                }
                break;
            case RequestCodeConstants.REQUEST_CODE_SELECT_CONTACT_EXECUTOR:
                ArrayList<String> executorIds = (ArrayList<String>) data.getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
                if (!LList.isEmpty(executorIds)) {
                    String executorId = executorIds.get(0);
                    if (!TextUtils.equals(executorId, getViewModel().executorId.get())) {
                        getViewModel().executorId.set(executorId);
                    }
                } else {
                    getViewModel().executorId.set("");
                }
                break;
            case RequestCodeConstants.REQUEST_CODE_TASK_EDIT_DATE:
                String targetDate = data.getStringExtra("targetDate");
                String targetTime = data.getStringExtra("targetTime");
                String startDate = data.getStringExtra("startDate");
                String startTime = data.getStringExtra("startTime");
                getViewModel().targetDate = targetDate;
                getViewModel().targetTime = targetTime;
                getViewModel().startDate = startDate;
                getViewModel().startTime = startTime;
                setTaskDateUI(targetDate, targetTime, startDate, startTime);
                break;
            default:
                break;
        }
    }
}
