<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.basic.callback.TitleBarCallback" />

        <variable
            name="viewModel"
            type="com.twl.hi.workflow.viewmodel.TaskEditContentViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            app:right="@{@string/done}"
            app:rightEnabled="@{viewModel.mSaveEnable}"
            app:callback="@{callback}"
            app:hasDivider="@{false}"
            app:left='@{" "}' />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:id="@+id/et_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@={viewModel.mInput}"
                android:textColor="@color/color_212121"
                android:gravity="top"
                android:background="@null"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColorHint="@color/color_B1B1B8"
                android:textSize="16sp" />
        </ScrollView>
    </LinearLayout>
</layout>