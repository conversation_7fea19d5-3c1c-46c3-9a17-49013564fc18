<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <merge
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

        <TextView
            android:id="@+id/tv_tag_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:text="@string/workflow_task_tag"
            android:textColor="@color/color_0D0D1A"
            android:textSize="15sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0"
            tools:text="@string/workflow_task_tag" />

        <TextView
            android:id="@+id/tv_tag_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="11dp"
            android:text="@string/workflow_please_select"
            android:textColor="@color/color_B1B1B8"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_tag_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_tag_name" />

        <ImageView
            android:id="@+id/tv_tag_go"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/workflow_ic_icon_gray_arrow_right"
            app:layout_constraintBottom_toBottomOf="@+id/tv_tag_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_tag_name" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_tag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="50dp"
            android:layout_marginEnd="11dp"
            android:layout_marginBottom="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_tag_name" />
    </merge>
</layout>