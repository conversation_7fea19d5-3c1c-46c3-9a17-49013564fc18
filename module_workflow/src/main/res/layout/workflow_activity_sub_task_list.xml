<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.workflow.viewmodel.SubTaskListViewModel" />


        <variable
            name="callback"
            type="com.twl.hi.workflow.callback.SubTaskListCallback" />
    </data>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            app:title="@{@string/workflow_sub_task}"
            app:callback="@{callback}"
            app:hasDivider="@{false}" />

        <com.twl.hi.basic.views.CommonTextView
            android:id="@+id/tv_add"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_alignParentBottom="true"
            android:layout_margin="20dp"
            android:background="@color/color_5D68E8"
            android:text="@string/workflow_sub_task_add"
            android:textColor="@color/app_white"
            android:textSize="17sp"
            android:gravity="center"
            app:btn_roundRadius="7dp"
            android:onClick="@{v->callback.clickAddSubTask()}" />

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_F9F9FA"
            android:layout_below="@+id/title_bar"
            android:paddingLeft="20dp"
            android:paddingTop="18dp"
            android:paddingBottom="8dp"
            android:textSize="13sp"
            android:textColor="@color/color_9999A3"
            tools:text="共计2个子任务，已完成0个" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/tv_num"
            android:layout_above="@+id/tv_add" />
    </RelativeLayout>
</layout>